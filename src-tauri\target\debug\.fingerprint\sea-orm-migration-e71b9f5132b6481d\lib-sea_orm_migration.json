{"rustc": 16591470773350601817, "features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlx-postgres\"]", "declared_features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlite-use-returning-for-3_35\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 4990465507743545796, "profile": 2241668132362809309, "path": 17550927566082441788, "deps": [[291883781376595779, "sea_orm", false, 15606159707457889668], [3405707034081185165, "dotenvy", false, 4397146467665378413], [8606274917505247608, "tracing", false, 702195515399835879], [10686516203231854806, "sea_schema", false, 5711843054293346714], [11946729385090170470, "async_trait", false, 2389517705436373950], [12141064477774631573, "sea_orm_cli", false, 13914229155827373923], [16230660778393187092, "tracing_subscriber", false, 159690804789844003], [17612818546626403359, "clap", false, 13578590751634973034]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-migration-e71b9f5132b6481d\\dep-lib-sea_orm_migration", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}