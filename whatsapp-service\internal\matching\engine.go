package matching

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"time"

	"go.uber.org/zap"

	"medibridge-whatsapp/internal/database"
)

// MatchingEngine handles the logic for matching medicine requests with offers
type MatchingEngine struct {
	logger *zap.Logger
	db     *database.DB
}

// NewMatchingEngine creates a new matching engine
func NewMatchingEngine(logger *zap.Logger, db *database.DB) *MatchingEngine {
	return &MatchingEngine{
		logger: logger,
		db:     db,
	}
}

// MatchingCriteria represents the criteria used for matching
type MatchingCriteria struct {
	PriceMatch        *bool   `json:"price_match,omitempty"`
	QuantityMatch     *bool   `json:"quantity_match,omitempty"`
	LocationProximity *float64 `json:"location_proximity,omitempty"`
	TimeRelevance     float64 `json:"time_relevance"`
	UserReputation    *float64 `json:"user_reputation,omitempty"`
	NameSimilarity    float64 `json:"name_similarity"`
}

// Match represents a potential match between a request and an offer
type Match struct {
	RequestMessageID  int              `json:"request_message_id"`
	OfferMessageID    int              `json:"offer_message_id"`
	MedicineID        int              `json:"medicine_id"`
	ConfidenceScore   float64          `json:"confidence_score"`
	MatchingCriteria  MatchingCriteria `json:"matching_criteria"`
	RequestMessage    *database.Message `json:"request_message,omitempty"`
	OfferMessage      *database.Message `json:"offer_message,omitempty"`
}

// FindMatches finds potential matches for medicine requests and offers
func (me *MatchingEngine) FindMatches(ctx context.Context) error {
	me.logger.Info("Starting matching process")

	// Get unprocessed requests (messages with type 'request')
	requests, err := me.getUnprocessedRequests()
	if err != nil {
		return fmt.Errorf("failed to get requests: %w", err)
	}

	// Get available offers (messages with type 'offer')
	offers, err := me.getAvailableOffers()
	if err != nil {
		return fmt.Errorf("failed to get offers: %w", err)
	}

	me.logger.Info("Found messages for matching",
		zap.Int("requests", len(requests)),
		zap.Int("offers", len(offers)))

	// Find matches for each request
	for _, request := range requests {
		matches, err := me.findMatchesForRequest(request, offers)
		if err != nil {
			me.logger.Error("Failed to find matches for request",
				zap.Int("request_id", request.ID),
				zap.Error(err))
			continue
		}

		// Store the best matches (top 3)
		for i, match := range matches {
			if i >= 3 { // Limit to top 3 matches
				break
			}

			if err := me.storeMatch(match); err != nil {
				me.logger.Error("Failed to store match",
					zap.Int("request_id", match.RequestMessageID),
					zap.Int("offer_id", match.OfferMessageID),
					zap.Error(err))
			}
		}
	}

	me.logger.Info("Matching process completed")
	return nil
}

// getUnprocessedRequests gets messages that are requests and haven't been processed for matching
func (me *MatchingEngine) getUnprocessedRequests() ([]*database.Message, error) {
	query := `
		SELECT m.id, m.whatsapp_message_id, m.account_id, m.group_jid, m.group_name,
		       m.sender_jid, m.sender_name, m.content, m.message_type, m.processing_status,
		       m.ai_analysis, m.error_message, m.message_timestamp, m.received_at,
		       m.processed_at, m.created_at, m.updated_at
		FROM messages m
		WHERE m.message_type = 'request' 
		  AND m.processing_status = 'processed'
		  AND m.message_timestamp > NOW() - INTERVAL '7 days'
		  AND NOT EXISTS (
		      SELECT 1 FROM matches ma WHERE ma.request_message_id = m.id
		  )
		ORDER BY m.message_timestamp DESC
		LIMIT 50
	`

	rows, err := me.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var messages []*database.Message
	for rows.Next() {
		msg := &database.Message{}
		err := rows.Scan(
			&msg.ID, &msg.WhatsAppMessageID, &msg.AccountID, &msg.GroupJID, &msg.GroupName,
			&msg.SenderJID, &msg.SenderName, &msg.Content, &msg.MessageType, &msg.ProcessingStatus,
			&msg.AIAnalysis, &msg.ErrorMessage, &msg.MessageTimestamp, &msg.ReceivedAt,
			&msg.ProcessedAt, &msg.CreatedAt, &msg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		messages = append(messages, msg)
	}

	return messages, nil
}

// getAvailableOffers gets messages that are offers and are still relevant
func (me *MatchingEngine) getAvailableOffers() ([]*database.Message, error) {
	query := `
		SELECT m.id, m.whatsapp_message_id, m.account_id, m.group_jid, m.group_name,
		       m.sender_jid, m.sender_name, m.content, m.message_type, m.processing_status,
		       m.ai_analysis, m.error_message, m.message_timestamp, m.received_at,
		       m.processed_at, m.created_at, m.updated_at
		FROM messages m
		WHERE m.message_type = 'offer' 
		  AND m.processing_status = 'processed'
		  AND m.message_timestamp > NOW() - INTERVAL '14 days'
		ORDER BY m.message_timestamp DESC
		LIMIT 100
	`

	rows, err := me.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var messages []*database.Message
	for rows.Next() {
		msg := &database.Message{}
		err := rows.Scan(
			&msg.ID, &msg.WhatsAppMessageID, &msg.AccountID, &msg.GroupJID, &msg.GroupName,
			&msg.SenderJID, &msg.SenderName, &msg.Content, &msg.MessageType, &msg.ProcessingStatus,
			&msg.AIAnalysis, &msg.ErrorMessage, &msg.MessageTimestamp, &msg.ReceivedAt,
			&msg.ProcessedAt, &msg.CreatedAt, &msg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		messages = append(messages, msg)
	}

	return messages, nil
}

// findMatchesForRequest finds potential matches for a specific request
func (me *MatchingEngine) findMatchesForRequest(request *database.Message, offers []*database.Message) ([]Match, error) {
	// Get medicines mentioned in the request
	requestMedicines, err := me.getMessageMedicines(request.ID)
	if err != nil {
		return nil, err
	}

	var matches []Match

	// For each medicine in the request, find matching offers
	for _, reqMedicine := range requestMedicines {
		for _, offer := range offers {
			// Skip if same sender
			if request.SenderJID == offer.SenderJID {
				continue
			}

			// Get medicines mentioned in the offer
			offerMedicines, err := me.getMessageMedicines(offer.ID)
			if err != nil {
				me.logger.Error("Failed to get offer medicines",
					zap.Int("offer_id", offer.ID),
					zap.Error(err))
				continue
			}

			// Check if any offer medicine matches the request medicine
			for _, offerMedicine := range offerMedicines {
				if me.medicinesMatch(reqMedicine, offerMedicine) {
					match := me.calculateMatch(request, offer, reqMedicine, offerMedicine)
					if match.ConfidenceScore >= 0.5 { // Minimum confidence threshold
						matches = append(matches, match)
					}
				}
			}
		}
	}

	// Sort matches by confidence score (highest first)
	for i := 0; i < len(matches)-1; i++ {
		for j := i + 1; j < len(matches); j++ {
			if matches[i].ConfidenceScore < matches[j].ConfidenceScore {
				matches[i], matches[j] = matches[j], matches[i]
			}
		}
	}

	return matches, nil
}

// MessageMedicine represents a medicine mentioned in a message
type MessageMedicine struct {
	ID             int     `json:"id"`
	MessageID      int     `json:"message_id"`
	MedicineID     int     `json:"medicine_id"`
	MedicineName   string  `json:"medicine_name"`
	ConfidenceScore float64 `json:"confidence_score"`
	Quantity       *string `json:"quantity,omitempty"`
	Unit           *string `json:"unit,omitempty"`
	Price          *string `json:"price,omitempty"`
	Currency       *string `json:"currency,omitempty"`
}

// getMessageMedicines gets medicines mentioned in a specific message
func (me *MatchingEngine) getMessageMedicines(messageID int) ([]MessageMedicine, error) {
	query := `
		SELECT mm.id, mm.message_id, mm.medicine_id, m.name, mm.confidence_score,
		       mm.quantity, mm.unit, mm.price, mm.currency
		FROM message_medicines mm
		JOIN medicines m ON mm.medicine_id = m.id
		WHERE mm.message_id = $1
		ORDER BY mm.confidence_score DESC
	`

	rows, err := me.db.Query(query, messageID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var medicines []MessageMedicine
	for rows.Next() {
		med := MessageMedicine{}
		err := rows.Scan(
			&med.ID, &med.MessageID, &med.MedicineID, &med.MedicineName,
			&med.ConfidenceScore, &med.Quantity, &med.Unit, &med.Price, &med.Currency,
		)
		if err != nil {
			return nil, err
		}
		medicines = append(medicines, med)
	}

	return medicines, nil
}

// medicinesMatch checks if two medicines are the same or similar
func (me *MatchingEngine) medicinesMatch(req, offer MessageMedicine) bool {
	// Exact medicine ID match
	if req.MedicineID == offer.MedicineID {
		return true
	}

	// Name similarity check
	similarity := me.calculateNameSimilarity(req.MedicineName, offer.MedicineName)
	return similarity >= 0.8 // 80% similarity threshold
}

// calculateNameSimilarity calculates similarity between two medicine names
func (me *MatchingEngine) calculateNameSimilarity(name1, name2 string) float64 {
	// Simple similarity calculation based on common words
	words1 := strings.Fields(strings.ToLower(name1))
	words2 := strings.Fields(strings.ToLower(name2))

	if len(words1) == 0 || len(words2) == 0 {
		return 0.0
	}

	commonWords := 0
	for _, word1 := range words1 {
		for _, word2 := range words2 {
			if word1 == word2 {
				commonWords++
				break
			}
		}
	}

	maxWords := math.Max(float64(len(words1)), float64(len(words2)))
	return float64(commonWords) / maxWords
}

// calculateMatch calculates the match score and criteria
func (me *MatchingEngine) calculateMatch(request, offer *database.Message, reqMed, offerMed MessageMedicine) Match {
	criteria := MatchingCriteria{}

	// Calculate name similarity
	criteria.NameSimilarity = me.calculateNameSimilarity(reqMed.MedicineName, offerMed.MedicineName)

	// Calculate time relevance (newer messages get higher scores)
	timeDiff := offer.MessageTimestamp.Sub(request.MessageTimestamp)
	if timeDiff < 0 {
		timeDiff = -timeDiff
	}
	
	// Messages within 24 hours get full score, older messages get reduced score
	hours := timeDiff.Hours()
	if hours <= 24 {
		criteria.TimeRelevance = 1.0
	} else if hours <= 168 { // 7 days
		criteria.TimeRelevance = 1.0 - (hours-24)/(168-24)*0.5
	} else {
		criteria.TimeRelevance = 0.5
	}

	// Check price compatibility
	if reqMed.Price != nil && offerMed.Price != nil {
		priceMatch := true // Simple check - can be enhanced
		criteria.PriceMatch = &priceMatch
	}

	// Check quantity compatibility
	if reqMed.Quantity != nil && offerMed.Quantity != nil {
		quantityMatch := true // Simple check - can be enhanced
		criteria.QuantityMatch = &quantityMatch
	}

	// Calculate overall confidence score
	confidenceScore := criteria.NameSimilarity * 0.4 + // 40% weight on name similarity
		criteria.TimeRelevance*0.3 + // 30% weight on time relevance
		reqMed.ConfidenceScore*0.15 + // 15% weight on request AI confidence
		offerMed.ConfidenceScore*0.15 // 15% weight on offer AI confidence

	// Bonus for exact medicine match
	if reqMed.MedicineID == offerMed.MedicineID {
		confidenceScore += 0.1
	}

	// Bonus for price/quantity compatibility
	if criteria.PriceMatch != nil && *criteria.PriceMatch {
		confidenceScore += 0.05
	}
	if criteria.QuantityMatch != nil && *criteria.QuantityMatch {
		confidenceScore += 0.05
	}

	// Ensure score is between 0 and 1
	if confidenceScore > 1.0 {
		confidenceScore = 1.0
	}

	return Match{
		RequestMessageID: request.ID,
		OfferMessageID:   offer.ID,
		MedicineID:       reqMed.MedicineID,
		ConfidenceScore:  confidenceScore,
		MatchingCriteria: criteria,
		RequestMessage:   request,
		OfferMessage:     offer,
	}
}

// storeMatch stores a match in the database
func (me *MatchingEngine) storeMatch(match Match) error {
	// Check if match already exists
	var existingID int
	checkQuery := `
		SELECT id FROM matches 
		WHERE request_message_id = $1 AND offer_message_id = $2 AND medicine_id = $3
	`
	err := me.db.QueryRow(checkQuery, match.RequestMessageID, match.OfferMessageID, match.MedicineID).Scan(&existingID)
	if err == nil {
		// Match already exists
		return nil
	}

	// Convert criteria to JSON
	criteriaJSON, err := json.Marshal(match.MatchingCriteria)
	if err != nil {
		return fmt.Errorf("failed to marshal criteria: %w", err)
	}

	// Calculate expiry date (7 days from now)
	expiresAt := time.Now().Add(7 * 24 * time.Hour)

	insertQuery := `
		INSERT INTO matches (
			request_message_id, offer_message_id, medicine_id, confidence_score,
			status, matching_criteria, created_at, updated_at, expires_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	_, err = me.db.Exec(insertQuery,
		match.RequestMessageID,
		match.OfferMessageID,
		match.MedicineID,
		match.ConfidenceScore,
		"pending",
		string(criteriaJSON),
		time.Now(),
		time.Now(),
		expiresAt,
	)

	if err != nil {
		return fmt.Errorf("failed to insert match: %w", err)
	}

	me.logger.Info("Match stored successfully",
		zap.Int("request_id", match.RequestMessageID),
		zap.Int("offer_id", match.OfferMessageID),
		zap.Int("medicine_id", match.MedicineID),
		zap.Float64("confidence", match.ConfidenceScore))

	return nil
}

// CleanupExpiredMatches removes expired matches from the database
func (me *MatchingEngine) CleanupExpiredMatches() error {
	query := `
		UPDATE matches 
		SET status = 'expired', updated_at = NOW()
		WHERE expires_at < NOW() AND status IN ('pending', 'notified')
	`

	result, err := me.db.Exec(query)
	if err != nil {
		return err
	}

	rowsAffected, _ := result.RowsAffected()
	me.logger.Info("Expired matches cleaned up", zap.Int64("count", rowsAffected))

	return nil
}
