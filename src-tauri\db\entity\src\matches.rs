use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>Eq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "matches")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,

    /// Request message ID (someone asking for medicine)
    pub request_message_id: i32,

    /// Offer message ID (someone offering medicine)
    pub offer_message_id: i32,

    /// Medicine ID that matches
    pub medicine_id: i32,

    /// Match confidence score (0.0 to 1.0)
    pub confidence_score: f32,

    /// Match status: pending, notified, completed, expired
    pub status: String,

    /// Additional matching criteria (JSON)
    pub matching_criteria: Option<String>,

    /// Notes about the match
    pub notes: Option<String>,

    /// When the match was created
    pub created_at: DateTime,

    /// When the match was last updated
    pub updated_at: DateTime,

    /// When notifications were sent (if any)
    pub notified_at: Option<DateTime>,

    /// When the match was completed/resolved
    pub completed_at: Option<DateTime>,

    /// Match expiry date
    pub expires_at: Option<DateTime>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON><PERSON>, DeriveRela<PERSON>)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::messages::Entity",
        from = "Column::RequestMessageId",
        to = "super::messages::Column::Id"
    )]
    RequestMessage,

    #[sea_orm(
        belongs_to = "super::messages::Entity",
        from = "Column::OfferMessageId",
        to = "super::messages::Column::Id"
    )]
    OfferMessage,

    #[sea_orm(
        belongs_to = "super::medicines::Entity",
        from = "Column::MedicineId",
        to = "super::medicines::Column::Id"
    )]
    Medicine,
}

impl Related<super::medicines::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Medicine.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Match status enum
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MatchStatus {
    Pending,   // مطابقة جديدة لم يتم التعامل معها
    Notified,  // تم إرسال إشعارات للأطراف
    Completed, // تم إتمام الصفقة
    Expired,   // انتهت صلاحية المطابقة
    Cancelled, // تم إلغاء المطابقة
}

impl std::fmt::Display for MatchStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MatchStatus::Pending => write!(f, "pending"),
            MatchStatus::Notified => write!(f, "notified"),
            MatchStatus::Completed => write!(f, "completed"),
            MatchStatus::Expired => write!(f, "expired"),
            MatchStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

impl From<String> for MatchStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "pending" => MatchStatus::Pending,
            "notified" => MatchStatus::Notified,
            "completed" => MatchStatus::Completed,
            "expired" => MatchStatus::Expired,
            "cancelled" => MatchStatus::Cancelled,
            _ => MatchStatus::Pending,
        }
    }
}

// Matching criteria structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MatchingCriteria {
    /// Price compatibility
    pub price_match: Option<bool>,

    /// Quantity compatibility
    pub quantity_match: Option<bool>,

    /// Location proximity (if available)
    pub location_proximity: Option<f32>,

    /// Time relevance (how recent are the messages)
    pub time_relevance: f32,

    /// User reputation/history
    pub user_reputation: Option<f32>,

    /// Additional factors
    pub additional_factors: Option<serde_json::Value>,
}

impl Default for MatchingCriteria {
    fn default() -> Self {
        Self {
            price_match: None,
            quantity_match: None,
            location_proximity: None,
            time_relevance: 1.0,
            user_reputation: None,
            additional_factors: None,
        }
    }
}
