# WellTrack Pharmacy Application - Database Management Makefile
# Provides convenient commands for managing Sea-ORM database migrations in the Tauri Rust backend

# Default shell - Windows compatible
ifeq ($(OS),Windows_NT)
    SHELL := cmd.exe
    .SHELLFLAGS := /c
    RM := del /Q
    MKDIR := mkdir
    ENV_FILE := src-tauri\.env
    ENV_EXAMPLE := src-tauri\.env.example
    PWSH := powershell.exe -NoProfile -Command
else
    SHELL := /bin/bash
    RM := rm -f
    MKDIR := mkdir -p
    ENV_FILE := src-tauri/.env
    ENV_EXAMPLE := src-tauri/.env.example
    PWSH :=
endif

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
WHITE := \033[0;37m
RESET := \033[0m

# Project directories
TAURI_DIR := src-tauri
MIGRATION_DIR := $(TAURI_DIR)/db/migration
ENTITY_DIR := $(TAURI_DIR)/db/entity
SERVICE_DIR := $(TAURI_DIR)/db/service

# Load environment variables from src-tauri/.env if it exists
ifneq (,$(wildcard $(ENV_FILE)))
    include $(ENV_FILE)
    export
endif

# Database configuration with defaults
DB_NAME ?= welltrack_pharmacy
DB_USER ?= postgres
DB_PASSWORD ?= password
DB_HOST ?= localhost
DB_PORT ?= 5432
DATABASE_URL ?= postgres://$(DB_USER):$(DB_PASSWORD)@$(DB_HOST):$(DB_PORT)/$(DB_NAME)

# Environment detection
ENV ?= development
ifeq ($(ENV),production)
    DATABASE_URL := $(DATABASE_URL_PROD)
endif

# Migration binary
MIGRATION_BIN := cargo run --manifest-path $(MIGRATION_DIR)/Cargo.toml --bin migration --

.PHONY: help
help: ## Show this help message
	@echo "WellTrack Pharmacy - Database Management Commands"
	@echo ""
	@echo "Core Migration Commands:"
	@echo "  migrate-up           Run all pending migrations to update the database schema"
	@echo "  migrate-down         Rollback the last migration"
	@echo "  migrate-reset        Reset database by rolling back all migrations and re-applying them"
	@echo "  migrate-status       Show current migration status and which migrations have been applied"
	@echo "  migrate-fresh        Drop all tables and re-run all migrations from scratch"
	@echo "  migrate-generate     Generate a new migration file (usage: make migrate-generate NAME=create_table)"
	@echo ""
	@echo "Development Workflow Commands:"
	@echo "  db-setup             Initialize database and run all migrations (for first-time setup)"
	@echo "  db-seed              Run database seeders to populate with sample data"
	@echo "  db-clean             Clean up database and reset to fresh state"
	@echo "  db-backup            Create a backup of the current database"
	@echo "  db-restore           Restore database from backup file"
	@echo ""
	@echo "Utility Commands:"
	@echo "  env-setup            Create .env file from .env.example if it doesn't exist"
	@echo "  entity-generate      Generate Sea-ORM entities from current database schema"
	@echo "  dev-setup            Complete development environment setup"
	@echo "  test-db              Run database-related tests"
	@echo "  lint-db              Run linting on database-related code"
	@echo "  clean                Clean build artifacts"
	@echo "  check-env            Check if required environment variables are set"
	@echo ""
	@echo "Environment Variables:"
	@echo "  DATABASE_URL         Database connection string"
	@echo "  ENV                  Environment (development|production) (default: development)"
	@echo ""
	@echo "Examples:"
	@echo "  make migrate-up                    # Run all pending migrations"
	@echo "  make migrate-status                # Check migration status"
	@echo "  make db-setup                      # Initialize database for first time"
	@echo "  set ENV=production && make migrate-up     # Run migrations in production (Windows)"
	@echo "  ENV=production make migrate-up            # Run migrations in production (Unix)"

.PHONY: env-setup
env-setup: ## Create .env file from .env.example if it doesn't exist
ifeq ($(OS),Windows_NT)
	@if not exist "$(ENV_FILE)" ( \
		if exist "$(ENV_EXAMPLE)" ( \
			copy "$(ENV_EXAMPLE)" "$(ENV_FILE)" >nul && \
			echo Created $(ENV_FILE) from $(ENV_EXAMPLE) && \
			echo Please edit $(ENV_FILE) with your database credentials \
		) else ( \
			echo Error: $(ENV_EXAMPLE) not found && \
			exit /b 1 \
		) \
	) else ( \
		echo $(ENV_FILE) already exists \
	)
else
	@if [ ! -f "$(ENV_FILE)" ]; then \
		if [ -f "$(ENV_EXAMPLE)" ]; then \
			cp "$(ENV_EXAMPLE)" "$(ENV_FILE)"; \
			echo "Created $(ENV_FILE) from $(ENV_EXAMPLE)"; \
			echo "Please edit $(ENV_FILE) with your database credentials"; \
		else \
			echo "Error: $(ENV_EXAMPLE) not found"; \
			exit 1; \
		fi; \
	else \
		echo "$(ENV_FILE) already exists"; \
	fi
endif

.PHONY: check-env
check-env: ## Check if required environment variables are set
	@echo "Checking environment configuration..."
	@echo "Environment: $(ENV)"
	@echo "Database URL: $(DATABASE_URL)"
	@echo "Environment file: $(ENV_FILE)"
ifeq ($(OS),Windows_NT)
	@if "$(DATABASE_URL)"=="" ( \
		echo Error: DATABASE_URL is not set && \
		echo Please run make env-setup to create environment file && \
		exit /b 1 \
	)
else
	@if [ -z "$(DATABASE_URL)" ]; then \
		echo "Error: DATABASE_URL is not set"; \
		echo "Please run 'make env-setup' to create environment file"; \
		exit 1; \
	fi
endif

.PHONY: build-migration
build-migration: ## Build the migration binary
	@echo "$(BLUE)Building migration binary...$(RESET)"
	@cd $(MIGRATION_DIR) && cargo build --bin migration
	@echo "$(GREEN)Migration binary built successfully$(RESET)"

.PHONY: migrate-up
migrate-up: check-env build-migration ## Run all pending migrations to update the database schema
	@echo "Running pending migrations..."
ifeq ($(OS),Windows_NT)
	@set "DATABASE_URL=$(DATABASE_URL)" && cd $(MIGRATION_DIR) && cargo run --bin migration -- up
else
	@export DATABASE_URL=$(DATABASE_URL) && $(MIGRATION_BIN) up
endif
	@echo "Migrations completed successfully"

.PHONY: migrate-down
migrate-down: check-env build-migration ## Rollback the last migration
	@echo "$(YELLOW)Rolling back the last migration...$(RESET)"
	@read -p "Are you sure you want to rollback the last migration? [y/N] " -n 1 -r; \
	echo ""; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		export DATABASE_URL=$(DATABASE_URL) && $(MIGRATION_BIN) down; \
		echo "$(GREEN)Migration rollback completed$(RESET)"; \
	else \
		echo "$(YELLOW)Migration rollback cancelled$(RESET)"; \
	fi

.PHONY: migrate-reset
migrate-reset: check-env build-migration ## Reset the database by rolling back all migrations and re-applying them
	@echo "$(RED)Resetting database (rollback all migrations and re-apply)...$(RESET)"
	@read -p "This will reset ALL data in the database. Are you sure? [y/N] " -n 1 -r; \
	echo ""; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo "$(BLUE)Rolling back all migrations...$(RESET)"; \
		export DATABASE_URL=$(DATABASE_URL) && $(MIGRATION_BIN) reset; \
		echo "$(BLUE)Re-applying all migrations...$(RESET)"; \
		export DATABASE_URL=$(DATABASE_URL) && $(MIGRATION_BIN) up; \
		echo "$(GREEN)Database reset completed$(RESET)"; \
	else \
		echo "$(YELLOW)Database reset cancelled$(RESET)"; \
	fi

.PHONY: migrate-status
migrate-status: check-env build-migration ## Show the current migration status and which migrations have been applied
	@echo "Checking migration status..."
ifeq ($(OS),Windows_NT)
	@set "DATABASE_URL=$(DATABASE_URL)" && cd $(MIGRATION_DIR) && cargo run --bin migration -- status
else
	@export DATABASE_URL=$(DATABASE_URL) && $(MIGRATION_BIN) status
endif

.PHONY: migrate-fresh
migrate-fresh: check-env build-migration ## Drop all tables and re-run all migrations from scratch
	@echo "Performing fresh migration (drop all tables and re-create)..."
	@echo "WARNING: This will DROP ALL TABLES and data!"
ifeq ($(OS),Windows_NT)
	@echo "Proceeding with fresh migration in 3 seconds... Press Ctrl+C to cancel"
	@timeout /t 3 /nobreak >nul 2>&1
	@echo "Dropping all tables..."
	@set "DATABASE_URL=$(DATABASE_URL)" && cd $(MIGRATION_DIR) && cargo run --bin migration -- fresh
	@echo "Fresh migration completed"
else
	@read -p "Are you absolutely sure? [y/N] " -n 1 -r; \
	echo ""; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo "Dropping all tables..."; \
		export DATABASE_URL=$(DATABASE_URL) && $(MIGRATION_BIN) fresh; \
		echo "Fresh migration completed"; \
	else \
		echo "Fresh migration cancelled"; \
	fi
endif

.PHONY: migrate-generate
migrate-generate: ## Generate a new migration file (usage: make migrate-generate NAME=create_users_table)
	@if [ -z "$(NAME)" ]; then \
		echo "$(RED)Error: NAME parameter is required$(RESET)"; \
		echo "Usage: make migrate-generate NAME=create_users_table"; \
		exit 1; \
	fi
	@echo "$(BLUE)Generating new migration: $(NAME)$(RESET)"
	@cd $(MIGRATION_DIR) && sea-orm-cli migrate generate $(NAME)
	@echo "$(GREEN)Migration file generated successfully$(RESET)"

.PHONY: db-setup
db-setup: check-env ## Initialize the database and run all migrations (for first-time setup)
	@echo "Setting up database for first time..."
	@echo "Building migration binary..."
	@cd $(MIGRATION_DIR) && cargo build --bin migration
	@echo "Running all migrations..."
ifeq ($(OS),Windows_NT)
	@set "DATABASE_URL=$(DATABASE_URL)" && cd $(MIGRATION_DIR) && cargo run --bin migration -- up
else
	@export DATABASE_URL=$(DATABASE_URL) && $(MIGRATION_BIN) up
endif
	@echo "Database setup completed successfully"
	@echo "Your WellTrack pharmacy database is ready!"

.PHONY: db-seed
db-seed: check-env ## Run database seeders to populate with sample data
	@echo "$(BLUE)Seeding database with sample data...$(RESET)"
	@echo "$(YELLOW)Note: Seeding functionality will be implemented with the sales management system$(RESET)"
	@echo "$(GREEN)Database seeding completed$(RESET)"

.PHONY: db-clean
db-clean: migrate-fresh ## Clean up the database and reset to a fresh state
	@echo "$(GREEN)Database cleaned and reset to fresh state$(RESET)"

.PHONY: db-backup
db-backup: check-env ## Create a backup of the current database
	@echo "$(BLUE)Creating database backup...$(RESET)"
	@BACKUP_FILE="welltrack_backup_$(shell date +%Y%m%d_%H%M%S).sql"; \
	pg_dump $(DATABASE_URL) > $$BACKUP_FILE; \
	echo "$(GREEN)Database backup created: $$BACKUP_FILE$(RESET)"

.PHONY: db-restore
db-restore: check-env ## Restore database from backup file (usage: make db-restore BACKUP_FILE=backup.sql)
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "$(RED)Error: BACKUP_FILE parameter is required$(RESET)"; \
		echo "Usage: make db-restore BACKUP_FILE=backup.sql"; \
		exit 1; \
	fi
	@if [ ! -f "$(BACKUP_FILE)" ]; then \
		echo "$(RED)Error: Backup file $(BACKUP_FILE) not found$(RESET)"; \
		exit 1; \
	fi
	@echo "$(YELLOW)Restoring database from $(BACKUP_FILE)...$(RESET)"
	@read -p "This will overwrite the current database. Are you sure? [y/N] " -n 1 -r; \
	echo ""; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		psql $(DATABASE_URL) < $(BACKUP_FILE); \
		echo "$(GREEN)Database restored successfully$(RESET)"; \
	else \
		echo "$(YELLOW)Database restore cancelled$(RESET)"; \
	fi

.PHONY: entity-generate
entity-generate: check-env ## Generate Sea-ORM entities from the current database schema
	@echo "$(BLUE)Generating entities from database schema...$(RESET)"
	@cd $(ENTITY_DIR) && sea-orm-cli generate entity \
		--database-url $(DATABASE_URL) \
		--output-dir src \
		--with-serde both
	@echo "$(GREEN)Entities generated successfully$(RESET)"

.PHONY: dev-setup
dev-setup: ## Complete development environment setup
	@echo "$(CYAN)Setting up WellTrack development environment...$(RESET)"
	@echo "$(BLUE)Setting up environment configuration...$(RESET)"
	@$(MAKE) env-setup
	@echo "$(BLUE)Installing Rust dependencies...$(RESET)"
	@cd $(TAURI_DIR) && cargo build
	@echo "$(BLUE)Installing Node.js dependencies...$(RESET)"
	@npm install
	@echo "$(BLUE)Setting up database...$(RESET)"
	@$(MAKE) db-setup
	@echo "$(GREEN)Development environment setup completed!$(RESET)"
	@echo ""
	@echo "$(CYAN)Next steps:$(RESET)"
	@echo "  1. Edit $(ENV_FILE) with your database credentials if needed"
	@echo "  2. Start the development server: $(WHITE)npm run dev$(RESET)"
	@echo "  3. Check migration status: $(WHITE)make migrate-status$(RESET)"
	@echo "  4. View available commands: $(WHITE)make help$(RESET)"

.PHONY: test-db
test-db: ## Run database-related tests
	@echo "$(BLUE)Running database tests...$(RESET)"
	@cd $(TAURI_DIR) && cargo test --package db_entity
	@cd $(TAURI_DIR) && cargo test --package db_service
	@echo "$(GREEN)Database tests completed$(RESET)"

.PHONY: lint-db
lint-db: ## Run linting on database-related code
	@echo "$(BLUE)Linting database code...$(RESET)"
	@cd $(ENTITY_DIR) && cargo clippy -- -D warnings
	@cd $(MIGRATION_DIR) && cargo clippy -- -D warnings
	@cd $(SERVICE_DIR) && cargo clippy -- -D warnings
	@echo "$(GREEN)Database linting completed$(RESET)"

.PHONY: clean
clean: ## Clean build artifacts
	@echo "$(BLUE)Cleaning build artifacts...$(RESET)"
	@cd $(TAURI_DIR) && cargo clean
	@echo "$(GREEN)Clean completed$(RESET)"

# Default target
.DEFAULT_GOAL := help
