{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 1696508418934853334], [10755362358622467486, "build_script_build", false, 9269494212664942228], [3935545708480822364, "build_script_build", false, 2258340456194240961]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-ad5987a8b1cfc708\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}