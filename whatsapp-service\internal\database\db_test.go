package database

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewDB(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	// Mock successful ping
	mock.ExpectPing()

	dbWrapper := &DB{DB: db}
	err = dbWrapper.Ping()
	assert.NoError(t, err)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDBQuery(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Mock successful query
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "test").
		AddRow(2, "test2")

	mock.ExpectQuery("SELECT (.+) FROM test").WillReturnRows(rows)

	result, err := dbWrapper.Query("SELECT id, name FROM test")
	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDBQueryRow(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Mock successful query
	rows := sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "test")
	mock.ExpectQuery("SELECT (.+) FROM test WHERE id = ?").WithArgs(1).WillReturnRows(rows)

	result := dbWrapper.QueryRow("SELECT id, name FROM test WHERE id = ?", 1)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDBExec(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Mock successful execution
	mock.ExpectExec("INSERT INTO test").WithArgs("test").WillReturnResult(sqlmock.NewResult(1, 1))

	result, err := dbWrapper.Exec("INSERT INTO test (name) VALUES (?)", "test")
	assert.NoError(t, err)
	assert.NotNil(t, result)

	rowsAffected, err := result.RowsAffected()
	assert.NoError(t, err)
	assert.Equal(t, int64(1), rowsAffected)

	lastInsertId, err := result.LastInsertId()
	assert.NoError(t, err)
	assert.Equal(t, int64(1), lastInsertId)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDBTransaction(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Mock transaction
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO test").WithArgs("test1").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectExec("INSERT INTO test").WithArgs("test2").WillReturnResult(sqlmock.NewResult(2, 1))
	mock.ExpectCommit()

	tx, err := dbWrapper.Begin()
	assert.NoError(t, err)

	_, err = tx.Exec("INSERT INTO test (name) VALUES (?)", "test1")
	assert.NoError(t, err)

	_, err = tx.Exec("INSERT INTO test (name) VALUES (?)", "test2")
	assert.NoError(t, err)

	err = tx.Commit()
	assert.NoError(t, err)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDBTransactionRollback(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Mock transaction with rollback
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO test").WithArgs("test1").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectExec("INSERT INTO test").WithArgs("test2").WillReturnError(sql.ErrConnDone)
	mock.ExpectRollback()

	tx, err := dbWrapper.Begin()
	assert.NoError(t, err)

	_, err = tx.Exec("INSERT INTO test (name) VALUES (?)", "test1")
	assert.NoError(t, err)

	_, err = tx.Exec("INSERT INTO test (name) VALUES (?)", "test2")
	assert.Error(t, err)

	err = tx.Rollback()
	assert.NoError(t, err)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMessageOperations(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Test inserting a message
	mock.ExpectExec("INSERT INTO messages").
		WithArgs("msg123", "acc1", "group1", "Group Name", "sender1", "Sender Name", 
			"Test message", "pending", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	now := time.Now()
	result, err := dbWrapper.Exec(`
		INSERT INTO messages (whatsapp_message_id, account_id, group_jid, group_name, 
			sender_jid, sender_name, content, processing_status, message_timestamp, received_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		"msg123", "acc1", "group1", "Group Name", "sender1", "Sender Name", 
		"Test message", "pending", now, now)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestAccountOperations(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Test inserting an account
	mock.ExpectExec("INSERT INTO whatsapp_accounts").
		WithArgs("acc1", "+************", "disconnected", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	now := time.Now()
	result, err := dbWrapper.Exec(`
		INSERT INTO whatsapp_accounts (account_id, phone_number, status, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?)`,
		"acc1", "+************", "disconnected", now, now)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Test updating account status
	mock.ExpectExec("UPDATE whatsapp_accounts").
		WithArgs("connected", sqlmock.AnyArg(), "acc1").
		WillReturnResult(sqlmock.NewResult(0, 1))

	result, err = dbWrapper.Exec(`
		UPDATE whatsapp_accounts SET status = ?, last_seen = ? WHERE account_id = ?`,
		"connected", now, "acc1")

	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMedicineOperations(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Test finding or creating medicine
	mock.ExpectQuery("SELECT id FROM medicines WHERE name = ?").
		WithArgs("paracetamol").
		WillReturnError(sql.ErrNoRows)

	mock.ExpectExec("INSERT INTO medicines").
		WithArgs("paracetamol", "pain reliever", "analgesic", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// First try to find existing medicine
	row := dbWrapper.QueryRow("SELECT id FROM medicines WHERE name = ?", "paracetamol")
	var medicineID int
	err = row.Scan(&medicineID)
	assert.Error(t, err) // Should not exist

	// Insert new medicine
	now := time.Now()
	result, err := dbWrapper.Exec(`
		INSERT INTO medicines (name, description, category, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?)`,
		"paracetamol", "pain reliever", "analgesic", now, now)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMatchOperations(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Test inserting a match
	mock.ExpectExec("INSERT INTO matches").
		WithArgs(1, 2, 1, 0.85, "pending", `{"name_similarity": 1.0}`, 
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	now := time.Now()
	expiresAt := now.Add(7 * 24 * time.Hour)
	result, err := dbWrapper.Exec(`
		INSERT INTO matches (request_message_id, offer_message_id, medicine_id, 
			confidence_score, status, matching_criteria, created_at, updated_at, expires_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		1, 2, 1, 0.85, "pending", `{"name_similarity": 1.0}`, now, now, expiresAt)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Test updating match status
	mock.ExpectExec("UPDATE matches").
		WithArgs("completed", sqlmock.AnyArg(), sqlmock.AnyArg(), 1).
		WillReturnResult(sqlmock.NewResult(0, 1))

	result, err = dbWrapper.Exec(`
		UPDATE matches SET status = ?, completed_at = ?, updated_at = ? WHERE id = ?`,
		"completed", now, now, 1)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestComplexQuery(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Test complex join query
	rows := sqlmock.NewRows([]string{
		"match_id", "confidence_score", "medicine_name", "request_content", "offer_content",
	}).AddRow(1, 0.85, "paracetamol", "أحتاج باراسيتامول", "متوفر باراسيتامول")

	mock.ExpectQuery("SELECT (.+) FROM matches").WillReturnRows(rows)

	result, err := dbWrapper.Query(`
		SELECT m.id as match_id, m.confidence_score, med.name as medicine_name,
			   req.content as request_content, off.content as offer_content
		FROM matches m
		JOIN medicines med ON m.medicine_id = med.id
		JOIN messages req ON m.request_message_id = req.id
		JOIN messages off ON m.offer_message_id = off.id
		WHERE m.status = 'pending'
		ORDER BY m.confidence_score DESC`)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestErrorHandling(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Test query error
	mock.ExpectQuery("SELECT (.+) FROM nonexistent").WillReturnError(sql.ErrNoRows)

	result, err := dbWrapper.Query("SELECT * FROM nonexistent")
	assert.Error(t, err)
	assert.Nil(t, result)

	// Test exec error
	mock.ExpectExec("INSERT INTO nonexistent").WillReturnError(sql.ErrConnDone)

	result2, err := dbWrapper.Exec("INSERT INTO nonexistent VALUES (1)")
	assert.Error(t, err)
	assert.Nil(t, result2)

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Benchmark tests
func BenchmarkDBQuery(b *testing.B) {
	db, mock, err := sqlmock.New()
	require.NoError(b, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Mock query for benchmark
	rows := sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "test")
	mock.ExpectQuery("SELECT (.+) FROM test").WillReturnRows(rows).Times(b.N)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		dbWrapper.Query("SELECT id, name FROM test")
	}
}

func BenchmarkDBExec(b *testing.B) {
	db, mock, err := sqlmock.New()
	require.NoError(b, err)
	defer db.Close()

	dbWrapper := &DB{DB: db}

	// Mock exec for benchmark
	mock.ExpectExec("INSERT INTO test").WillReturnResult(sqlmock.NewResult(1, 1)).Times(b.N)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		dbWrapper.Exec("INSERT INTO test (name) VALUES (?)", "test")
	}
}
