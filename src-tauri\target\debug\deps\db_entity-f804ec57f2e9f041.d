D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\libdb_entity-f804ec57f2e9f041.rmeta: db\entity\src\lib.rs db\entity\src\accounts.rs db\entity\src\matches.rs db\entity\src\medicines.rs db\entity\src\message_medicines.rs db\entity\src\messages.rs Cargo.toml

D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\db_entity-f804ec57f2e9f041.d: db\entity\src\lib.rs db\entity\src\accounts.rs db\entity\src\matches.rs db\entity\src\medicines.rs db\entity\src\message_medicines.rs db\entity\src\messages.rs Cargo.toml

db\entity\src\lib.rs:
db\entity\src\accounts.rs:
db\entity\src\matches.rs:
db\entity\src\medicines.rs:
db\entity\src\message_medicines.rs:
db\entity\src\messages.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
