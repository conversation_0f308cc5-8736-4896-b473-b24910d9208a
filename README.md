# MediBridge - نظام إدارة المخزون الدوائي

![MediBridge Logo](./assets/logo.png)

## 📋 نظرة عامة

MediBridge هو نظام متقدم لإدارة المخزون الدوائي يراقب مجموعات WhatsApp لطلبات وعروض الأدوية، ويستخدم الذكاء الصناعي لتحليل الرسائل العربية، ويطابق العرض مع الطلب، ويعرض النتائج في لوحة تحكم شاملة.

## ✨ الميزات الرئيسية

### 🤖 الذكاء الصناعي المحلي
- معالجة الرسائل العربية باستخدام Ollama
- استخراج معلومات الأدوية تلقائياً
- تصنيف الرسائل (طلب/عرض/غير محدد)
- حماية الخصوصية بالمعالجة المحلية

### 📱 تكامل WhatsApp
- مراقبة مجموعات WhatsApp متعددة
- إدارة حسابات متعددة
- اتصال آمن ومشفر
- معالجة الرسائل في الوقت الفعلي

### 🎯 نظام المطابقة الذكي
- خوارزمية متقدمة لمطابقة الطلبات مع العروض
- معايير مطابقة متعددة (الاسم، السعر، الكمية، الوقت)
- درجات ثقة للمطابقات
- إشعارات تلقائية للمطابقات الجديدة

### 📊 لوحة تحكم شاملة
- إحصائيات مفصلة ورسوم بيانية
- مراقبة الأداء في الوقت الفعلي
- تقارير قابلة للتخصيص
- واجهة مستخدم عربية بالكامل

## 🏗️ البنية التقنية

### Frontend (Tauri + React)
- **Tauri**: إطار عمل لتطبيقات سطح المكتب
- **React 18**: مكتبة واجهة المستخدم
- **TypeScript**: لغة البرمجة المكتوبة
- **TanStack Router**: توجيه متقدم
- **Shadcn/ui**: مكونات واجهة المستخدم
- **Tailwind CSS**: تنسيق الواجهة

### Backend (Go)
- **Go 1.21+**: لغة البرمجة الخلفية
- **Gin**: إطار عمل HTTP
- **Whatsmeow**: مكتبة WhatsApp
- **PostgreSQL**: قاعدة البيانات
- **Ollama**: خدمة الذكاء الصناعي المحلية

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية

```bash
# Node.js 18+
node --version

# Go 1.21+
go version

# Rust (للـ Tauri)
rustc --version

# PostgreSQL
psql --version

# Ollama
ollama --version
```

### 1. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE medibridge;

-- إنشاء المستخدم
CREATE USER medibridge_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE medibridge TO medibridge_user;
```

### 2. إعداد Ollama

```bash
# تثبيت Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# تحميل نموذج اللغة العربية
ollama pull llama2
```

### 3. إعداد المشروع

```bash
# استنساخ المشروع
git clone https://github.com/your-username/medibridge.git
cd medibridge

# تثبيت dependencies للـ frontend
npm install

# إعداد متغيرات البيئة
cp .env.example .env
# قم بتحرير .env وإضافة التكوينات المطلوبة

# إعداد خدمة Go
cd whatsapp-service
cp .env.example .env
# قم بتحرير .env وإضافة تكوينات قاعدة البيانات
```

### 4. تشغيل التطبيق

```bash
# تشغيل خدمة Go
cd whatsapp-service
go run main.go

# في terminal آخر، تشغيل تطبيق Tauri
cd ..
npm run tauri dev
```

## 📁 هيكل المشروع

```
MediBridge/
├── src/                          # Frontend React
│   ├── components/               # مكونات React
│   ├── routes/                   # صفحات التطبيق
│   ├── lib/                      # مكتبات مساعدة
│   └── tests/                    # اختبارات Frontend
├── src-tauri/                    # تكوين Tauri
│   ├── src/                      # كود Rust
│   └── Cargo.toml               # dependencies Rust
├── whatsapp-service/             # خدمة Go الخلفية
│   ├── internal/                # كود Go الداخلي
│   │   ├── api/                 # REST API
│   │   ├── ai/                  # خدمة الذكاء الصناعي
│   │   ├── database/            # قاعدة البيانات
│   │   ├── matching/            # نظام المطابقة
│   │   ├── whatsapp/            # تكامل WhatsApp
│   │   └── performance/         # تحسين الأداء
│   ├── migrations/              # هجرات قاعدة البيانات
│   └── main.go                  # نقطة البداية
└── docs/                        # الوثائق
```

## 🔧 التكوين

### متغيرات البيئة (.env)

```env
# قاعدة البيانات
DB_HOST=localhost
DB_PORT=5432
DB_NAME=medibridge
DB_USER=medibridge_user
DB_PASSWORD=your_password

# Ollama
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama2

# خدمة WhatsApp
WHATSAPP_PORT=8080
WHATSAPP_SESSION_PATH=./sessions

# الأمان
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key
```

## 📊 استخدام النظام

### 1. إضافة حسابات WhatsApp
1. انتقل إلى صفحة "الحسابات"
2. انقر على "إضافة حساب جديد"
3. امسح رمز QR بهاتفك
4. انتظر حتى يتم الاتصال

### 2. مراقبة الرسائل
- يتم مراقبة الرسائل تلقائياً
- يظهر تحليل الذكاء الصناعي في صفحة "الرسائل"
- يمكن إعادة معالجة الرسائل يدوياً

### 3. عرض المطابقات
- انتقل إلى صفحة "المطابقات"
- اعرض المطابقات المقترحة
- قم بتحديث حالة المطابقات
- شغل المطابقة اليدوية عند الحاجة

### 4. مراجعة الإحصائيات
- لوحة التحكم تعرض نظرة عامة
- صفحة الإحصائيات تحتوي على تفاصيل أكثر
- التقارير قابلة للتحميل والمشاركة

## 🧪 الاختبارات

### تشغيل اختبارات Frontend

```bash
# اختبارات الوحدة
npm run test

# اختبارات التكامل
npm run test:integration

# تغطية الكود
npm run test:coverage
```

### تشغيل اختبارات Backend

```bash
cd whatsapp-service

# اختبارات الوحدة
go test ./...

# اختبارات الأداء
go test -bench=. ./...

# تغطية الكود
go test -cover ./...
```

## 🔒 الأمان والخصوصية

- **تشفير محلي**: جميع البيانات مشفرة محلياً
- **لا توجد خوادم خارجية**: المعالجة تتم محلياً بالكامل
- **حماية جلسات WhatsApp**: جلسات آمنة ومحمية
- **تحكم في الوصول**: نظام صلاحيات متقدم

## 📈 تحسين الأداء

- **تجميع الاتصالات**: إدارة فعالة لاتصالات قاعدة البيانات
- **ذاكرة التخزين المؤقت**: تخزين مؤقت ذكي للبيانات المتكررة
- **تحديد المعدل**: حماية من الحمولة الزائدة
- **مراقبة الموارد**: مراقبة استخدام الذاكرة والمعالج

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### خطوات المساهمة

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- **الوثائق**: [docs/](./docs/)
- **Issues**: [GitHub Issues](https://github.com/your-username/medibridge/issues)
- **المناقشات**: [GitHub Discussions](https://github.com/your-username/medibridge/discussions)

## 🙏 شكر وتقدير

- [Tauri](https://tauri.app/) - إطار عمل تطبيقات سطح المكتب
- [Whatsmeow](https://github.com/tulir/whatsmeow) - مكتبة WhatsApp
- [Ollama](https://ollama.ai/) - خدمة الذكاء الصناعي المحلية
- [Shadcn/ui](https://ui.shadcn.com/) - مكونات واجهة المستخدم

---

<div align="center">
  <p>صُنع بـ ❤️ لخدمة المجتمع الطبي</p>
  <p>© 2024 MediBridge. جميع الحقوق محفوظة.</p>
</div>
