import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { 
  Settings, 
  Database, 
  Bot, 
  MessageSquare, 
  Shield, 
  Bell,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'

interface SystemSettings {
  // WhatsApp Settings
  whatsapp_enabled: boolean
  auto_connect: boolean
  message_processing_interval: number
  max_retries: number
  
  // AI Settings
  ai_enabled: boolean
  ai_model: string
  ai_confidence_threshold: number
  ai_processing_batch_size: number
  
  // Matching Settings
  matching_enabled: boolean
  matching_interval: number
  matching_confidence_threshold: number
  auto_expire_days: number
  
  // Notification Settings
  notifications_enabled: boolean
  email_notifications: boolean
  webhook_url: string
  
  // Database Settings
  cleanup_enabled: boolean
  cleanup_interval_days: number
  backup_enabled: boolean
  backup_interval_hours: number
}

export const Route = createFileRoute('/settings')({
  component: SettingsPage,
})

function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>({
    whatsapp_enabled: true,
    auto_connect: true,
    message_processing_interval: 10,
    max_retries: 3,
    ai_enabled: true,
    ai_model: 'llama2',
    ai_confidence_threshold: 0.7,
    ai_processing_batch_size: 10,
    matching_enabled: true,
    matching_interval: 5,
    matching_confidence_threshold: 0.5,
    auto_expire_days: 7,
    notifications_enabled: true,
    email_notifications: false,
    webhook_url: '',
    cleanup_enabled: true,
    cleanup_interval_days: 30,
    backup_enabled: false,
    backup_interval_hours: 24
  })
  
  const [loading, setLoading] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const updateSetting = (key: keyof SystemSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    setHasChanges(true)
  }

  const saveSettings = async () => {
    try {
      setLoading(true)
      // TODO: Implement API call to save settings
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      toast.success('تم حفظ الإعدادات بنجاح')
      setHasChanges(false)
    } catch (error) {
      toast.error('فشل في حفظ الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  const resetSettings = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      setSettings({
        whatsapp_enabled: true,
        auto_connect: true,
        message_processing_interval: 10,
        max_retries: 3,
        ai_enabled: true,
        ai_model: 'llama2',
        ai_confidence_threshold: 0.7,
        ai_processing_batch_size: 10,
        matching_enabled: true,
        matching_interval: 5,
        matching_confidence_threshold: 0.5,
        auto_expire_days: 7,
        notifications_enabled: true,
        email_notifications: false,
        webhook_url: '',
        cleanup_enabled: true,
        cleanup_interval_days: 30,
        backup_enabled: false,
        backup_interval_hours: 24
      })
      setHasChanges(true)
      toast.info('تم إعادة تعيين الإعدادات')
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">الإعدادات</h1>
          <p className="text-muted-foreground">إدارة إعدادات النظام والتكوين</p>
        </div>
        
        <div className="flex items-center space-x-2">
          {hasChanges && (
            <Badge variant="outline" className="text-orange-600">
              <AlertCircle className="w-3 h-3 mr-1" />
              تغييرات غير محفوظة
            </Badge>
          )}
          
          <Button variant="outline" onClick={resetSettings}>
            <RefreshCw className="w-4 h-4 mr-2" />
            إعادة تعيين
          </Button>
          
          <Button onClick={saveSettings} disabled={loading || !hasChanges}>
            <Save className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            حفظ التغييرات
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* WhatsApp Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              إعدادات WhatsApp
            </CardTitle>
            <CardDescription>تكوين اتصالات ومعالجة رسائل WhatsApp</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>تفعيل خدمة WhatsApp</Label>
                <p className="text-xs text-muted-foreground">تشغيل/إيقاف معالجة رسائل WhatsApp</p>
              </div>
              <Switch
                checked={settings.whatsapp_enabled}
                onCheckedChange={(checked) => updateSetting('whatsapp_enabled', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label>الاتصال التلقائي</Label>
                <p className="text-xs text-muted-foreground">اتصال تلقائي عند بدء التشغيل</p>
              </div>
              <Switch
                checked={settings.auto_connect}
                onCheckedChange={(checked) => updateSetting('auto_connect', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>فترة معالجة الرسائل (ثانية)</Label>
              <Input
                type="number"
                value={settings.message_processing_interval}
                onChange={(e) => updateSetting('message_processing_interval', parseInt(e.target.value))}
                min="5"
                max="300"
              />
            </div>
            
            <div className="space-y-2">
              <Label>عدد المحاولات القصوى</Label>
              <Input
                type="number"
                value={settings.max_retries}
                onChange={(e) => updateSetting('max_retries', parseInt(e.target.value))}
                min="1"
                max="10"
              />
            </div>
          </CardContent>
        </Card>

        {/* AI Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="w-5 h-5" />
              إعدادات الذكاء الصناعي
            </CardTitle>
            <CardDescription>تكوين معالجة الرسائل بالذكاء الصناعي</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>تفعيل الذكاء الصناعي</Label>
                <p className="text-xs text-muted-foreground">معالجة الرسائل بالذكاء الصناعي</p>
              </div>
              <Switch
                checked={settings.ai_enabled}
                onCheckedChange={(checked) => updateSetting('ai_enabled', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>نموذج الذكاء الصناعي</Label>
              <Select
                value={settings.ai_model}
                onValueChange={(value) => updateSetting('ai_model', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="llama2">Llama 2</SelectItem>
                  <SelectItem value="llama2:13b">Llama 2 13B</SelectItem>
                  <SelectItem value="mistral">Mistral</SelectItem>
                  <SelectItem value="codellama">Code Llama</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>حد الثقة ({Math.round(settings.ai_confidence_threshold * 100)}%)</Label>
              <Input
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                value={settings.ai_confidence_threshold}
                onChange={(e) => updateSetting('ai_confidence_threshold', parseFloat(e.target.value))}
                className="w-full"
              />
            </div>
            
            <div className="space-y-2">
              <Label>حجم دفعة المعالجة</Label>
              <Input
                type="number"
                value={settings.ai_processing_batch_size}
                onChange={(e) => updateSetting('ai_processing_batch_size', parseInt(e.target.value))}
                min="1"
                max="50"
              />
            </div>
          </CardContent>
        </Card>

        {/* Matching Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              إعدادات المطابقة
            </CardTitle>
            <CardDescription>تكوين نظام مطابقة الطلبات والعروض</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>تفعيل المطابقة التلقائية</Label>
                <p className="text-xs text-muted-foreground">البحث التلقائي عن المطابقات</p>
              </div>
              <Switch
                checked={settings.matching_enabled}
                onCheckedChange={(checked) => updateSetting('matching_enabled', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>فترة المطابقة (دقائق)</Label>
              <Input
                type="number"
                value={settings.matching_interval}
                onChange={(e) => updateSetting('matching_interval', parseInt(e.target.value))}
                min="1"
                max="60"
              />
            </div>
            
            <div className="space-y-2">
              <Label>حد ثقة المطابقة ({Math.round(settings.matching_confidence_threshold * 100)}%)</Label>
              <Input
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                value={settings.matching_confidence_threshold}
                onChange={(e) => updateSetting('matching_confidence_threshold', parseFloat(e.target.value))}
                className="w-full"
              />
            </div>
            
            <div className="space-y-2">
              <Label>انتهاء صلاحية المطابقة (أيام)</Label>
              <Input
                type="number"
                value={settings.auto_expire_days}
                onChange={(e) => updateSetting('auto_expire_days', parseInt(e.target.value))}
                min="1"
                max="30"
              />
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              إعدادات الإشعارات
            </CardTitle>
            <CardDescription>تكوين إشعارات النظام والتنبيهات</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>تفعيل الإشعارات</Label>
                <p className="text-xs text-muted-foreground">إرسال إشعارات للأحداث المهمة</p>
              </div>
              <Switch
                checked={settings.notifications_enabled}
                onCheckedChange={(checked) => updateSetting('notifications_enabled', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label>إشعارات البريد الإلكتروني</Label>
                <p className="text-xs text-muted-foreground">إرسال إشعارات عبر البريد</p>
              </div>
              <Switch
                checked={settings.email_notifications}
                onCheckedChange={(checked) => updateSetting('email_notifications', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>رابط Webhook</Label>
              <Input
                type="url"
                placeholder="https://example.com/webhook"
                value={settings.webhook_url}
                onChange={(e) => updateSetting('webhook_url', e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                رابط لإرسال إشعارات HTTP POST
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Database Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              إعدادات قاعدة البيانات
            </CardTitle>
            <CardDescription>إدارة البيانات والنسخ الاحتياطية</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>تنظيف تلقائي</Label>
                <p className="text-xs text-muted-foreground">حذف البيانات القديمة تلقائياً</p>
              </div>
              <Switch
                checked={settings.cleanup_enabled}
                onCheckedChange={(checked) => updateSetting('cleanup_enabled', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>فترة التنظيف (أيام)</Label>
              <Input
                type="number"
                value={settings.cleanup_interval_days}
                onChange={(e) => updateSetting('cleanup_interval_days', parseInt(e.target.value))}
                min="7"
                max="365"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label>النسخ الاحتياطي التلقائي</Label>
                <p className="text-xs text-muted-foreground">إنشاء نسخ احتياطية دورية</p>
              </div>
              <Switch
                checked={settings.backup_enabled}
                onCheckedChange={(checked) => updateSetting('backup_enabled', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>فترة النسخ الاحتياطي (ساعات)</Label>
              <Input
                type="number"
                value={settings.backup_interval_hours}
                onChange={(e) => updateSetting('backup_interval_hours', parseInt(e.target.value))}
                min="1"
                max="168"
              />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              إعدادات الأمان
            </CardTitle>
            <CardDescription>تكوين أمان النظام والوصول</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-muted rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Info className="w-4 h-4 text-blue-500" />
                <h4 className="font-medium">معلومات الأمان</h4>
              </div>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• جميع البيانات مشفرة محلياً</li>
                <li>• لا يتم إرسال بيانات خارج النظام</li>
                <li>• معالجة الذكاء الصناعي محلية</li>
                <li>• جلسات WhatsApp محمية</li>
              </ul>
            </div>
            
            <div className="flex items-center justify-center p-4 border-2 border-dashed border-green-200 rounded-lg">
              <div className="text-center">
                <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <p className="text-sm font-medium text-green-700">النظام آمن</p>
                <p className="text-xs text-green-600">جميع إعدادات الأمان مفعلة</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Changes Banner */}
      {hasChanges && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-orange-600" />
                <div>
                  <h3 className="font-medium text-orange-800">تغييرات غير محفوظة</h3>
                  <p className="text-sm text-orange-700">لديك تغييرات غير محفوظة في الإعدادات</p>
                </div>
              </div>
              <Button onClick={saveSettings} disabled={loading}>
                <Save className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                حفظ الآن
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
