package matching

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBasicMatching(t *testing.T) {
	// Test basic functionality that doesn't require database
	assert.True(t, true, "Basic test should pass")
}

func TestStringComparison(t *testing.T) {
	tests := []struct {
		name     string
		str1     string
		str2     string
		expected bool
	}{
		{"exact_match", "paracetamol", "paracetamol", true},
		{"case_insensitive", "Paracetamol", "paracetamol", false}, // case sensitive
		{"different_strings", "aspirin", "paracetamol", false},
		{"empty_strings", "", "", true},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := test.str1 == test.str2
			assert.Equal(t, test.expected, result)
		})
	}
}

// Benchmark test for string operations
func BenchmarkStringComparison(b *testing.B) {
	str1 := "paracetamol 500mg tablets"
	str2 := "paracetamol extra strength"

	for b.<PERSON>() {
		_ = str1 == str2
	}
}
