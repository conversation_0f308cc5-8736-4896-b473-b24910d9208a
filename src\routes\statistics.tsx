import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { invoke } from '@tauri-apps/api/core'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  MessageSquare, 
  Users, 
  Pill,
  Target,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react'

interface StatsOverview {
  total_accounts: number
  active_accounts: number
  total_messages: number
  processed_messages: number
  pending_messages: number
  total_medicines: number
  total_matches: number
}

interface MessageStats {
  daily_messages: Array<{ date: string; count: number }>
  message_types: {
    request: number
    offer: number
    unknown: number
  }
  processing_stats: {
    pending: number
    processed: number
    failed: number
  }
}

interface MatchingStats {
  total_matches: number
  recent_matches: number
  matches_by_status: Record<string, number>
  average_confidence: number
  success_rate: number
}

export const Route = createFileRoute('/statistics')({
  component: StatisticsPage,
})

function StatisticsPage() {
  const [overview, setOverview] = useState<StatsOverview | null>(null)
  const [messageStats, setMessageStats] = useState<MessageStats | null>(null)
  const [matchingStats, setMatchingStats] = useState<MatchingStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d')

  useEffect(() => {
    loadStatistics()
  }, [timeRange])

  const loadStatistics = async () => {
    try {
      setLoading(true)
      const [overviewData, messageStatsData, matchingStatsData] = await Promise.all([
        invoke<StatsOverview>('get_stats_overview'),
        invoke<MessageStats>('get_message_stats'),
        invoke<MatchingStats>('get_matching_stats')
      ])
      
      setOverview(overviewData)
      setMessageStats(messageStatsData)
      setMatchingStats(matchingStatsData)
    } catch (error) {
      console.error('Failed to load statistics:', error)
      toast.error('فشل في تحميل الإحصائيات')
    } finally {
      setLoading(false)
    }
  }

  const getProcessingRate = () => {
    if (!overview) return 0
    if (overview.total_messages === 0) return 0
    return Math.round((overview.processed_messages / overview.total_messages) * 100)
  }

  const getActiveAccountsRate = () => {
    if (!overview) return 0
    if (overview.total_accounts === 0) return 0
    return Math.round((overview.active_accounts / overview.total_accounts) * 100)
  }

  if (loading && !overview) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">الإحصائيات</h1>
          <p className="text-muted-foreground">تحليل شامل لأداء النظام</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">يوم واحد</SelectItem>
              <SelectItem value="7d">7 أيام</SelectItem>
              <SelectItem value="30d">30 يوم</SelectItem>
              <SelectItem value="90d">90 يوم</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={loadStatistics} disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الرسائل</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.total_messages || 0}</div>
            <p className="text-xs text-muted-foreground">
              معدل المعالجة: {getProcessingRate()}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الحسابات النشطة</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.active_accounts || 0}</div>
            <p className="text-xs text-muted-foreground">
              من أصل {overview?.total_accounts || 0} ({getActiveAccountsRate()}%)
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأدوية المكتشفة</CardTitle>
            <Pill className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.total_medicines || 0}</div>
            <p className="text-xs text-muted-foreground">
              أدوية مختلفة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المطابقات</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.total_matches || 0}</div>
            <p className="text-xs text-muted-foreground">
              معدل النجاح: {matchingStats ? Math.round(matchingStats.success_rate * 100) : 0}%
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Message Processing Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              معالجة الرسائل
            </CardTitle>
            <CardDescription>حالة معالجة الرسائل بالذكاء الصناعي</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {messageStats && (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>تم المعالجة</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="default">{messageStats.processing_stats.processed}</Badge>
                    <span className="text-xs text-muted-foreground">
                      ({overview ? Math.round((messageStats.processing_stats.processed / overview.total_messages) * 100) : 0}%)
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-yellow-500" />
                    <span>في الانتظار</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{messageStats.processing_stats.pending}</Badge>
                    <span className="text-xs text-muted-foreground">
                      ({overview ? Math.round((messageStats.processing_stats.pending / overview.total_messages) * 100) : 0}%)
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <span>فشل</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive">{messageStats.processing_stats.failed}</Badge>
                    <span className="text-xs text-muted-foreground">
                      ({overview ? Math.round((messageStats.processing_stats.failed / overview.total_messages) * 100) : 0}%)
                    </span>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Message Types */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              أنواع الرسائل
            </CardTitle>
            <CardDescription>توزيع الرسائل حسب النوع</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {messageStats && (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-blue-500" />
                    <span>طلبات</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="default">{messageStats.message_types.request}</Badge>
                    <span className="text-xs text-muted-foreground">
                      ({Math.round((messageStats.message_types.request / (messageStats.message_types.request + messageStats.message_types.offer + messageStats.message_types.unknown)) * 100)}%)
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingDown className="w-4 h-4 text-green-500" />
                    <span>عروض</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{messageStats.message_types.offer}</Badge>
                    <span className="text-xs text-muted-foreground">
                      ({Math.round((messageStats.message_types.offer / (messageStats.message_types.request + messageStats.message_types.offer + messageStats.message_types.unknown)) * 100)}%)
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4 text-gray-500" />
                    <span>غير محدد</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{messageStats.message_types.unknown}</Badge>
                    <span className="text-xs text-muted-foreground">
                      ({Math.round((messageStats.message_types.unknown / (messageStats.message_types.request + messageStats.message_types.offer + messageStats.message_types.unknown)) * 100)}%)
                    </span>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Matching Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              أداء المطابقة
            </CardTitle>
            <CardDescription>إحصائيات نظام المطابقة الذكي</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {matchingStats && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{matchingStats.total_matches}</div>
                    <div className="text-xs text-muted-foreground">إجمالي المطابقات</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{matchingStats.recent_matches}</div>
                    <div className="text-xs text-muted-foreground">مطابقات حديثة</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">متوسط الثقة</span>
                    <span className="text-sm font-medium">{Math.round(matchingStats.average_confidence * 100)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">معدل النجاح</span>
                    <span className="text-sm font-medium">{Math.round(matchingStats.success_rate * 100)}%</span>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Match Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              حالات المطابقة
            </CardTitle>
            <CardDescription>توزيع المطابقات حسب الحالة</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {matchingStats && Object.entries(matchingStats.matches_by_status).map(([status, count]) => {
              const statusMap: Record<string, { label: string; color: string }> = {
                pending: { label: 'في الانتظار', color: 'text-yellow-600' },
                notified: { label: 'تم الإشعار', color: 'text-blue-600' },
                completed: { label: 'مكتمل', color: 'text-green-600' },
                expired: { label: 'منتهي الصلاحية', color: 'text-gray-600' },
                cancelled: { label: 'ملغي', color: 'text-red-600' }
              }
              
              const statusInfo = statusMap[status] || { label: status, color: 'text-gray-600' }
              const percentage = matchingStats.total_matches > 0 ? Math.round((count / matchingStats.total_matches) * 100) : 0
              
              return (
                <div key={status} className="flex items-center justify-between">
                  <span className="text-sm">{statusInfo.label}</span>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${statusInfo.color}`}>{count}</span>
                    <span className="text-xs text-muted-foreground">({percentage}%)</span>
                  </div>
                </div>
              )
            })}
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle>رؤى الأداء</CardTitle>
          <CardDescription>تحليل أداء النظام واقتراحات التحسين</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <h3 className="font-semibold">معدل المعالجة</h3>
              </div>
              <p className="text-2xl font-bold text-green-600">{getProcessingRate()}%</p>
              <p className="text-xs text-muted-foreground mt-1">
                {getProcessingRate() >= 90 ? 'ممتاز' : getProcessingRate() >= 70 ? 'جيد' : 'يحتاج تحسين'}
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="w-5 h-5 text-blue-500" />
                <h3 className="font-semibold">دقة المطابقة</h3>
              </div>
              <p className="text-2xl font-bold text-blue-600">
                {matchingStats ? Math.round(matchingStats.average_confidence * 100) : 0}%
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                {matchingStats && matchingStats.average_confidence >= 0.8 ? 'عالية' : 'متوسطة'}
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Users className="w-5 h-5 text-purple-500" />
                <h3 className="font-semibold">نشاط الحسابات</h3>
              </div>
              <p className="text-2xl font-bold text-purple-600">{getActiveAccountsRate()}%</p>
              <p className="text-xs text-muted-foreground mt-1">
                {getActiveAccountsRate() >= 80 ? 'نشاط عالي' : 'نشاط متوسط'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
