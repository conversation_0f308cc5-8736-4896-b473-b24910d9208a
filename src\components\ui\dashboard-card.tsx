import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { type LucideIcon, RefreshCw } from 'lucide-react';
import type React from 'react';

// Types for the DashboardCard component
export interface DashboardCardBadge {
  text: string;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  className?: string;
}

export interface DashboardCardAction {
  icon: LucideIcon;
  onClick: () => void;
  disabled?: boolean;
  tooltip?: string;
  className?: string;
}

export interface DashboardCardProps {
  title: string;
  subtitle?: string;
  icon?: LucideIcon;
  badges?: DashboardCardBadge[];
  actions?: DashboardCardAction[];
  isLoading?: boolean;
  error?: string | Error | null;
  className?: string;
  children: React.ReactNode;
  loadingSkeletonRows?: number;
  headerClassName?: string;
  contentClassName?: string;
}

// Loading skeleton component for consistent loading states
const LoadingSkeleton: React.FC<{ rows?: number }> = ({ rows = 3 }) => (
  <div className="space-y-3">
    <div className="grid grid-cols-2 gap-3">
      <div className="space-y-1">
        <div className="shimmer h-3 w-16 rounded" />
        <div className="shimmer h-5 w-12 rounded" />
      </div>
      <div className="space-y-1">
        <div className="shimmer h-3 w-20 rounded" />
        <div className="shimmer h-5 w-16 rounded" />
      </div>
    </div>
    <div className="flex h-40 items-center justify-center">
      <div className="shimmer h-32 w-32 rounded-full" />
    </div>
    <div className="space-y-2">
      {[...Array.from({ length: rows }).keys()].map(i => (
        <div key={i} className="shimmer h-8 w-full rounded-lg" />
      ))}
    </div>
  </div>
);

// Error state component for consistent error handling
const ErrorState: React.FC<{ error: string | Error }> = ({ error }) => (
  <div className="flex flex-1 items-center justify-center">
    <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-center">
      <p className="text-sm text-red-600">
        {typeof error === 'string' ? error : error.message}
      </p>
    </div>
  </div>
);

/**
 * DashboardCard - A reusable card component for WellTrack dashboard
 *
 * Provides consistent layout structure, loading states, error handling,
 * and healthcare-themed styling for all dashboard components.
 *
 * @example
 * ```tsx
 * <DashboardCard
 *   title="Component Title"
 *   subtitle="Optional description"
 *   icon={IconComponent}
 *   badges={[{ text: 'Live', variant: 'outline', className: 'text-teal-700' }]}
 *   actions={[{ icon: RefreshCw, onClick: handleRefresh, disabled: isLoading }]}
 *   isLoading={isLoading}
 *   error={error}
 * >
 *   <YourContent />
 * </DashboardCard>
 * ```
 */
export const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  subtitle,
  icon: Icon,
  badges = [],
  actions = [],
  isLoading = false,
  error = null,
  className,
  children,
  loadingSkeletonRows = 3,
  headerClassName,
  contentClassName,
}) => {
  // Loading state
  if (isLoading) {
    return (
      <Card className={cn('flex h-full flex-col', className)}>
        <CardHeader
          className={cn(
            'flex flex-shrink-0 flex-row items-center justify-between space-y-0 pb-2',
            headerClassName,
          )}
        >
          <div className="flex flex-col space-y-1">
            <h3 className="flex items-center text-sm font-semibold text-gray-900">
              {Icon && <Icon className="mr-1 h-3 w-3" />}
              {title}
            </h3>
            {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
          </div>
          <div className="flex items-center space-x-1">
            <Badge variant="outline" className="text-xs">
              Loading...
            </Badge>
          </div>
        </CardHeader>
        <CardContent className={cn('flex-1 space-y-3', contentClassName)}>
          <LoadingSkeleton rows={loadingSkeletonRows} />
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className={cn('flex h-full flex-col', className)}>
        <CardHeader
          className={cn(
            'flex flex-shrink-0 flex-row items-center justify-between space-y-0 pb-2',
            headerClassName,
          )}
        >
          <div className="flex flex-col space-y-1">
            <h3 className="flex items-center text-sm font-semibold text-gray-900">
              {Icon && <Icon className="mr-1 h-3 w-3" />}
              {title}
            </h3>
            {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
          </div>
        </CardHeader>
        <CardContent
          className={cn(
            'flex flex-1 items-center justify-center',
            contentClassName,
          )}
        >
          <ErrorState error={error} />
        </CardContent>
      </Card>
    );
  }

  // Normal state
  return (
    <Card className={cn('flex h-full flex-col', className)}>
      <CardHeader
        className={cn(
          'flex flex-shrink-0 flex-row items-center justify-between space-y-0 pb-2',
          headerClassName,
        )}
      >
        <div className="flex flex-col space-y-1">
          <h3 className="flex items-center text-sm font-semibold text-gray-900">
            {Icon && <Icon className="mr-1 h-3 w-3" />}
            {title}
          </h3>
          {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
        </div>
        <div className="flex items-center space-x-1">
          {/* Render badges */}
          {badges.map((badge, index) => (
            <Badge
              key={`badge-${badge.text}-${index}`}
              variant={badge.variant || 'outline'}
              className={cn('text-xs', badge.className)}
            >
              {badge.text}
            </Badge>
          ))}

          {/* Render action buttons */}
          {actions.map((action, index) => (
            <Button
              key={`action-${action.tooltip || 'button'}-${index}`}
              variant="ghost"
              size="sm"
              onClick={action.onClick}
              disabled={action.disabled}
              className={cn(
                'h-6 w-6 p-0 text-gray-500 hover:text-gray-700',
                action.className,
              )}
              title={action.tooltip}
            >
              <action.icon
                className={cn(
                  'h-3 w-3',
                  action.disabled && action.icon === RefreshCw
                    ? 'animate-spin'
                    : '',
                )}
              />
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className={cn('min-h-0 flex-1', contentClassName)}>
        {children}
      </CardContent>
    </Card>
  );
};
