D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\libapp_lib-6eb7c7dc2e5c086c.rmeta: src\lib.rs src\api\mod.rs src\api\whatsapp.rs src\api\client.rs src\commands\mod.rs src\commands\whatsapp.rs src\commands\accounts.rs src\commands\messages.rs src\commands\stats.rs src\error.rs src\state\mod.rs D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\build\app-bc7c1a13f83bc500\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7 Cargo.toml

D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\app_lib-6eb7c7dc2e5c086c.d: src\lib.rs src\api\mod.rs src\api\whatsapp.rs src\api\client.rs src\commands\mod.rs src\commands\whatsapp.rs src\commands\accounts.rs src\commands\messages.rs src\commands\stats.rs src\error.rs src\state\mod.rs D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\build\app-bc7c1a13f83bc500\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7 Cargo.toml

src\lib.rs:
src\api\mod.rs:
src\api\whatsapp.rs:
src\api\client.rs:
src\commands\mod.rs:
src\commands\whatsapp.rs:
src\commands\accounts.rs:
src\commands\messages.rs:
src\commands\stats.rs:
src\error.rs:
src\state\mod.rs:
D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\build\app-bc7c1a13f83bc500\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:
Cargo.toml:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=app
# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
# env-dep:OUT_DIR=D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\app-bc7c1a13f83bc500\\out
