{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 7318463814424510623], [10755362358622467486, "build_script_build", false, 6488693282549871822], [3935545708480822364, "build_script_build", false, 9345695602248517529]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-bc7c1a13f83bc500\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}