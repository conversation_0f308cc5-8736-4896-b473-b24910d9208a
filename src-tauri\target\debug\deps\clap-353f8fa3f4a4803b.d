D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\libclap-353f8fa3f4a4803b.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.md

D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\clap-353f8fa3f4a4803b.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.md

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.md:
