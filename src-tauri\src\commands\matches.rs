use crate::api::{PaginationParams, WhatsAppApi};
use crate::error::AppError;
use crate::state::AppState;
use serde::{Deserialize, Serialize};
use tauri::State;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Match {
    pub id: i32,
    pub request_message_id: i32,
    pub offer_message_id: i32,
    pub medicine_id: i32,
    pub confidence_score: f64,
    pub status: String,
    pub matching_criteria: Option<String>,
    pub notes: Option<String>,
    pub created_at: String,
    pub updated_at: String,
    pub notified_at: Option<String>,
    pub completed_at: Option<String>,
    pub expires_at: Option<String>,
    pub medicine_name: String,
    pub requester_name: String,
    pub requester_jid: String,
    pub request_content: String,
    pub request_timestamp: String,
    pub offerer_name: String,
    pub offerer_jid: String,
    pub offer_content: String,
    pub offer_timestamp: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MatchingStats {
    pub total_matches: i32,
    pub recent_matches: i32,
    pub matches_by_status: std::collections::HashMap<String, i32>,
    pub average_confidence: f64,
    pub success_rate: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedMatches {
    pub matches: Vec<Match>,
    pub total: i32,
    pub limit: i32,
    pub offset: i32,
}

/// Get matches with pagination and filtering
#[tauri::command]
pub async fn get_matches(
    limit: Option<u32>,
    offset: Option<u32>,
    status: Option<String>,
    state: State<'_, AppState>,
) -> Result<PaginatedMatches, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());

    let mut endpoint = "/api/v1/matches?".to_string();

    if let Some(limit) = limit {
        endpoint.push_str(&format!("limit={}&", limit));
    }
    if let Some(offset) = offset {
        endpoint.push_str(&format!("offset={}&", offset));
    }
    if let Some(status) = status {
        endpoint.push_str(&format!("status={}&", status));
    }

    // Remove trailing &
    if endpoint.ends_with('&') {
        endpoint.pop();
    }

    #[derive(Deserialize)]
    struct Response {
        matches: Vec<Match>,
        total: i32,
        limit: i32,
        offset: i32,
    }

    let response: Response = api.get(&endpoint).await.map_err(|e| e.to_string())?;

    Ok(PaginatedMatches {
        matches: response.matches,
        total: response.total,
        limit: response.limit,
        offset: response.offset,
    })
}

/// Get a specific match by ID
#[tauri::command]
pub async fn get_match(match_id: i32, state: State<'_, AppState>) -> Result<Match, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    let endpoint = format!("/api/v1/matches/{}", match_id);

    #[derive(Deserialize)]
    struct Response {
        #[serde(rename = "match")]
        match_data: Match,
    }

    let response: Response = api.get(&endpoint).await.map_err(|e| e.to_string())?;
    Ok(response.match_data)
}

/// Update match status
#[tauri::command]
pub async fn update_match_status(
    match_id: i32,
    status: String,
    notes: Option<String>,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    let endpoint = format!("/api/v1/matches/{}/status", match_id);

    #[derive(Serialize)]
    struct Request {
        status: String,
        notes: Option<String>,
    }

    #[derive(Deserialize)]
    struct Response {
        message: String,
    }

    let request = Request { status, notes };
    let response: Response = api
        .put(&endpoint, &request)
        .await
        .map_err(|e| e.to_string())?;
    Ok(response.message)
}

/// Run manual matching process
#[tauri::command]
pub async fn run_manual_matching(state: State<'_, AppState>) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    let endpoint = "/api/v1/matches/run";

    #[derive(Deserialize)]
    struct Response {
        message: String,
    }

    let response: Response = api.post(&endpoint, &()).await.map_err(|e| e.to_string())?;
    Ok(response.message)
}

/// Get matching statistics
#[tauri::command]
pub async fn get_matching_stats(state: State<'_, AppState>) -> Result<MatchingStats, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.get("/api/v1/matches/stats")
        .await
        .map_err(|e| e.to_string())
}
