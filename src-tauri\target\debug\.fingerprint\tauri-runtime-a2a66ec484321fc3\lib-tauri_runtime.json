{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 18316066009122804046, "deps": [[442785307232013896, "build_script_build", false, 1315052582861037539], [3150220818285335163, "url", false, 6530261799351731353], [4143744114649553716, "raw_window_handle", false, 3850038477068984947], [7606335748176206944, "dpi", false, 14102429171621091192], [9010263965687315507, "http", false, 3354175764241350141], [9689903380558560274, "serde", false, 10996426815917291500], [10806645703491011684, "thiserror", false, 44651847147331727], [11050281405049894993, "tauri_utils", false, 5597325593669197443], [13116089016666501665, "windows", false, 9661768058793115304], [15367738274754116744, "serde_json", false, 1559938316259437281], [16727543399706004146, "cookie", false, 6193473348559863361]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-a2a66ec484321fc3\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}