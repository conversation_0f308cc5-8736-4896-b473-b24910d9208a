package interfaces

import (
	"context"

	"medibridge-whatsapp/internal/database"
	"medibridge-whatsapp/internal/matching"
	"medibridge-whatsapp/internal/whatsapp"
)

// DatabaseInterface defines the interface for database operations
type DatabaseInterface interface {
	Query(query string, args ...interface{}) (*database.Rows, error)
	QueryRow(query string, args ...interface{}) *database.Row
	Exec(query string, args ...interface{}) (database.Result, error)
}

// WhatsAppManagerInterface defines the interface for WhatsApp operations
type WhatsAppManagerInterface interface {
	GetClients() []whatsapp.Client
	ConnectClient(accountID string) error
	DisconnectClient(accountID string) error
	GetQRCode(accountID string) (string, error)
	Shutdown()
}

// AIProcessorInterface defines the interface for AI processing
type AIProcessorInterface interface {
	ProcessMessage(messageID int) error
	Start(ctx context.Context)
}

// MatchingServiceInterface defines the interface for matching operations
type MatchingServiceInterface interface {
	GetMatches(limit, offset int, status string) ([]matching.MatchDetails, int, error)
	GetMatchDetails(matchID int) (*matching.MatchDetails, error)
	UpdateMatchStatus(matchID int, status string, notes *string) error
	RunManualMatching(ctx context.Context) error
	GetMatchingStats() (*matching.MatchingStats, error)
}
