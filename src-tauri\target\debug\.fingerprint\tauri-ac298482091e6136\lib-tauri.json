{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 7780054017505697778, "deps": [[40386456601120721, "percent_encoding", false, 10973999686076924311], [442785307232013896, "tauri_runtime", false, 6688998139154510724], [1200537532907108615, "url<PERSON><PERSON>n", false, 17140344150239503574], [3150220818285335163, "url", false, 3569982634382302943], [4143744114649553716, "raw_window_handle", false, 8084507311091100797], [4341921533227644514, "muda", false, 14632011621990221427], [4919829919303820331, "serialize_to_javascript", false, 12014772394534080304], [5986029879202738730, "log", false, 14504179726639124161], [7752760652095876438, "tauri_runtime_wry", false, 11734048255791257082], [8539587424388551196, "webview2_com", false, 16629564564922351208], [9010263965687315507, "http", false, 15283923244554771932], [9228235415475680086, "tauri_macros", false, 11074126761809796821], [9538054652646069845, "tokio", false, 15578497443310246981], [9689903380558560274, "serde", false, 1190822165864209379], [9920160576179037441, "getrandom", false, 5721414032010686405], [10229185211513642314, "mime", false, 1056189065056296338], [10629569228670356391, "futures_util", false, 5770148779909698394], [10755362358622467486, "build_script_build", false, 6488693282549871822], [10806645703491011684, "thiserror", false, 3797447290076662653], [11050281405049894993, "tauri_utils", false, 11882863560368240684], [11989259058781683633, "dunce", false, 6805626572801907050], [12565293087094287914, "window_vibrancy", false, 17620885371698229358], [12986574360607194341, "serde_repr", false, 11609501881873135158], [13077543566650298139, "heck", false, 1474043532582966568], [13116089016666501665, "windows", false, 6388814616047702111], [13625485746686963219, "anyhow", false, 13018930408720609840], [15367738274754116744, "serde_json", false, 13370400617386028636], [16928111194414003569, "dirs", false, 4614596750184675280], [17155886227862585100, "glob", false, 13970887638437548295]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-ac298482091e6136\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}