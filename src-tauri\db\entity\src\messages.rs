use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "messages")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    
    /// WhatsApp message ID
    #[sea_orm(unique)]
    pub whatsapp_message_id: String,
    
    /// Account ID that received this message
    pub account_id: i32,
    
    /// Group JID (WhatsApp group identifier)
    pub group_jid: String,
    
    /// Group name
    pub group_name: String,
    
    /// Sender JID (WhatsApp user identifier)
    pub sender_jid: String,
    
    /// Sender name/display name
    pub sender_name: String,
    
    /// Message content/text
    pub content: String,
    
    /// Message type: request, offer, unknown
    pub message_type: String,
    
    /// Processing status: pending, processed, failed
    pub processing_status: String,
    
    /// AI analysis result (JSON)
    pub ai_analysis: Option<String>,
    
    /// Error message if processing failed
    pub error_message: Option<String>,
    
    /// Message timestamp from WhatsApp
    pub message_timestamp: DateTime,
    
    /// When the message was received by our system
    pub received_at: DateTime,
    
    /// When the message was processed by AI
    pub processed_at: Option<DateTime>,
    
    /// Record creation timestamp
    pub created_at: DateTime,
    
    /// Last update timestamp
    pub updated_at: DateTime,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::accounts::Entity",
        from = "Column::AccountId",
        to = "super::accounts::Column::Id"
    )]
    Account,
    
    #[sea_orm(has_many = "super::message_medicines::Entity")]
    MessageMedicines,
}

impl Related<super::accounts::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Account.def()
    }
}

impl Related<super::message_medicines::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MessageMedicines.def()
    }
}

impl Related<super::medicines::Entity> for Entity {
    fn to() -> RelationDef {
        super::message_medicines::Relation::Medicine.def()
    }
    
    fn via() -> Option<RelationDef> {
        Some(super::message_medicines::Relation::Message.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Message type enum
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MessageType {
    Request,  // طلب دواء
    Offer,    // عرض دواء
    Unknown,  // غير محدد
}

impl std::fmt::Display for MessageType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MessageType::Request => write!(f, "request"),
            MessageType::Offer => write!(f, "offer"),
            MessageType::Unknown => write!(f, "unknown"),
        }
    }
}

impl From<String> for MessageType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "request" => MessageType::Request,
            "offer" => MessageType::Offer,
            "unknown" => MessageType::Unknown,
            _ => MessageType::Unknown,
        }
    }
}

// Processing status enum
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProcessingStatus {
    Pending,
    Processed,
    Failed,
}

impl std::fmt::Display for ProcessingStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ProcessingStatus::Pending => write!(f, "pending"),
            ProcessingStatus::Processed => write!(f, "processed"),
            ProcessingStatus::Failed => write!(f, "failed"),
        }
    }
}

impl From<String> for ProcessingStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "pending" => ProcessingStatus::Pending,
            "processed" => ProcessingStatus::Processed,
            "failed" => ProcessingStatus::Failed,
            _ => ProcessingStatus::Pending,
        }
    }
}
