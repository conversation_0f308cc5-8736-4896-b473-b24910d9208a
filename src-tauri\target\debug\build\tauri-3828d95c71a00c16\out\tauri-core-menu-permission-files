["\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\tauri-3828d95c71a00c16\\out\\permissions\\menu\\autogenerated\\default.toml"]