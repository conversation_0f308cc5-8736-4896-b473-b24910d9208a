package matching

import (
	"context"
	"time"

	"go.uber.org/zap"

	"medibridge-whatsapp/internal/database"
)

// Service handles periodic matching operations
type Service struct {
	logger  *zap.Logger
	db      *database.DB
	engine  *MatchingEngine
	ticker  *time.Ticker
	done    chan struct{}
	running bool
}

// NewService creates a new matching service
func NewService(logger *zap.Logger, db *database.DB, interval time.Duration) *Service {
	engine := NewMatchingEngine(logger, db)
	
	return &Service{
		logger: logger,
		db:     db,
		engine: engine,
		ticker: time.NewTicker(interval),
		done:   make(chan struct{}),
	}
}

// Start begins the periodic matching process
func (s *Service) Start(ctx context.Context) {
	if s.running {
		s.logger.Warn("Matching service is already running")
		return
	}

	s.running = true
	s.logger.Info("Starting matching service")

	// Run initial matching
	go func() {
		if err := s.engine.FindMatches(ctx); err != nil {
			s.logger.Error("Initial matching failed", zap.Error(err))
		}
	}()

	// Start periodic matching
	go s.run(ctx)
}

// Stop stops the matching service
func (s *Service) Stop() {
	if !s.running {
		return
	}

	s.logger.Info("Stopping matching service")
	s.running = false
	s.ticker.Stop()
	close(s.done)
}

// run executes the periodic matching loop
func (s *Service) run(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Matching service stopped due to context cancellation")
			return
		case <-s.done:
			s.logger.Info("Matching service stopped")
			return
		case <-s.ticker.C:
			s.logger.Debug("Running periodic matching")
			
			// Run matching process
			if err := s.engine.FindMatches(ctx); err != nil {
				s.logger.Error("Periodic matching failed", zap.Error(err))
			}
			
			// Cleanup expired matches
			if err := s.engine.CleanupExpiredMatches(); err != nil {
				s.logger.Error("Failed to cleanup expired matches", zap.Error(err))
			}
		}
	}
}

// RunManualMatching triggers a manual matching process
func (s *Service) RunManualMatching(ctx context.Context) error {
	s.logger.Info("Running manual matching")
	return s.engine.FindMatches(ctx)
}

// GetMatchingStats returns statistics about the matching process
func (s *Service) GetMatchingStats() (*MatchingStats, error) {
	stats := &MatchingStats{}

	// Get total matches
	err := s.db.QueryRow("SELECT COUNT(*) FROM matches").Scan(&stats.TotalMatches)
	if err != nil {
		return nil, err
	}

	// Get matches by status
	statusQuery := `
		SELECT status, COUNT(*) 
		FROM matches 
		GROUP BY status
	`
	rows, err := s.db.Query(statusQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	stats.MatchesByStatus = make(map[string]int)
	for rows.Next() {
		var status string
		var count int
		if err := rows.Scan(&status, &count); err != nil {
			continue
		}
		stats.MatchesByStatus[status] = count
	}

	// Get recent matches (last 24 hours)
	err = s.db.QueryRow(`
		SELECT COUNT(*) FROM matches 
		WHERE created_at > NOW() - INTERVAL '24 hours'
	`).Scan(&stats.RecentMatches)
	if err != nil {
		stats.RecentMatches = 0
	}

	// Get average confidence score
	err = s.db.QueryRow(`
		SELECT AVG(confidence_score) FROM matches 
		WHERE status != 'expired'
	`).Scan(&stats.AverageConfidence)
	if err != nil {
		stats.AverageConfidence = 0.0
	}

	// Get success rate (completed matches / total matches)
	var completedMatches int
	err = s.db.QueryRow(`
		SELECT COUNT(*) FROM matches WHERE status = 'completed'
	`).Scan(&completedMatches)
	if err != nil {
		completedMatches = 0
	}

	if stats.TotalMatches > 0 {
		stats.SuccessRate = float64(completedMatches) / float64(stats.TotalMatches)
	}

	return stats, nil
}

// MatchingStats represents statistics about the matching process
type MatchingStats struct {
	TotalMatches       int             `json:"total_matches"`
	RecentMatches      int             `json:"recent_matches"`
	MatchesByStatus    map[string]int  `json:"matches_by_status"`
	AverageConfidence  float64         `json:"average_confidence"`
	SuccessRate        float64         `json:"success_rate"`
}

// GetMatchDetails returns detailed information about a specific match
func (s *Service) GetMatchDetails(matchID int) (*MatchDetails, error) {
	query := `
		SELECT 
			m.id, m.request_message_id, m.offer_message_id, m.medicine_id,
			m.confidence_score, m.status, m.matching_criteria, m.notes,
			m.created_at, m.updated_at, m.notified_at, m.completed_at, m.expires_at,
			med.name as medicine_name,
			req.sender_name as requester_name, req.sender_jid as requester_jid,
			req.content as request_content, req.message_timestamp as request_timestamp,
			off.sender_name as offerer_name, off.sender_jid as offerer_jid,
			off.content as offer_content, off.message_timestamp as offer_timestamp
		FROM matches m
		JOIN medicines med ON m.medicine_id = med.id
		JOIN messages req ON m.request_message_id = req.id
		JOIN messages off ON m.offer_message_id = off.id
		WHERE m.id = $1
	`

	details := &MatchDetails{}
	err := s.db.QueryRow(query, matchID).Scan(
		&details.ID, &details.RequestMessageID, &details.OfferMessageID, &details.MedicineID,
		&details.ConfidenceScore, &details.Status, &details.MatchingCriteria, &details.Notes,
		&details.CreatedAt, &details.UpdatedAt, &details.NotifiedAt, &details.CompletedAt, &details.ExpiresAt,
		&details.MedicineName,
		&details.RequesterName, &details.RequesterJID,
		&details.RequestContent, &details.RequestTimestamp,
		&details.OffererName, &details.OffererJID,
		&details.OfferContent, &details.OfferTimestamp,
	)

	if err != nil {
		return nil, err
	}

	return details, nil
}

// MatchDetails represents detailed information about a match
type MatchDetails struct {
	ID                int        `json:"id"`
	RequestMessageID  int        `json:"request_message_id"`
	OfferMessageID    int        `json:"offer_message_id"`
	MedicineID        int        `json:"medicine_id"`
	ConfidenceScore   float64    `json:"confidence_score"`
	Status            string     `json:"status"`
	MatchingCriteria  *string    `json:"matching_criteria"`
	Notes             *string    `json:"notes"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	NotifiedAt        *time.Time `json:"notified_at"`
	CompletedAt       *time.Time `json:"completed_at"`
	ExpiresAt         *time.Time `json:"expires_at"`
	MedicineName      string     `json:"medicine_name"`
	RequesterName     string     `json:"requester_name"`
	RequesterJID      string     `json:"requester_jid"`
	RequestContent    string     `json:"request_content"`
	RequestTimestamp  time.Time  `json:"request_timestamp"`
	OffererName       string     `json:"offerer_name"`
	OffererJID        string     `json:"offerer_jid"`
	OfferContent      string     `json:"offer_content"`
	OfferTimestamp    time.Time  `json:"offer_timestamp"`
}

// UpdateMatchStatus updates the status of a match
func (s *Service) UpdateMatchStatus(matchID int, status string, notes *string) error {
	var query string
	var args []interface{}

	switch status {
	case "notified":
		query = `
			UPDATE matches 
			SET status = $1, notified_at = NOW(), updated_at = NOW()
			WHERE id = $2
		`
		args = []interface{}{status, matchID}
	case "completed":
		query = `
			UPDATE matches 
			SET status = $1, completed_at = NOW(), updated_at = NOW()
			WHERE id = $2
		`
		args = []interface{}{status, matchID}
	default:
		query = `
			UPDATE matches 
			SET status = $1, updated_at = NOW()
			WHERE id = $2
		`
		args = []interface{}{status, matchID}
	}

	if notes != nil {
		query = `
			UPDATE matches 
			SET status = $1, notes = $2, updated_at = NOW()
			WHERE id = $3
		`
		args = []interface{}{status, *notes, matchID}
	}

	_, err := s.db.Exec(query, args...)
	if err != nil {
		return err
	}

	s.logger.Info("Match status updated",
		zap.Int("match_id", matchID),
		zap.String("status", status))

	return nil
}

// GetMatches returns a list of matches with pagination and filtering
func (s *Service) GetMatches(limit, offset int, status string) ([]MatchDetails, int, error) {
	var whereClause string
	var args []interface{}
	argIndex := 1

	if status != "" && status != "all" {
		whereClause = "WHERE m.status = $" + string(rune('0'+argIndex))
		args = append(args, status)
		argIndex++
	}

	// Get total count
	countQuery := `
		SELECT COUNT(*) FROM matches m
		JOIN medicines med ON m.medicine_id = med.id
		JOIN messages req ON m.request_message_id = req.id
		JOIN messages off ON m.offer_message_id = off.id
		` + whereClause

	var total int
	err := s.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// Get matches
	query := `
		SELECT 
			m.id, m.request_message_id, m.offer_message_id, m.medicine_id,
			m.confidence_score, m.status, m.matching_criteria, m.notes,
			m.created_at, m.updated_at, m.notified_at, m.completed_at, m.expires_at,
			med.name as medicine_name,
			req.sender_name as requester_name, req.sender_jid as requester_jid,
			req.content as request_content, req.message_timestamp as request_timestamp,
			off.sender_name as offerer_name, off.sender_jid as offerer_jid,
			off.content as offer_content, off.message_timestamp as offer_timestamp
		FROM matches m
		JOIN medicines med ON m.medicine_id = med.id
		JOIN messages req ON m.request_message_id = req.id
		JOIN messages off ON m.offer_message_id = off.id
		` + whereClause + `
		ORDER BY m.created_at DESC
		LIMIT $` + string(rune('0'+argIndex)) + ` OFFSET $` + string(rune('0'+argIndex+1))

	args = append(args, limit, offset)

	rows, err := s.db.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var matches []MatchDetails
	for rows.Next() {
		details := MatchDetails{}
		err := rows.Scan(
			&details.ID, &details.RequestMessageID, &details.OfferMessageID, &details.MedicineID,
			&details.ConfidenceScore, &details.Status, &details.MatchingCriteria, &details.Notes,
			&details.CreatedAt, &details.UpdatedAt, &details.NotifiedAt, &details.CompletedAt, &details.ExpiresAt,
			&details.MedicineName,
			&details.RequesterName, &details.RequesterJID,
			&details.RequestContent, &details.RequestTimestamp,
			&details.OffererName, &details.OffererJID,
			&details.OfferContent, &details.OfferTimestamp,
		)
		if err != nil {
			continue
		}
		matches = append(matches, details)
	}

	return matches, total, nil
}
