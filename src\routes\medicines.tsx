import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { Search, Pill, TrendingUp, TrendingDown, Plus, Edit, BarChart3 } from 'lucide-react'

interface Medicine {
  id: number
  name: string
  alternative_names?: string
  category?: string
  description?: string
  active_ingredient?: string
  manufacturer?: string
  dosage_form?: string
  strength?: string
  mention_count: number
  request_count: number
  offer_count: number
  created_at: string
  updated_at: string
  last_mentioned_at?: string
}

export const Route = createFileRoute('/medicines')({
  component: MedicinesPage,
})

function MedicinesPage() {
  const [medicines, setMedicines] = useState<Medicine[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('mention_count')
  const [selectedMedicine, setSelectedMedicine] = useState<Medicine | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)

  // Mock data - في التطبيق الحقيقي سيتم جلب البيانات من API
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockMedicines: Medicine[] = [
        {
          id: 1,
          name: "باراسيتامول",
          alternative_names: '["أدول", "بانادول", "سيتال"]',
          category: "painkiller",
          description: "مسكن للألم وخافض للحرارة",
          active_ingredient: "Paracetamol",
          manufacturer: "شركة الأدوية المصرية",
          dosage_form: "أقراص",
          strength: "500mg",
          mention_count: 45,
          request_count: 30,
          offer_count: 15,
          created_at: "2024-01-15T10:00:00Z",
          updated_at: "2024-01-20T15:30:00Z",
          last_mentioned_at: "2024-01-20T15:30:00Z"
        },
        {
          id: 2,
          name: "أموكسيسيلين",
          alternative_names: '["أوجمنتين", "كلافوكس"]',
          category: "antibiotic",
          description: "مضاد حيوي واسع المجال",
          active_ingredient: "Amoxicillin",
          manufacturer: "شركة جلاكسو",
          dosage_form: "كبسولات",
          strength: "500mg",
          mention_count: 32,
          request_count: 25,
          offer_count: 7,
          created_at: "2024-01-10T08:00:00Z",
          updated_at: "2024-01-18T12:00:00Z",
          last_mentioned_at: "2024-01-18T12:00:00Z"
        },
        {
          id: 3,
          name: "فيتامين د",
          alternative_names: '["ديفارول", "فيدروب"]',
          category: "vitamin",
          description: "فيتامين د3 لتقوية العظام",
          active_ingredient: "Cholecalciferol",
          manufacturer: "شركة الفا",
          dosage_form: "نقط",
          strength: "400 IU/ml",
          mention_count: 28,
          request_count: 20,
          offer_count: 8,
          created_at: "2024-01-12T09:00:00Z",
          updated_at: "2024-01-19T14:00:00Z",
          last_mentioned_at: "2024-01-19T14:00:00Z"
        }
      ]
      setMedicines(mockMedicines)
      setLoading(false)
    }, 1000)
  }, [])

  const getCategoryBadge = (category?: string) => {
    const categoryMap = {
      antibiotic: { label: 'مضاد حيوي', variant: 'destructive' as const },
      painkiller: { label: 'مسكن', variant: 'default' as const },
      vitamin: { label: 'فيتامين', variant: 'secondary' as const },
      supplement: { label: 'مكمل غذائي', variant: 'outline' as const },
      chronic: { label: 'أمراض مزمنة', variant: 'default' as const },
      emergency: { label: 'طوارئ', variant: 'destructive' as const },
      pediatric: { label: 'أطفال', variant: 'secondary' as const },
      other: { label: 'أخرى', variant: 'outline' as const }
    }
    
    const categoryInfo = categoryMap[category as keyof typeof categoryMap] || { label: category || 'غير محدد', variant: 'outline' as const }
    return <Badge variant={categoryInfo.variant}>{categoryInfo.label}</Badge>
  }

  const filteredMedicines = medicines
    .filter(medicine => {
      const matchesSearch = searchTerm === '' || 
        medicine.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (medicine.alternative_names && medicine.alternative_names.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (medicine.active_ingredient && medicine.active_ingredient.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesCategory = categoryFilter === 'all' || medicine.category === categoryFilter
      
      return matchesSearch && matchesCategory
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'mention_count':
          return b.mention_count - a.mention_count
        case 'request_count':
          return b.request_count - a.request_count
        case 'offer_count':
          return b.offer_count - a.offer_count
        case 'name':
          return a.name.localeCompare(b.name, 'ar')
        case 'last_mentioned':
          return new Date(b.last_mentioned_at || 0).getTime() - new Date(a.last_mentioned_at || 0).getTime()
        default:
          return 0
      }
    })

  const showMedicineDetails = (medicine: Medicine) => {
    setSelectedMedicine(medicine)
    setIsDetailDialogOpen(true)
  }

  const getAlternativeNames = (alternativeNames?: string) => {
    if (!alternativeNames) return []
    try {
      return JSON.parse(alternativeNames)
    } catch {
      return []
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">الأدوية</h1>
          <p className="text-muted-foreground">إدارة قاعدة بيانات الأدوية المكتشفة</p>
        </div>
        
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          إضافة دواء جديد
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الأدوية</CardTitle>
            <Pill className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{medicines.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأكثر طلباً</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.max(...medicines.map(m => m.request_count), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {medicines.find(m => m.request_count === Math.max(...medicines.map(m => m.request_count)))?.name}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأكثر عرضاً</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.max(...medicines.map(m => m.offer_count), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {medicines.find(m => m.offer_count === Math.max(...medicines.map(m => m.offer_count)))?.name}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الذكر</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {medicines.reduce((sum, m) => sum + m.mention_count, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>البحث والفلترة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="البحث في الأدوية..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="الفئة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفئات</SelectItem>
                <SelectItem value="antibiotic">مضاد حيوي</SelectItem>
                <SelectItem value="painkiller">مسكن</SelectItem>
                <SelectItem value="vitamin">فيتامين</SelectItem>
                <SelectItem value="supplement">مكمل غذائي</SelectItem>
                <SelectItem value="chronic">أمراض مزمنة</SelectItem>
                <SelectItem value="emergency">طوارئ</SelectItem>
                <SelectItem value="pediatric">أطفال</SelectItem>
                <SelectItem value="other">أخرى</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="ترتيب حسب" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mention_count">الأكثر ذكراً</SelectItem>
                <SelectItem value="request_count">الأكثر طلباً</SelectItem>
                <SelectItem value="offer_count">الأكثر عرضاً</SelectItem>
                <SelectItem value="name">الاسم</SelectItem>
                <SelectItem value="last_mentioned">آخر ذكر</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Medicines Table */}
      <Card>
        <CardHeader>
          <CardTitle>الأدوية ({filteredMedicines.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم</TableHead>
                <TableHead>الفئة</TableHead>
                <TableHead>المادة الفعالة</TableHead>
                <TableHead>عدد الذكر</TableHead>
                <TableHead>الطلبات</TableHead>
                <TableHead>العروض</TableHead>
                <TableHead>آخر ذكر</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMedicines.map((medicine) => (
                <TableRow key={medicine.id}>
                  <TableCell className="font-medium">{medicine.name}</TableCell>
                  <TableCell>{getCategoryBadge(medicine.category)}</TableCell>
                  <TableCell>{medicine.active_ingredient || '-'}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{medicine.mention_count}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="default">{medicine.request_count}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{medicine.offer_count}</Badge>
                  </TableCell>
                  <TableCell>
                    {medicine.last_mentioned_at 
                      ? new Date(medicine.last_mentioned_at).toLocaleDateString('ar-EG')
                      : '-'
                    }
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => showMedicineDetails(medicine)}
                      >
                        عرض
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredMedicines.length === 0 && (
            <div className="text-center py-8">
              <Pill className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">لا توجد أدوية</h3>
              <p className="text-muted-foreground">لم يتم العثور على أدوية تطابق المعايير المحددة</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Medicine Details Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>تفاصيل الدواء</DialogTitle>
            <DialogDescription>
              معلومات مفصلة عن الدواء وإحصائيات الاستخدام
            </DialogDescription>
          </DialogHeader>
          
          {selectedMedicine && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>اسم الدواء</Label>
                  <p className="text-sm font-medium">{selectedMedicine.name}</p>
                </div>
                <div>
                  <Label>الفئة</Label>
                  <div className="mt-1">{getCategoryBadge(selectedMedicine.category)}</div>
                </div>
                <div>
                  <Label>المادة الفعالة</Label>
                  <p className="text-sm text-muted-foreground">{selectedMedicine.active_ingredient || '-'}</p>
                </div>
                <div>
                  <Label>الشركة المصنعة</Label>
                  <p className="text-sm text-muted-foreground">{selectedMedicine.manufacturer || '-'}</p>
                </div>
                <div>
                  <Label>الشكل الصيدلي</Label>
                  <p className="text-sm text-muted-foreground">{selectedMedicine.dosage_form || '-'}</p>
                </div>
                <div>
                  <Label>التركيز</Label>
                  <p className="text-sm text-muted-foreground">{selectedMedicine.strength || '-'}</p>
                </div>
              </div>
              
              {selectedMedicine.description && (
                <div>
                  <Label>الوصف</Label>
                  <p className="text-sm text-muted-foreground mt-1">{selectedMedicine.description}</p>
                </div>
              )}
              
              {getAlternativeNames(selectedMedicine.alternative_names).length > 0 && (
                <div>
                  <Label>الأسماء البديلة</Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {getAlternativeNames(selectedMedicine.alternative_names).map((name: string, index: number) => (
                      <Badge key={index} variant="outline">{name}</Badge>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{selectedMedicine.mention_count}</div>
                  <div className="text-xs text-muted-foreground">إجمالي الذكر</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{selectedMedicine.request_count}</div>
                  <div className="text-xs text-muted-foreground">الطلبات</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{selectedMedicine.offer_count}</div>
                  <div className="text-xs text-muted-foreground">العروض</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground pt-4 border-t">
                <div>
                  <Label>تاريخ الإضافة</Label>
                  <p>{new Date(selectedMedicine.created_at).toLocaleString('ar-EG')}</p>
                </div>
                {selectedMedicine.last_mentioned_at && (
                  <div>
                    <Label>آخر ذكر</Label>
                    <p>{new Date(selectedMedicine.last_mentioned_at).toLocaleString('ar-EG')}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
