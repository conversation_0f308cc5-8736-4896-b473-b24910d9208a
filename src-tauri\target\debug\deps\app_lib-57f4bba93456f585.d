D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\libapp_lib-57f4bba93456f585.rmeta: src\lib.rs src\state\mod.rs src\error.rs D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\build\app-f806ea9ede6193a0\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\app_lib-57f4bba93456f585.d: src\lib.rs src\state\mod.rs src\error.rs D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\build\app-f806ea9ede6193a0\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
src\state\mod.rs:
src\error.rs:
D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\build\app-f806ea9ede6193a0\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=app
# env-dep:OUT_DIR=D:\\programming\\desktop-apps\\MediBridge\\src-tauri\\target\\debug\\build\\app-f806ea9ede6193a0\\out
