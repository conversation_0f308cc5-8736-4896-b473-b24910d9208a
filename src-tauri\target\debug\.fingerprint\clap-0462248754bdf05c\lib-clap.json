{"rustc": 16591470773350601817, "features": "[\"color\", \"default\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 11439587820120798860, "path": 10675380123599034488, "deps": [[8750560705953570236, "clap_builder", false, 4277896134812340757], [17056525256108235978, "clap_derive", false, 1983651248890681782]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-0462248754bdf05c\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}