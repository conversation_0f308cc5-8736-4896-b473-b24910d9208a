import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Bell, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  X,
  Clock,
  Wifi,
  WifiOff,
  Database,
  Bot
} from 'lucide-react'

export type AlertType = 'info' | 'warning' | 'error' | 'success'
export type AlertCategory = 'system' | 'whatsapp' | 'ai' | 'matching' | 'database'

export interface Alert {
  id: string
  type: AlertType
  category: AlertCategory
  title: string
  message: string
  timestamp: Date
  read: boolean
  actionable?: boolean
  action?: () => void
  actionLabel?: string
}

interface AlertCenterProps {
  alerts?: Alert[]
  onMarkAsRead?: (alertId: string) => void
  onDismiss?: (alertId: string) => void
  onClearAll?: () => void
}

export function AlertCenter({ 
  alerts = [], 
  onMarkAsRead, 
  onDismiss, 
  onClearAll 
}: AlertCenterProps) {
  const [localAlerts, setLocalAlerts] = useState<Alert[]>(alerts)

  useEffect(() => {
    setLocalAlerts(alerts)
  }, [alerts])

  const unreadCount = localAlerts.filter(alert => !alert.read).length

  const getAlertIcon = (type: AlertType) => {
    switch (type) {
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      default:
        return <Info className="w-4 h-4 text-blue-500" />
    }
  }

  const getCategoryIcon = (category: AlertCategory) => {
    switch (category) {
      case 'whatsapp':
        return <Wifi className="w-3 h-3" />
      case 'ai':
        return <Bot className="w-3 h-3" />
      case 'database':
        return <Database className="w-3 h-3" />
      default:
        return <Bell className="w-3 h-3" />
    }
  }

  const getAlertBadgeVariant = (type: AlertType) => {
    switch (type) {
      case 'error':
        return 'destructive' as const
      case 'warning':
        return 'outline' as const
      case 'success':
        return 'default' as const
      default:
        return 'secondary' as const
    }
  }

  const handleMarkAsRead = (alertId: string) => {
    setLocalAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId ? { ...alert, read: true } : alert
      )
    )
    onMarkAsRead?.(alertId)
  }

  const handleDismiss = (alertId: string) => {
    setLocalAlerts(prev => prev.filter(alert => alert.id !== alertId))
    onDismiss?.(alertId)
  }

  const handleClearAll = () => {
    setLocalAlerts([])
    onClearAll?.()
  }

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return 'الآن'
    if (minutes < 60) return `منذ ${minutes} دقيقة`
    if (hours < 24) return `منذ ${hours} ساعة`
    return `منذ ${days} يوم`
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell className="w-5 h-5" />
            <CardTitle>مركز التنبيهات</CardTitle>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadCount}
              </Badge>
            )}
          </div>
          
          {localAlerts.length > 0 && (
            <Button variant="ghost" size="sm" onClick={handleClearAll}>
              مسح الكل
            </Button>
          )}
        </div>
        <CardDescription>
          آخر التنبيهات والإشعارات من النظام
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {localAlerts.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>لا توجد تنبيهات جديدة</p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {localAlerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border transition-all duration-200 ${
                    alert.read 
                      ? 'bg-muted/50 border-muted' 
                      : 'bg-background border-border shadow-sm'
                  }`}
                >
                  <div className="flex items-start justify-between space-x-2">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="flex-shrink-0 mt-0.5">
                        {getAlertIcon(alert.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className={`text-sm font-medium ${
                            alert.read ? 'text-muted-foreground' : 'text-foreground'
                          }`}>
                            {alert.title}
                          </h4>
                          
                          <Badge 
                            variant={getAlertBadgeVariant(alert.type)}
                            className="text-xs flex items-center space-x-1"
                          >
                            {getCategoryIcon(alert.category)}
                            <span>{alert.category}</span>
                          </Badge>
                        </div>
                        
                        <p className={`text-sm ${
                          alert.read ? 'text-muted-foreground' : 'text-muted-foreground'
                        }`}>
                          {alert.message}
                        </p>
                        
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <Clock className="w-3 h-3" />
                            <span>{formatTimestamp(alert.timestamp)}</span>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            {alert.actionable && alert.action && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={alert.action}
                                className="text-xs h-6"
                              >
                                {alert.actionLabel || 'إجراء'}
                              </Button>
                            )}
                            
                            {!alert.read && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleMarkAsRead(alert.id)}
                                className="text-xs h-6"
                              >
                                تم القراءة
                              </Button>
                            )}
                            
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleDismiss(alert.id)}
                              className="text-xs h-6 p-1"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}

// Hook for managing alerts
export function useAlerts() {
  const [alerts, setAlerts] = useState<Alert[]>([])

  const addAlert = (alert: Omit<Alert, 'id' | 'timestamp' | 'read'>) => {
    const newAlert: Alert = {
      ...alert,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      read: false
    }
    setAlerts(prev => [newAlert, ...prev])
  }

  const markAsRead = (alertId: string) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId ? { ...alert, read: true } : alert
      )
    )
  }

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId))
  }

  const clearAll = () => {
    setAlerts([])
  }

  // Generate sample alerts for demo
  const generateSampleAlerts = () => {
    const sampleAlerts: Alert[] = [
      {
        id: '1',
        type: 'success',
        category: 'matching',
        title: 'مطابقة جديدة',
        message: 'تم العثور على 3 مطابقات جديدة لطلبات الأدوية',
        timestamp: new Date(Date.now() - 5 * 60000),
        read: false,
        actionable: true,
        actionLabel: 'عرض المطابقات'
      },
      {
        id: '2',
        type: 'warning',
        category: 'whatsapp',
        title: 'انقطاع الاتصال',
        message: 'تم فقدان الاتصال مع حساب WhatsApp مؤقتاً',
        timestamp: new Date(Date.now() - 15 * 60000),
        read: false,
        actionable: true,
        actionLabel: 'إعادة الاتصال'
      },
      {
        id: '3',
        type: 'info',
        category: 'ai',
        title: 'معالجة مكتملة',
        message: 'تم معالجة 25 رسالة جديدة بنجاح',
        timestamp: new Date(Date.now() - 30 * 60000),
        read: true
      },
      {
        id: '4',
        type: 'error',
        category: 'database',
        title: 'خطأ في قاعدة البيانات',
        message: 'فشل في حفظ بعض البيانات، يرجى المحاولة مرة أخرى',
        timestamp: new Date(Date.now() - 60 * 60000),
        read: false,
        actionable: true,
        actionLabel: 'إعادة المحاولة'
      }
    ]
    setAlerts(sampleAlerts)
  }

  return {
    alerts,
    addAlert,
    markAsRead,
    dismissAlert,
    clearAll,
    generateSampleAlerts
  }
}
