{"rustc": 16591470773350601817, "features": "[\"bigdecimal\", \"chrono\", \"postgres-array\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-postgres\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"ipnetwork\", \"mac_address\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-any\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-mac_address\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 2357794343378131723, "profile": 15657897354478470176, "path": 10650141606276709978, "deps": [[7161281228672193341, "sea_query", false, 8624398820415589527], [8319709847752024821, "uuid", false, 2124506860832549886], [9897246384292347999, "chrono", false, 7272009525982716657], [12409575957772518135, "time", false, 16913109520808322110], [14647456484942590313, "bigdecimal", false, 12514717680354173628], [15367738274754116744, "serde_json", false, 1559938316259437281], [16682465660942253309, "rust_decimal", false, 5924256968426900616], [17982831385697850842, "sqlx", false, 11326571238587193374]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-query-binder-344d6aa9e5972b89\\dep-lib-sea_query_binder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}