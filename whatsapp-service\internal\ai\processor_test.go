package ai

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap/zaptest"

	"medibridge-whatsapp/internal/database"
)

// MockDB for testing
type MockDB struct {
	mock.Mock
}

func (m *MockDB) Query(query string, args ...interface{}) (*database.Rows, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Rows), mockArgs.Error(1)
}

func (m *MockDB) QueryRow(query string, args ...interface{}) *database.Row {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Row)
}

func (m *MockDB) Exec(query string, args ...interface{}) (database.Result, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(database.Result), mockArgs.Error(1)
}

// MockOllamaClient for testing
type MockOllamaClient struct {
	mock.Mock
}

func (m *MockOllamaClient) GenerateCompletion(ctx context.Context, model, prompt string) (*OllamaResponse, error) {
	args := m.Called(ctx, model, prompt)
	return args.Get(0).(*OllamaResponse), args.Error(1)
}

func TestNewMessageProcessor(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	assert.NotNil(t, processor)
	assert.Equal(t, logger, processor.logger)
	assert.Equal(t, mockDB, processor.db)
	assert.Equal(t, mockClient, processor.ollamaClient)
	assert.Equal(t, 10, processor.batchSize)
	assert.Equal(t, 5*time.Second, processor.processingInterval)
}

func TestProcessMessage(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	// Mock getting message from database
	mockMessage := &database.Message{
		ID:      1,
		Content: "أحتاج باراسيتامول 500 مجم",
	}

	mockDB.On("QueryRow", mock.AnythingOfType("string"), 1).Return(&database.Row{})
	// Mock the Scan method would need more complex mocking

	// Mock Ollama response
	mockResponse := &OllamaResponse{
		Response: `{
			"message_type": "request",
			"medicines": [
				{
					"name": "paracetamol",
					"confidence": 0.9,
					"quantity": "500mg",
					"unit": "tablets"
				}
			],
			"confidence": 0.85
		}`,
	}

	mockClient.On("GenerateCompletion", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(mockResponse, nil)

	// Mock database updates
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	err := processor.ProcessMessage(1)
	assert.NoError(t, err)

	mockClient.AssertExpectations(t)
	mockDB.AssertExpectations(t)
}

func TestParseAIResponse(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	tests := []struct {
		name           string
		response       string
		expectedType   string
		expectedMeds   int
		expectError    bool
	}{
		{
			name: "valid_request",
			response: `{
				"message_type": "request",
				"medicines": [
					{
						"name": "paracetamol",
						"confidence": 0.9,
						"quantity": "500mg"
					}
				],
				"confidence": 0.85
			}`,
			expectedType: "request",
			expectedMeds: 1,
			expectError:  false,
		},
		{
			name: "valid_offer",
			response: `{
				"message_type": "offer",
				"medicines": [
					{
						"name": "aspirin",
						"confidence": 0.8,
						"price": "50 EGP"
					},
					{
						"name": "ibuprofen",
						"confidence": 0.7
					}
				],
				"confidence": 0.75
			}`,
			expectedType: "offer",
			expectedMeds: 2,
			expectError:  false,
		},
		{
			name: "unknown_type",
			response: `{
				"message_type": "unknown",
				"medicines": [],
				"confidence": 0.1
			}`,
			expectedType: "unknown",
			expectedMeds: 0,
			expectError:  false,
		},
		{
			name:        "invalid_json",
			response:    `invalid json`,
			expectError: true,
		},
		{
			name:        "empty_response",
			response:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.parseAIResponse(tt.response)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.expectedType, result.MessageType)
			assert.Len(t, result.Medicines, tt.expectedMeds)
		})
	}
}

func TestBuildPrompt(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	tests := []struct {
		name     string
		content  string
		contains []string
	}{
		{
			name:    "arabic_request",
			content: "أحتاج باراسيتامول",
			contains: []string{
				"أحتاج باراسيتامول",
				"message_type",
				"medicines",
				"confidence",
			},
		},
		{
			name:    "english_offer",
			content: "I have paracetamol available",
			contains: []string{
				"I have paracetamol available",
				"JSON format",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prompt := processor.buildPrompt(tt.content)

			for _, expected := range tt.contains {
				assert.Contains(t, prompt, expected)
			}
		})
	}
}

func TestGetPendingMessages(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 2, 5*time.Second)

	// Mock database query
	mockDB.On("Query", mock.AnythingOfType("string"), 2).Return(&database.Rows{}, nil)

	messages, err := processor.getPendingMessages()
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestUpdateMessageStatus(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	// Mock successful update
	mockDB.On("Exec", mock.AnythingOfType("string"), "processed", mock.AnythingOfType("string"), mock.Anything, 1).Return(&MockResult{}, nil)

	err := processor.updateMessageStatus(1, "processed", `{"test": "data"}`, nil)
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestUpdateMessageStatusWithError(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	errorMsg := "AI processing failed"

	// Mock successful update with error
	mockDB.On("Exec", mock.AnythingOfType("string"), "failed", mock.AnythingOfType("string"), errorMsg, 1).Return(&MockResult{}, nil)

	err := processor.updateMessageStatus(1, "failed", "", &errorMsg)
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestStoreMedicines(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	medicines := []Medicine{
		{
			Name:       "paracetamol",
			Confidence: 0.9,
			Quantity:   "500mg",
			Unit:       "tablets",
		},
		{
			Name:       "aspirin",
			Confidence: 0.8,
			Price:      "50 EGP",
		},
	}

	// Mock medicine lookup and insertion
	mockDB.On("QueryRow", mock.AnythingOfType("string"), "paracetamol").Return(&database.Row{})
	mockDB.On("QueryRow", mock.AnythingOfType("string"), "aspirin").Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil).Times(2)

	err := processor.storeMedicines(1, medicines)
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

// MockResult implements database.Result interface
type MockResult struct {
	mock.Mock
}

func (m *MockResult) RowsAffected() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockResult) LastInsertId() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

// Test with real HTTP server for Ollama client
func TestOllamaClientIntegration(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"response": `{
				"message_type": "request",
				"medicines": [
					{
						"name": "paracetamol",
						"confidence": 0.9
					}
				],
				"confidence": 0.85
			}`,
		}
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	client := NewOllamaClient(server.URL)
	
	ctx := context.Background()
	response, err := client.GenerateCompletion(ctx, "llama2", "test prompt")
	
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Contains(t, response.Response, "paracetamol")
}

func TestOllamaClientError(t *testing.T) {
	// Create a test server that returns an error
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	}))
	defer server.Close()

	client := NewOllamaClient(server.URL)
	
	ctx := context.Background()
	response, err := client.GenerateCompletion(ctx, "llama2", "test prompt")
	
	assert.Error(t, err)
	assert.Nil(t, response)
}

// Benchmark tests
func BenchmarkParseAIResponse(b *testing.B) {
	logger := zaptest.NewLogger(b)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	response := `{
		"message_type": "request",
		"medicines": [
			{
				"name": "paracetamol",
				"confidence": 0.9,
				"quantity": "500mg"
			}
		],
		"confidence": 0.85
	}`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		processor.parseAIResponse(response)
	}
}

func BenchmarkBuildPrompt(b *testing.B) {
	logger := zaptest.NewLogger(b)
	mockDB := &MockDB{}
	mockClient := &MockOllamaClient{}

	processor := NewMessageProcessor(logger, mockDB, mockClient, 10, 5*time.Second)

	content := "أحتاج باراسيتامول 500 مجم للصداع"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		processor.buildPrompt(content)
	}
}
