use sea_orm_migration::{prelude::*, schema::*};

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(Matches::Table)
                    .if_not_exists()
                    .col(pk_auto(Matches::Id))
                    .col(integer(Matches::RequestMessageId))
                    .col(integer(Matches::OfferMessageId))
                    .col(integer(Matches::MedicineId))
                    .col(float(Matches::ConfidenceScore).default(0.0))
                    .col(string(Matches::Status).default("pending"))
                    .col(text_null(Matches::MatchingCriteria))
                    .col(text_null(Matches::Notes))
                    .col(timestamp_with_time_zone(Matches::CreatedAt).default(Expr::current_timestamp()))
                    .col(timestamp_with_time_zone(Matches::UpdatedAt).default(Expr::current_timestamp()))
                    .col(timestamp_null(Matches::NotifiedAt))
                    .col(timestamp_null(Matches::CompletedAt))
                    .col(timestamp_null(Matches::ExpiresAt))
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_matches_request_message_id")
                            .from(Matches::Table, Matches::RequestMessageId)
                            .to(Messages::Table, Messages::Id)
                            .on_delete(ForeignKeyAction::Cascade)
                            .on_update(ForeignKeyAction::Cascade),
                    )
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_matches_offer_message_id")
                            .from(Matches::Table, Matches::OfferMessageId)
                            .to(Messages::Table, Messages::Id)
                            .on_delete(ForeignKeyAction::Cascade)
                            .on_update(ForeignKeyAction::Cascade),
                    )
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_matches_medicine_id")
                            .from(Matches::Table, Matches::MedicineId)
                            .to(Medicines::Table, Medicines::Id)
                            .on_delete(ForeignKeyAction::Cascade)
                            .on_update(ForeignKeyAction::Cascade),
                    )
                    .to_owned(),
            )
            .await?;

        // Create unique index to prevent duplicate matches
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_matches_unique")
                    .table(Matches::Table)
                    .col(Matches::RequestMessageId)
                    .col(Matches::OfferMessageId)
                    .col(Matches::MedicineId)
                    .unique()
                    .to_owned(),
            )
            .await?;

        // Create index on status for filtering
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_matches_status")
                    .table(Matches::Table)
                    .col(Matches::Status)
                    .to_owned(),
            )
            .await?;

        // Create index on medicine_id for filtering by medicine
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_matches_medicine_id")
                    .table(Matches::Table)
                    .col(Matches::MedicineId)
                    .to_owned(),
            )
            .await?;

        // Create index on confidence_score for filtering high-confidence matches
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_matches_confidence_score")
                    .table(Matches::Table)
                    .col(Matches::ConfidenceScore)
                    .to_owned(),
            )
            .await?;

        // Create index on created_at for chronological ordering
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_matches_created_at")
                    .table(Matches::Table)
                    .col(Matches::CreatedAt)
                    .to_owned(),
            )
            .await?;

        // Create index on expires_at for cleanup tasks
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_matches_expires_at")
                    .table(Matches::Table)
                    .col(Matches::ExpiresAt)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Matches::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum Matches {
    Table,
    Id,
    RequestMessageId,
    OfferMessageId,
    MedicineId,
    ConfidenceScore,
    Status,
    MatchingCriteria,
    Notes,
    CreatedAt,
    UpdatedAt,
    NotifiedAt,
    CompletedAt,
    ExpiresAt,
}

#[derive(DeriveIden)]
enum Messages {
    Table,
    Id,
}

#[derive(DeriveIden)]
enum Medicines {
    Table,
    Id,
}
