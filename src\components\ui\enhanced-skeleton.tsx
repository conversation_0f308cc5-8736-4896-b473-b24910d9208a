import { cn } from '@/lib/utils';

interface EnhancedSkeletonProps {
  className?: string;
  variant?: 'default' | 'chart' | 'metric' | 'text';
  animate?: boolean;
}

export function EnhancedSkeleton({
  className,
  variant = 'default',
  animate = true,
}: EnhancedSkeletonProps) {
  const baseClasses =
    'bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded-md';
  const animationClasses = animate ? 'animate-pulse' : '';

  const variantClasses = {
    default: 'h-4 w-full',
    chart: 'h-64 w-full rounded-lg',
    metric: 'h-8 w-24 rounded-lg',
    text: 'h-3 w-full',
  };

  return (
    <div
      className={cn(
        baseClasses,
        animationClasses,
        variantClasses[variant],
        className,
      )}
    />
  );
}

interface ChartSkeletonProps {
  className?: string;
}

export function ChartSkeleton({ className }: ChartSkeletonProps) {
  return (
    <div className={cn('space-y-4 p-4', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <EnhancedSkeleton className="h-5 w-32" />
          <EnhancedSkeleton variant="text" className="w-48" />
        </div>
        <div className="flex space-x-2">
          <EnhancedSkeleton className="h-7 w-7 rounded-full" />
          <EnhancedSkeleton className="h-7 w-7 rounded-full" />
        </div>
      </div>

      {/* Chart Area */}
      <EnhancedSkeleton variant="chart" />
    </div>
  );
}

interface MetricSkeletonProps {
  className?: string;
}

export function MetricSkeleton({ className }: MetricSkeletonProps) {
  return (
    <div className={cn('space-y-3 p-4', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <EnhancedSkeleton variant="text" className="w-20" />
        <EnhancedSkeleton className="h-8 w-8 rounded-lg" />
      </div>

      {/* Value */}
      <EnhancedSkeleton variant="metric" />

      {/* Trend */}
      <EnhancedSkeleton className="h-5 w-16 rounded-full" />
    </div>
  );
}

interface PanelSkeletonProps {
  className?: string;
  rows?: number;
}

export function PanelSkeleton({ className, rows = 3 }: PanelSkeletonProps) {
  return (
    <div className={cn('space-y-4 p-4', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <EnhancedSkeleton className="h-5 w-28" />
        <EnhancedSkeleton className="h-6 w-6 rounded-full" />
      </div>

      {/* Content Rows */}
      <div className="space-y-3">
        {Array.from({ length: rows }).map((_, i) => (
          <div key={i} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <EnhancedSkeleton className="h-6 w-6 rounded-full" />
              <EnhancedSkeleton variant="text" className="w-24" />
            </div>
            <EnhancedSkeleton className="h-4 w-16" />
          </div>
        ))}
      </div>
    </div>
  );
}

interface TableSkeletonProps {
  className?: string;
  rows?: number;
  columns?: number;
}

export function TableSkeleton({
  className,
  rows = 5,
  columns = 4,
}: TableSkeletonProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {/* Header */}
      <div
        className="grid gap-4"
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {Array.from({ length: columns }).map((_, i) => (
          <EnhancedSkeleton key={i} className="h-4 w-full" />
        ))}
      </div>

      {/* Rows */}
      <div className="space-y-2">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div
            key={rowIndex}
            className="grid gap-4"
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <EnhancedSkeleton
                key={colIndex}
                variant="text"
                className="w-full"
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}

interface LoadingStateProps {
  type: 'chart' | 'metric' | 'panel' | 'table';
  className?: string;
  rows?: number;
  columns?: number;
}

export function LoadingState({
  type,
  className,
  rows,
  columns,
}: LoadingStateProps) {
  switch (type) {
    case 'chart':
      return <ChartSkeleton className={className} />;
    case 'metric':
      return <MetricSkeleton className={className} />;
    case 'panel':
      return <PanelSkeleton className={className} rows={rows} />;
    case 'table':
      return (
        <TableSkeleton className={className} rows={rows} columns={columns} />
      );
    default:
      return <EnhancedSkeleton className={className} />;
  }
}
