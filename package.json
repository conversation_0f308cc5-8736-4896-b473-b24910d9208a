{"name": "medibridge", "private": true, "type": "module", "scripts": {"dev": "vite --port 1420", "start": "vite --port 1420", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "format": "biome format", "lint": "biome lint", "check": "biome check", "typecheck": "npx tsc --noEmit", "tauri": "tauri", "db:studio": "drizzle-kit studio"}, "dependencies": {"@faker-js/faker": "^9.6.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/vite": "^4.0.6", "@tanstack/db-collections": "^0.0.6", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-db": "^0.0.4", "@tanstack/react-query": "^5.66.5", "@tanstack/react-query-devtools": "^5.66.5", "@tanstack/react-router": "^1.114.3", "@tanstack/react-router-devtools": "^1.114.3", "@tanstack/react-table": "^8.21.2", "@tanstack/react-virtual": "^3.13.8", "@tanstack/router-plugin": "^1.114.3", "@tauri-apps/api": "^2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.12.1", "fuse.js": "^7.1.0", "humantime": "^2.4.0", "immer": "^10.1.1", "lucide-react": "^0.476.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "sonner": "^2.0.3", "swapy": "^1.0.5", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.6", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.4", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tauri-apps/cli": "^2.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/node": "^22.15.18", "@types/pg": "^8.15.2", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "csv-parse": "^5.6.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "jsdom": "^26.0.0", "pg": "^8.16.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5.7.2", "uuid": "^11.1.0", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}