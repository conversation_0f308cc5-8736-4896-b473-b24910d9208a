{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"attributes\", \"crossbeam-utils\", \"default\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"tokio1\", \"wasm-bindgen-futures\"]", "declared_features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"async-process\", \"attributes\", \"crossbeam-utils\", \"default\", \"docs\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"io_safety\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"surf\", \"tokio02\", \"tokio03\", \"tokio1\", \"unstable\", \"wasm-bindgen-futures\"]", "target": 9139776409365598091, "profile": 15657897354478470176, "path": 15006081452338764564, "deps": [[5103565458935487, "futures_io", false, 15671947253275539370], [1615478164327904835, "pin_utils", false, 15291338659036758343], [1906322745568073236, "pin_project_lite", false, 8749487287775455052], [3129130049864710036, "memchr", false, 2482764352869297290], [3722963349756955755, "once_cell", false, 16821080632637768006], [4468123440088164316, "crossbeam_utils", false, 233989751936552599], [5195813957092839672, "async_lock", false, 10316155524247321800], [5302544599749092241, "async_channel", false, 4989310863279113950], [5986029879202738730, "log", false, 3210362874149672375], [6955678925937229351, "slab", false, 17115650866986596118], [7425331225454150061, "futures_lite", false, 439461534531656204], [7620660491849607393, "futures_core", false, 7480744780212006673], [9511937138168509053, "async_attributes", false, 13422225676985540092], [12616986139693676752, "async_io", false, 4022618611418838936], [13330646740533913557, "async_global_executor", false, 17664192619897369611], [17569958903244628888, "kv_log_macro", false, 11862255027106399020]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-std-8a2d410d83d1a63a\\dep-lib-async_std", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}