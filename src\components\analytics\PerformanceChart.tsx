import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface DataPoint {
  date: string
  value: number
  label?: string
}

interface PerformanceChartProps {
  title: string
  description?: string
  data: DataPoint[]
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple'
  showTrend?: boolean
  unit?: string
}

export function PerformanceChart({ 
  title, 
  description, 
  data, 
  color = 'blue', 
  showTrend = true,
  unit = ''
}: PerformanceChartProps) {
  const maxValue = Math.max(...data.map(d => d.value))
  const minValue = Math.min(...data.map(d => d.value))
  const latestValue = data[data.length - 1]?.value || 0
  const previousValue = data[data.length - 2]?.value || 0
  
  const trend = latestValue > previousValue ? 'up' : latestValue < previousValue ? 'down' : 'stable'
  const trendPercentage = previousValue > 0 ? Math.abs(((latestValue - previousValue) / previousValue) * 100) : 0

  const colorClasses = {
    blue: {
      bg: 'bg-blue-500',
      light: 'bg-blue-100',
      text: 'text-blue-600',
      border: 'border-blue-200'
    },
    green: {
      bg: 'bg-green-500',
      light: 'bg-green-100',
      text: 'text-green-600',
      border: 'border-green-200'
    },
    red: {
      bg: 'bg-red-500',
      light: 'bg-red-100',
      text: 'text-red-600',
      border: 'border-red-200'
    },
    yellow: {
      bg: 'bg-yellow-500',
      light: 'bg-yellow-100',
      text: 'text-yellow-600',
      border: 'border-yellow-200'
    },
    purple: {
      bg: 'bg-purple-500',
      light: 'bg-purple-100',
      text: 'text-purple-600',
      border: 'border-purple-200'
    }
  }

  const colors = colorClasses[color]

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-3 h-3 text-green-600" />
      case 'down':
        return <TrendingDown className="w-3 h-3 text-red-600" />
      default:
        return <Minus className="w-3 h-3 text-gray-600" />
    }
  }

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <Card className={`${colors.border} border-2`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{title}</CardTitle>
          {showTrend && (
            <div className="flex items-center space-x-1">
              {getTrendIcon()}
              <span className={`text-xs font-medium ${getTrendColor()}`}>
                {trendPercentage.toFixed(1)}%
              </span>
            </div>
          )}
        </div>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Current Value */}
          <div className="flex items-baseline space-x-2">
            <span className={`text-3xl font-bold ${colors.text}`}>
              {latestValue.toLocaleString('ar-EG')}
            </span>
            {unit && (
              <span className="text-sm text-muted-foreground">{unit}</span>
            )}
          </div>

          {/* Mini Chart */}
          <div className="space-y-2">
            <div className="flex items-end justify-between h-16 space-x-1">
              {data.slice(-7).map((point, index) => {
                const height = maxValue > 0 ? (point.value / maxValue) * 100 : 0
                const isLatest = index === data.slice(-7).length - 1
                
                return (
                  <div
                    key={index}
                    className="flex-1 flex flex-col items-center"
                  >
                    <div
                      className={`w-full rounded-t ${isLatest ? colors.bg : colors.light} transition-all duration-300`}
                      style={{ height: `${Math.max(height, 4)}%` }}
                      title={`${point.label || point.date}: ${point.value}${unit}`}
                    />
                  </div>
                )
              })}
            </div>
            
            {/* Labels */}
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{data[Math.max(0, data.length - 7)]?.date}</span>
              <span>{data[data.length - 1]?.date}</span>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-4 pt-2 border-t">
            <div className="text-center">
              <div className="text-sm font-medium">{maxValue.toLocaleString('ar-EG')}</div>
              <div className="text-xs text-muted-foreground">أعلى قيمة</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-medium">{minValue.toLocaleString('ar-EG')}</div>
              <div className="text-xs text-muted-foreground">أقل قيمة</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Usage example component
export function AnalyticsDashboard() {
  // Mock data - في التطبيق الحقيقي سيتم جلب البيانات من API
  const messageData = [
    { date: '2024-01-15', value: 45 },
    { date: '2024-01-16', value: 52 },
    { date: '2024-01-17', value: 38 },
    { date: '2024-01-18', value: 61 },
    { date: '2024-01-19', value: 55 },
    { date: '2024-01-20', value: 67 },
    { date: '2024-01-21', value: 73 }
  ]

  const matchData = [
    { date: '2024-01-15', value: 12 },
    { date: '2024-01-16', value: 15 },
    { date: '2024-01-17', value: 9 },
    { date: '2024-01-18', value: 18 },
    { date: '2024-01-19', value: 16 },
    { date: '2024-01-20', value: 22 },
    { date: '2024-01-21', value: 25 }
  ]

  const medicineData = [
    { date: '2024-01-15', value: 8 },
    { date: '2024-01-16', value: 12 },
    { date: '2024-01-17', value: 7 },
    { date: '2024-01-18', value: 15 },
    { date: '2024-01-19', value: 11 },
    { date: '2024-01-20', value: 18 },
    { date: '2024-01-21', value: 21 }
  ]

  return (
    <div className="grid gap-6 md:grid-cols-3">
      <PerformanceChart
        title="الرسائل اليومية"
        description="عدد الرسائل المعالجة يومياً"
        data={messageData}
        color="blue"
        unit="رسالة"
      />
      
      <PerformanceChart
        title="المطابقات اليومية"
        description="عدد المطابقات المكتشفة يومياً"
        data={matchData}
        color="green"
        unit="مطابقة"
      />
      
      <PerformanceChart
        title="الأدوية الجديدة"
        description="عدد الأدوية المكتشفة يومياً"
        data={medicineData}
        color="purple"
        unit="دواء"
      />
    </div>
  )
}
