use sea_orm_migration::{prelude::*, schema::*};

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(MessageMedicines::Table)
                    .if_not_exists()
                    .col(pk_auto(MessageMedicines::Id))
                    .col(integer(MessageMedicines::MessageId))
                    .col(integer(MessageMedicines::MedicineId))
                    .col(float(MessageMedicines::ConfidenceScore).default(0.0))
                    .col(string_null(MessageMedicines::Quantity))
                    .col(string_null(MessageMedicines::Unit))
                    .col(string_null(MessageMedicines::Price))
                    .col(string_null(MessageMedicines::Currency))
                    .col(text_null(MessageMedicines::Notes))
                    .col(integer_null(MessageMedicines::PositionStart))
                    .col(integer_null(MessageMedicines::PositionEnd))
                    .col(timestamp_with_time_zone(MessageMedicines::CreatedAt).default(Expr::current_timestamp()))
                    .col(timestamp_with_time_zone(MessageMedicines::UpdatedAt).default(Expr::current_timestamp()))
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_message_medicines_message_id")
                            .from(MessageMedicines::Table, MessageMedicines::MessageId)
                            .to(Messages::Table, Messages::Id)
                            .on_delete(ForeignKeyAction::Cascade)
                            .on_update(ForeignKeyAction::Cascade),
                    )
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_message_medicines_medicine_id")
                            .from(MessageMedicines::Table, MessageMedicines::MedicineId)
                            .to(Medicines::Table, Medicines::Id)
                            .on_delete(ForeignKeyAction::Cascade)
                            .on_update(ForeignKeyAction::Cascade),
                    )
                    .to_owned(),
            )
            .await?;

        // Create unique index to prevent duplicate message-medicine pairs
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_message_medicines_unique")
                    .table(MessageMedicines::Table)
                    .col(MessageMedicines::MessageId)
                    .col(MessageMedicines::MedicineId)
                    .unique()
                    .to_owned(),
            )
            .await?;

        // Create index on message_id for faster lookups
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_message_medicines_message_id")
                    .table(MessageMedicines::Table)
                    .col(MessageMedicines::MessageId)
                    .to_owned(),
            )
            .await?;

        // Create index on medicine_id for faster lookups
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_message_medicines_medicine_id")
                    .table(MessageMedicines::Table)
                    .col(MessageMedicines::MedicineId)
                    .to_owned(),
            )
            .await?;

        // Create index on confidence_score for filtering high-confidence matches
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_message_medicines_confidence_score")
                    .table(MessageMedicines::Table)
                    .col(MessageMedicines::ConfidenceScore)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(MessageMedicines::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum MessageMedicines {
    Table,
    Id,
    MessageId,
    MedicineId,
    ConfidenceScore,
    Quantity,
    Unit,
    Price,
    Currency,
    Notes,
    PositionStart,
    PositionEnd,
    CreatedAt,
    UpdatedAt,
}

#[derive(DeriveIden)]
enum Messages {
    Table,
    Id,
}

#[derive(DeriveIden)]
enum Medicines {
    Table,
    Id,
}
