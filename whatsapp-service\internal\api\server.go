package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"medibridge-whatsapp/internal/ai"
	"medibridge-whatsapp/internal/database"
	"medibridge-whatsapp/internal/whatsapp"
)

// Server represents the API server
type Server struct {
	logger      *zap.Logger
	waManager   *whatsapp.Manager
	db          *database.DB
	aiProcessor *ai.MessageProcessor
}

// NewServer creates a new API server
func NewServer(logger *zap.Logger, waManager *whatsapp.Manager, db *database.DB, aiProcessor *ai.MessageProcessor) *Server {
	return &Server{
		logger:      logger,
		waManager:   waManager,
		db:          db,
		aiProcessor: aiProcessor,
	}
}

// SetupRoutes sets up all API routes
func (s *Server) SetupRoutes(router *gin.Engine) {
	// Health check
	router.GET("/health", s.healthCheck)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Account management
		accounts := v1.Group("/accounts")
		{
			accounts.GET("", s.getAccounts)
			accounts.POST("", s.createAccount)
			accounts.GET("/:id", s.getAccount)
			accounts.PUT("/:id/status", s.updateAccountStatus)
			accounts.DELETE("/:id", s.deleteAccount)
		}

		// WhatsApp client management
		clients := v1.Group("/clients")
		{
			clients.GET("", s.getClients)
			clients.POST("/:phone/connect", s.connectClient)
			clients.POST("/:phone/disconnect", s.disconnectClient)
			clients.GET("/:phone/qr", s.getQRCode)
		}

		// Message management
		messages := v1.Group("/messages")
		{
			messages.GET("", s.getMessages)
			messages.GET("/:id", s.getMessage)
			messages.POST("/:id/reprocess", s.reprocessMessage)
		}

		// Statistics
		stats := v1.Group("/stats")
		{
			stats.GET("/overview", s.getStatsOverview)
			stats.GET("/messages", s.getMessageStats)
			stats.GET("/medicines", s.getMedicineStats)
		}
	}
}

// healthCheck returns the health status of the service
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": gin.H{"unix": gin.H{"seconds": **********}},
		"service":   "medibridge-whatsapp",
	})
}

// getAccounts returns all accounts
func (s *Server) getAccounts(c *gin.Context) {
	accounts, err := s.db.GetActiveAccounts()
	if err != nil {
		s.logger.Error("Failed to get accounts", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get accounts"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"accounts": accounts,
		"total":    len(accounts),
	})
}

// createAccount creates a new account
func (s *Server) createAccount(c *gin.Context) {
	var req struct {
		PhoneNumber string `json:"phone_number" binding:"required"`
		DisplayName string `json:"display_name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// TODO: Implement account creation in database
	c.JSON(http.StatusCreated, gin.H{
		"message": "Account created successfully",
		"phone":   req.PhoneNumber,
	})
}

// getAccount returns a specific account
func (s *Server) getAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	// TODO: Implement get account by ID
	c.JSON(http.StatusOK, gin.H{
		"account_id": id,
		"message":    "Account details",
	})
}

// updateAccountStatus updates an account's status
func (s *Server) updateAccountStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := s.db.UpdateAccountStatus(id, req.Status); err != nil {
		s.logger.Error("Failed to update account status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update account status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Account status updated successfully",
	})
}

// deleteAccount deletes an account
func (s *Server) deleteAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	// TODO: Implement account deletion
	c.JSON(http.StatusOK, gin.H{
		"message":    "Account deleted successfully",
		"account_id": id,
	})
}

// getClients returns all connected WhatsApp clients
func (s *Server) getClients(c *gin.Context) {
	clients := s.waManager.GetClients()

	c.JSON(http.StatusOK, gin.H{
		"clients": clients,
		"total":   len(clients),
	})
}

// connectClient connects a WhatsApp client
func (s *Server) connectClient(c *gin.Context) {
	phone := c.Param("phone")

	// TODO: Implement client connection
	c.JSON(http.StatusOK, gin.H{
		"message": "Client connection initiated",
		"phone":   phone,
	})
}

// disconnectClient disconnects a WhatsApp client
func (s *Server) disconnectClient(c *gin.Context) {
	phone := c.Param("phone")

	// TODO: Implement client disconnection
	c.JSON(http.StatusOK, gin.H{
		"message": "Client disconnected",
		"phone":   phone,
	})
}

// getQRCode returns QR code for pairing
func (s *Server) getQRCode(c *gin.Context) {
	phone := c.Param("phone")

	// TODO: Implement QR code generation
	c.JSON(http.StatusOK, gin.H{
		"message": "QR code generated",
		"phone":   phone,
		"qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
	})
}

// getMessages returns messages with pagination
func (s *Server) getMessages(c *gin.Context) {
	// Get query parameters
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")
	status := c.Query("status")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	// TODO: Implement message retrieval with filters
	c.JSON(http.StatusOK, gin.H{
		"messages": []interface{}{},
		"total":    0,
		"limit":    limit,
		"offset":   offset,
		"status":   status,
	})
}

// getMessage returns a specific message
func (s *Server) getMessage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message ID"})
		return
	}

	// TODO: Implement get message by ID
	c.JSON(http.StatusOK, gin.H{
		"message_id": id,
		"message":    "Message details",
	})
}

// reprocessMessage reprocesses a message with AI
func (s *Server) reprocessMessage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message ID"})
		return
	}

	// Process the message with AI
	ctx := c.Request.Context()
	if err := s.aiProcessor.ProcessSingleMessage(ctx, id); err != nil {
		s.logger.Error("Failed to reprocess message", zap.Int("message_id", id), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reprocess message"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Message reprocessed successfully",
		"message_id": id,
	})
}

// getStatsOverview returns general statistics
func (s *Server) getStatsOverview(c *gin.Context) {
	// TODO: Implement statistics overview
	c.JSON(http.StatusOK, gin.H{
		"total_accounts":     0,
		"active_accounts":    0,
		"total_messages":     0,
		"processed_messages": 0,
		"pending_messages":   0,
		"total_medicines":    0,
		"total_matches":      0,
	})
}

// getMessageStats returns message statistics
func (s *Server) getMessageStats(c *gin.Context) {
	// TODO: Implement message statistics
	c.JSON(http.StatusOK, gin.H{
		"daily_messages":   []interface{}{},
		"message_types":    gin.H{"request": 0, "offer": 0, "unknown": 0},
		"processing_stats": gin.H{"pending": 0, "processed": 0, "failed": 0},
	})
}

// getMedicineStats returns medicine statistics
func (s *Server) getMedicineStats(c *gin.Context) {
	// TODO: Implement medicine statistics
	c.JSON(http.StatusOK, gin.H{
		"most_requested": []interface{}{},
		"most_offered":   []interface{}{},
		"categories":     gin.H{},
	})
}
