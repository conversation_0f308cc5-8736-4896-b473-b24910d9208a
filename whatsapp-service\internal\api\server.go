package api

import (
	"fmt"
	"medibridge-whatsapp/internal/ai"
	"medibridge-whatsapp/internal/database"
	"medibridge-whatsapp/internal/matching"
	"medibridge-whatsapp/internal/whatsapp"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Server represents the API server
type Server struct {
	logger          *zap.Logger
	waManager       *whatsapp.Manager
	db              *database.DB
	aiProcessor     *ai.MessageProcessor
	matchingService *matching.Service
}

// NewServer creates a new API server
func NewServer(logger *zap.Logger, waManager *whatsapp.Manager, db *database.DB, aiProcessor *ai.MessageProcessor, matchingService *matching.Service) *Server {
	return &Server{
		logger:          logger,
		waManager:       waManager,
		db:              db,
		aiProcessor:     aiProcessor,
		matchingService: matchingService,
	}
}

// SetupRoutes sets up all API routes
func (s *Server) SetupRoutes(router *gin.Engine) {
	// Health check
	router.GET("/health", s.healthCheck)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Account management
		accounts := v1.Group("/accounts")
		{
			accounts.GET("", s.getAccounts)
			accounts.POST("", s.createAccount)
			accounts.GET("/:id", s.getAccount)
			accounts.PUT("/:id/status", s.updateAccountStatus)
			accounts.DELETE("/:id", s.deleteAccount)
		}

		// WhatsApp client management
		clients := v1.Group("/clients")
		{
			clients.GET("", s.getClients)
			clients.POST("/:phone/connect", s.connectClient)
			clients.POST("/:phone/disconnect", s.disconnectClient)
			clients.GET("/:phone/qr", s.getQRCode)
		}

		// Message management
		messages := v1.Group("/messages")
		{
			messages.GET("", s.getMessages)
			messages.GET("/:id", s.getMessage)
			messages.POST("/:id/reprocess", s.reprocessMessage)
		}

		// Matching
		matches := v1.Group("/matches")
		{
			matches.GET("", s.getMatches)
			matches.GET("/:id", s.getMatch)
			matches.PUT("/:id/status", s.updateMatchStatus)
			matches.POST("/run", s.runMatching)
			matches.GET("/stats", s.getMatchingStats)
		}

		// Statistics
		stats := v1.Group("/stats")
		{
			stats.GET("/overview", s.getStatsOverview)
			stats.GET("/messages", s.getMessageStats)
			stats.GET("/medicines", s.getMedicineStats)
		}
	}
}

// healthCheck returns the health status of the service
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": gin.H{"unix": gin.H{"seconds": **********}},
		"service":   "medibridge-whatsapp",
	})
}

// getAccounts returns all accounts
func (s *Server) getAccounts(c *gin.Context) {
	accounts, err := s.db.GetActiveAccounts()
	if err != nil {
		s.logger.Error("Failed to get accounts", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get accounts"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"accounts": accounts,
		"total":    len(accounts),
	})
}

// createAccount creates a new account
func (s *Server) createAccount(c *gin.Context) {
	var req struct {
		PhoneNumber string `json:"phone_number" binding:"required"`
		DisplayName string `json:"display_name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create account in database
	accountID := fmt.Sprintf("acc_%d", time.Now().Unix())

	query := `
		INSERT INTO whatsapp_accounts (account_id, phone_number, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
	`

	now := time.Now()
	_, err := s.db.Exec(query, accountID, req.PhoneNumber, "disconnected", now, now)
	if err != nil {
		s.logger.Error("Failed to create account", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create account"})
		return
	}

	s.logger.Info("Account created successfully",
		zap.String("account_id", accountID),
		zap.String("phone", req.PhoneNumber))

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Account created successfully",
		"account_id": accountID,
		"phone":      req.PhoneNumber,
	})
}

// getAccount returns a specific account
func (s *Server) getAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	query := `
		SELECT account_id, phone_number, status, last_seen, created_at, updated_at
		FROM whatsapp_accounts
		WHERE id = $1
	`

	var account struct {
		AccountID   string     `json:"account_id"`
		PhoneNumber string     `json:"phone_number"`
		Status      string     `json:"status"`
		LastSeen    *time.Time `json:"last_seen"`
		CreatedAt   time.Time  `json:"created_at"`
		UpdatedAt   time.Time  `json:"updated_at"`
	}

	row := s.db.QueryRow(query, id)
	err = row.Scan(&account.AccountID, &account.PhoneNumber, &account.Status,
		&account.LastSeen, &account.CreatedAt, &account.UpdatedAt)

	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
			return
		}
		s.logger.Error("Failed to get account", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get account"})
		return
	}

	c.JSON(http.StatusOK, account)
}

// updateAccountStatus updates an account's status
func (s *Server) updateAccountStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := s.db.UpdateAccountStatus(id, req.Status); err != nil {
		s.logger.Error("Failed to update account status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update account status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Account status updated successfully",
	})
}

// deleteAccount deletes an account
func (s *Server) deleteAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	// First check if account exists
	checkQuery := `SELECT account_id FROM whatsapp_accounts WHERE id = $1`
	var accountID string
	err = s.db.QueryRow(checkQuery, id).Scan(&accountID)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
			return
		}
		s.logger.Error("Failed to check account", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check account"})
		return
	}

	// Delete account from database
	deleteQuery := `DELETE FROM whatsapp_accounts WHERE id = $1`
	result, err := s.db.Exec(deleteQuery, id)
	if err != nil {
		s.logger.Error("Failed to delete account", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete account"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
		return
	}

	s.logger.Info("Account deleted successfully", zap.String("account_id", accountID))
	c.JSON(http.StatusOK, gin.H{
		"message":    "Account deleted successfully",
		"account_id": accountID,
	})
}

// getClients returns all connected WhatsApp clients
func (s *Server) getClients(c *gin.Context) {
	clients := s.waManager.GetClients()

	c.JSON(http.StatusOK, gin.H{
		"clients": clients,
		"total":   len(clients),
	})
}

// connectClient connects a WhatsApp client
func (s *Server) connectClient(c *gin.Context) {
	phone := c.Param("phone")

	// TODO: Implement client connection
	c.JSON(http.StatusOK, gin.H{
		"message": "Client connection initiated",
		"phone":   phone,
	})
}

// disconnectClient disconnects a WhatsApp client
func (s *Server) disconnectClient(c *gin.Context) {
	phone := c.Param("phone")

	// TODO: Implement client disconnection
	c.JSON(http.StatusOK, gin.H{
		"message": "Client disconnected",
		"phone":   phone,
	})
}

// getQRCode returns QR code for pairing
func (s *Server) getQRCode(c *gin.Context) {
	phone := c.Param("phone")

	// TODO: Implement QR code generation
	c.JSON(http.StatusOK, gin.H{
		"message": "QR code generated",
		"phone":   phone,
		"qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
	})
}

// getMessages returns messages with pagination
func (s *Server) getMessages(c *gin.Context) {
	// Get query parameters
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")
	status := c.Query("status")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	// Build query with filters
	baseQuery := `
		SELECT id, whatsapp_message_id, account_id, group_jid, group_name,
			   sender_jid, sender_name, content, message_type, processing_status,
			   ai_analysis, error_message, message_timestamp, received_at,
			   processed_at, created_at, updated_at
		FROM messages
	`

	var whereClause string
	var args []interface{}
	argIndex := 1

	if status != "" {
		whereClause = " WHERE processing_status = $" + strconv.Itoa(argIndex)
		args = append(args, status)
		argIndex++
	}

	// Get total count
	countQuery := "SELECT COUNT(*) FROM messages" + whereClause
	var total int
	err := s.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		s.logger.Error("Failed to get message count", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get message count"})
		return
	}

	// Get messages with pagination
	query := baseQuery + whereClause +
		" ORDER BY message_timestamp DESC LIMIT $" + strconv.Itoa(argIndex) +
		" OFFSET $" + strconv.Itoa(argIndex+1)
	args = append(args, limit, offset)

	rows, err := s.db.Query(query, args...)
	if err != nil {
		s.logger.Error("Failed to get messages", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get messages"})
		return
	}
	defer rows.Close()

	var messages []map[string]interface{}
	for rows.Next() {
		var msg struct {
			ID                int        `json:"id"`
			WhatsAppMessageID string     `json:"whatsapp_message_id"`
			AccountID         string     `json:"account_id"`
			GroupJID          string     `json:"group_jid"`
			GroupName         string     `json:"group_name"`
			SenderJID         string     `json:"sender_jid"`
			SenderName        string     `json:"sender_name"`
			Content           string     `json:"content"`
			MessageType       *string    `json:"message_type"`
			ProcessingStatus  string     `json:"processing_status"`
			AIAnalysis        *string    `json:"ai_analysis"`
			ErrorMessage      *string    `json:"error_message"`
			MessageTimestamp  time.Time  `json:"message_timestamp"`
			ReceivedAt        time.Time  `json:"received_at"`
			ProcessedAt       *time.Time `json:"processed_at"`
			CreatedAt         time.Time  `json:"created_at"`
			UpdatedAt         time.Time  `json:"updated_at"`
		}

		err := rows.Scan(&msg.ID, &msg.WhatsAppMessageID, &msg.AccountID, &msg.GroupJID,
			&msg.GroupName, &msg.SenderJID, &msg.SenderName, &msg.Content,
			&msg.MessageType, &msg.ProcessingStatus, &msg.AIAnalysis, &msg.ErrorMessage,
			&msg.MessageTimestamp, &msg.ReceivedAt, &msg.ProcessedAt,
			&msg.CreatedAt, &msg.UpdatedAt)
		if err != nil {
			s.logger.Error("Failed to scan message", zap.Error(err))
			continue
		}

		messages = append(messages, map[string]interface{}{
			"id":                  msg.ID,
			"whatsapp_message_id": msg.WhatsAppMessageID,
			"account_id":          msg.AccountID,
			"group_jid":           msg.GroupJID,
			"group_name":          msg.GroupName,
			"sender_jid":          msg.SenderJID,
			"sender_name":         msg.SenderName,
			"content":             msg.Content,
			"message_type":        msg.MessageType,
			"processing_status":   msg.ProcessingStatus,
			"ai_analysis":         msg.AIAnalysis,
			"error_message":       msg.ErrorMessage,
			"message_timestamp":   msg.MessageTimestamp,
			"received_at":         msg.ReceivedAt,
			"processed_at":        msg.ProcessedAt,
			"created_at":          msg.CreatedAt,
			"updated_at":          msg.UpdatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"messages": messages,
		"total":    total,
		"limit":    limit,
		"offset":   offset,
		"status":   status,
	})
}

// getMessage returns a specific message
func (s *Server) getMessage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message ID"})
		return
	}

	query := `
		SELECT id, whatsapp_message_id, account_id, group_jid, group_name,
			   sender_jid, sender_name, content, message_type, processing_status,
			   ai_analysis, error_message, message_timestamp, received_at,
			   processed_at, created_at, updated_at
		FROM messages
		WHERE id = $1
	`

	var msg struct {
		ID                int        `json:"id"`
		WhatsAppMessageID string     `json:"whatsapp_message_id"`
		AccountID         string     `json:"account_id"`
		GroupJID          string     `json:"group_jid"`
		GroupName         string     `json:"group_name"`
		SenderJID         string     `json:"sender_jid"`
		SenderName        string     `json:"sender_name"`
		Content           string     `json:"content"`
		MessageType       *string    `json:"message_type"`
		ProcessingStatus  string     `json:"processing_status"`
		AIAnalysis        *string    `json:"ai_analysis"`
		ErrorMessage      *string    `json:"error_message"`
		MessageTimestamp  time.Time  `json:"message_timestamp"`
		ReceivedAt        time.Time  `json:"received_at"`
		ProcessedAt       *time.Time `json:"processed_at"`
		CreatedAt         time.Time  `json:"created_at"`
		UpdatedAt         time.Time  `json:"updated_at"`
	}

	row := s.db.QueryRow(query, id)
	err = row.Scan(&msg.ID, &msg.WhatsAppMessageID, &msg.AccountID, &msg.GroupJID,
		&msg.GroupName, &msg.SenderJID, &msg.SenderName, &msg.Content,
		&msg.MessageType, &msg.ProcessingStatus, &msg.AIAnalysis, &msg.ErrorMessage,
		&msg.MessageTimestamp, &msg.ReceivedAt, &msg.ProcessedAt,
		&msg.CreatedAt, &msg.UpdatedAt)

	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Message not found"})
			return
		}
		s.logger.Error("Failed to get message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get message"})
		return
	}

	// Get associated medicines
	medicinesQuery := `
		SELECT mm.id, mm.medicine_id, m.name, mm.confidence_score,
			   mm.quantity, mm.unit, mm.price, mm.currency
		FROM message_medicines mm
		JOIN medicines m ON mm.medicine_id = m.id
		WHERE mm.message_id = $1
	`

	medicineRows, err := s.db.Query(medicinesQuery, id)
	if err != nil {
		s.logger.Error("Failed to get message medicines", zap.Error(err))
	} else {
		defer medicineRows.Close()

		var medicines []map[string]interface{}
		for medicineRows.Next() {
			var med struct {
				ID              int     `json:"id"`
				MedicineID      int     `json:"medicine_id"`
				Name            string  `json:"name"`
				ConfidenceScore float64 `json:"confidence_score"`
				Quantity        *string `json:"quantity"`
				Unit            *string `json:"unit"`
				Price           *string `json:"price"`
				Currency        *string `json:"currency"`
			}

			err := medicineRows.Scan(&med.ID, &med.MedicineID, &med.Name,
				&med.ConfidenceScore, &med.Quantity, &med.Unit, &med.Price, &med.Currency)
			if err != nil {
				s.logger.Error("Failed to scan medicine", zap.Error(err))
				continue
			}

			medicines = append(medicines, map[string]interface{}{
				"id":               med.ID,
				"medicine_id":      med.MedicineID,
				"name":             med.Name,
				"confidence_score": med.ConfidenceScore,
				"quantity":         med.Quantity,
				"unit":             med.Unit,
				"price":            med.Price,
				"currency":         med.Currency,
			})
		}

		response := map[string]interface{}{
			"id":                  msg.ID,
			"whatsapp_message_id": msg.WhatsAppMessageID,
			"account_id":          msg.AccountID,
			"group_jid":           msg.GroupJID,
			"group_name":          msg.GroupName,
			"sender_jid":          msg.SenderJID,
			"sender_name":         msg.SenderName,
			"content":             msg.Content,
			"message_type":        msg.MessageType,
			"processing_status":   msg.ProcessingStatus,
			"ai_analysis":         msg.AIAnalysis,
			"error_message":       msg.ErrorMessage,
			"message_timestamp":   msg.MessageTimestamp,
			"received_at":         msg.ReceivedAt,
			"processed_at":        msg.ProcessedAt,
			"created_at":          msg.CreatedAt,
			"updated_at":          msg.UpdatedAt,
			"medicines":           medicines,
		}

		c.JSON(http.StatusOK, response)
		return
	}

	// Fallback without medicines
	c.JSON(http.StatusOK, msg)
}

// reprocessMessage reprocesses a message with AI
func (s *Server) reprocessMessage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message ID"})
		return
	}

	// Process the message with AI
	ctx := c.Request.Context()
	if err := s.aiProcessor.ProcessSingleMessage(ctx, id); err != nil {
		s.logger.Error("Failed to reprocess message", zap.Int("message_id", id), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reprocess message"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Message reprocessed successfully",
		"message_id": id,
	})
}

// getStatsOverview returns general statistics
func (s *Server) getStatsOverview(c *gin.Context) {
	// Get account statistics
	var totalAccounts, activeAccounts int
	s.db.QueryRow("SELECT COUNT(*) FROM whatsapp_accounts").Scan(&totalAccounts)
	s.db.QueryRow("SELECT COUNT(*) FROM whatsapp_accounts WHERE status = 'connected'").Scan(&activeAccounts)

	// Get message statistics
	var totalMessages, processedMessages, pendingMessages int
	s.db.QueryRow("SELECT COUNT(*) FROM messages").Scan(&totalMessages)
	s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'processed'").Scan(&processedMessages)
	s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'pending'").Scan(&pendingMessages)

	// Get medicine statistics
	var totalMedicines int
	s.db.QueryRow("SELECT COUNT(*) FROM medicines").Scan(&totalMedicines)

	// Get match statistics
	var totalMatches int
	s.db.QueryRow("SELECT COUNT(*) FROM matches").Scan(&totalMatches)

	c.JSON(http.StatusOK, gin.H{
		"total_accounts":     totalAccounts,
		"active_accounts":    activeAccounts,
		"total_messages":     totalMessages,
		"processed_messages": processedMessages,
		"pending_messages":   pendingMessages,
		"total_medicines":    totalMedicines,
		"total_matches":      totalMatches,
	})
}

// getMessageStats returns message statistics
func (s *Server) getMessageStats(c *gin.Context) {
	// Get message type statistics
	var requestCount, offerCount, unknownCount int
	s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE message_type = 'request'").Scan(&requestCount)
	s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE message_type = 'offer'").Scan(&offerCount)
	s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE message_type IS NULL OR message_type = 'unknown'").Scan(&unknownCount)

	// Get processing statistics
	var pendingCount, processedCount, failedCount int
	s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'pending'").Scan(&pendingCount)
	s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'processed'").Scan(&processedCount)
	s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'failed'").Scan(&failedCount)

	// Get daily message counts for the last 7 days
	dailyQuery := `
		SELECT DATE(message_timestamp) as date, COUNT(*) as count
		FROM messages
		WHERE message_timestamp >= CURRENT_DATE - INTERVAL '7 days'
		GROUP BY DATE(message_timestamp)
		ORDER BY date DESC
	`

	rows, err := s.db.Query(dailyQuery)
	var dailyMessages []map[string]interface{}

	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var date string
			var count int
			if err := rows.Scan(&date, &count); err == nil {
				dailyMessages = append(dailyMessages, map[string]interface{}{
					"date":  date,
					"count": count,
				})
			}
		}
	}

	if dailyMessages == nil {
		dailyMessages = []map[string]interface{}{}
	}

	c.JSON(http.StatusOK, gin.H{
		"daily_messages":   dailyMessages,
		"message_types":    gin.H{"request": requestCount, "offer": offerCount, "unknown": unknownCount},
		"processing_stats": gin.H{"pending": pendingCount, "processed": processedCount, "failed": failedCount},
	})
}

// getMedicineStats returns medicine statistics
func (s *Server) getMedicineStats(c *gin.Context) {
	// Get most requested medicines
	requestedQuery := `
		SELECT m.name, COUNT(*) as count
		FROM message_medicines mm
		JOIN medicines m ON mm.medicine_id = m.id
		JOIN messages msg ON mm.message_id = msg.id
		WHERE msg.message_type = 'request'
		GROUP BY m.name
		ORDER BY count DESC
		LIMIT 10
	`

	requestedRows, err := s.db.Query(requestedQuery)
	var mostRequested []map[string]interface{}

	if err == nil {
		defer requestedRows.Close()
		for requestedRows.Next() {
			var name string
			var count int
			if err := requestedRows.Scan(&name, &count); err == nil {
				mostRequested = append(mostRequested, map[string]interface{}{
					"name":  name,
					"count": count,
				})
			}
		}
	}

	// Get most offered medicines
	offeredQuery := `
		SELECT m.name, COUNT(*) as count
		FROM message_medicines mm
		JOIN medicines m ON mm.medicine_id = m.id
		JOIN messages msg ON mm.message_id = msg.id
		WHERE msg.message_type = 'offer'
		GROUP BY m.name
		ORDER BY count DESC
		LIMIT 10
	`

	offeredRows, err := s.db.Query(offeredQuery)
	var mostOffered []map[string]interface{}

	if err == nil {
		defer offeredRows.Close()
		for offeredRows.Next() {
			var name string
			var count int
			if err := offeredRows.Scan(&name, &count); err == nil {
				mostOffered = append(mostOffered, map[string]interface{}{
					"name":  name,
					"count": count,
				})
			}
		}
	}

	// Get medicine categories
	categoryQuery := `
		SELECT category, COUNT(*) as count
		FROM medicines
		WHERE category IS NOT NULL AND category != ''
		GROUP BY category
		ORDER BY count DESC
	`

	categoryRows, err := s.db.Query(categoryQuery)
	categories := make(map[string]int)

	if err == nil {
		defer categoryRows.Close()
		for categoryRows.Next() {
			var category string
			var count int
			if err := categoryRows.Scan(&category, &count); err == nil {
				categories[category] = count
			}
		}
	}

	if mostRequested == nil {
		mostRequested = []map[string]interface{}{}
	}
	if mostOffered == nil {
		mostOffered = []map[string]interface{}{}
	}

	c.JSON(http.StatusOK, gin.H{
		"most_requested": mostRequested,
		"most_offered":   mostOffered,
		"categories":     categories,
	})
}

// getMatches returns matches with pagination and filtering
func (s *Server) getMatches(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")
	status := c.DefaultQuery("status", "")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	matches, total, err := s.matchingService.GetMatches(limit, offset, status)
	if err != nil {
		s.logger.Error("Failed to get matches", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get matches"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"matches": matches,
		"total":   total,
		"limit":   limit,
		"offset":  offset,
	})
}

// getMatch returns a specific match by ID
func (s *Server) getMatch(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid match ID"})
		return
	}

	match, err := s.matchingService.GetMatchDetails(id)
	if err != nil {
		s.logger.Error("Failed to get match details", zap.Int("match_id", id), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get match details"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"match": match})
}

// updateMatchStatus updates the status of a match
func (s *Server) updateMatchStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid match ID"})
		return
	}

	var req struct {
		Status string  `json:"status" binding:"required"`
		Notes  *string `json:"notes"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := s.matchingService.UpdateMatchStatus(id, req.Status, req.Notes); err != nil {
		s.logger.Error("Failed to update match status", zap.Int("match_id", id), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update match status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Match status updated successfully"})
}

// runMatching triggers manual matching process
func (s *Server) runMatching(c *gin.Context) {
	ctx := c.Request.Context()
	if err := s.matchingService.RunManualMatching(ctx); err != nil {
		s.logger.Error("Failed to run manual matching", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to run matching"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Matching process started successfully"})
}

// getMatchingStats returns matching statistics
func (s *Server) getMatchingStats(c *gin.Context) {
	stats, err := s.matchingService.GetMatchingStats()
	if err != nil {
		s.logger.Error("Failed to get matching stats", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get matching stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}
