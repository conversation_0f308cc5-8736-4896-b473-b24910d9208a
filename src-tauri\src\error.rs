#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error(transparent)]
    Service(#[from] db_service::ServiceError),

    #[error("HTTP request failed: {0}")]
    HttpRequest(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("Configuration error: {0}")]
    Configuration(String),

    #[error("WhatsApp service unavailable")]
    WhatsAppUnavailable,
}

pub type AppResult<T> = Result<T, AppError>;
