use tauri::State;
use crate::api::{WhatsAppApi, Message, PaginationParams, PaginatedResponse};
use crate::error::AppError;
use crate::state::AppState;

/// Get messages with pagination and filtering
#[tauri::command]
pub async fn get_messages(
    limit: Option<u32>,
    offset: Option<u32>,
    status: Option<String>,
    state: State<'_, AppState>,
) -> Result<PaginatedResponse<Message>, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    let params = PaginationParams {
        limit,
        offset,
        status,
    };
    api.get_messages(params).await.map_err(|e| e.to_string())
}

/// Get a specific message by ID
#[tauri::command]
pub async fn get_message(
    message_id: i32,
    state: State<'_, AppState>,
) -> Result<Message, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.get_message(message_id).await.map_err(|e| e.to_string())
}

/// Reprocess a message with AI
#[tauri::command]
pub async fn reprocess_message(
    message_id: i32,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.reprocess_message(message_id)
        .await
        .map_err(|e| e.to_string())
}
