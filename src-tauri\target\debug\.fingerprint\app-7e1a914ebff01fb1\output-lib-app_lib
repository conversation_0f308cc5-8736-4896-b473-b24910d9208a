{"$message_type":"diagnostic","message":"unresolved import `reqwest`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\api\\client.rs","byte_start":4,"byte_end":11,"line_start":1,"line_end":1,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"use reqwest::{Client, Response};","highlight_start":5,"highlight_end":12}],"label":"use of unresolved module or unlinked crate `reqwest`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `reqwest`, use `cargo add reqwest` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `reqwest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\api\\client.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse reqwest::{Client, Response};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `reqwest`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `reqwest`, use `cargo add reqwest` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::error::AppError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":61,"byte_end":83,"line_start":3,"line_end":3,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"use crate::error::AppError;","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":57,"byte_end":85,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::AppError;","highlight_start":1,"highlight_end":28},{"text":"use crate::state::AppState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::error::AppError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\whatsapp.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::error::AppError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::error::AppError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":84,"byte_end":106,"line_start":3,"line_end":3,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"use crate::error::AppError;","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":80,"byte_end":108,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::AppError;","highlight_start":1,"highlight_end":28},{"text":"use crate::state::AppState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::error::AppError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\accounts.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::error::AppError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::error::AppError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\messages.rs","byte_start":99,"byte_end":121,"line_start":3,"line_end":3,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"use crate::error::AppError;","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\commands\\messages.rs","byte_start":95,"byte_end":123,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::AppError;","highlight_start":1,"highlight_end":28},{"text":"use crate::state::AppState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::error::AppError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\messages.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::error::AppError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::error::AppError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\stats.rs","byte_start":82,"byte_end":104,"line_start":3,"line_end":3,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"use crate::error::AppError;","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\commands\\stats.rs","byte_start":78,"byte_end":106,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::AppError;","highlight_start":1,"highlight_end":28},{"text":"use crate::state::AppState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::error::AppError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\stats.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::error::AppError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":201,"byte_end":206,"line_start":8,"line_end":8,"column_start":36,"column_end":41,"is_primary":true,"text":[{"text":"pub async fn check_whatsapp_health(state: State<'_, AppState>) -> Result<bool, String> {","highlight_start":36,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":201,"byte_end":206,"line_start":8,"line_end":8,"column_start":36,"column_end":41,"is_primary":true,"text":[{"text":"pub async fn check_whatsapp_health(state: State<'_, AppState>) -> Result<bool, String> {","highlight_start":36,"highlight_end":41}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\whatsapp.rs:8:36\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn check_whatsapp_health(state: State<'_, AppState>) -> Result<bool, String> {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":474,"byte_end":479,"line_start":15,"line_end":15,"column_start":35,"column_end":40,"is_primary":true,"text":[{"text":"pub async fn get_whatsapp_clients(state: State<'_, AppState>) -> Result<Vec<Client>, String> {","highlight_start":35,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":474,"byte_end":479,"line_start":15,"line_end":15,"column_start":35,"column_end":40,"is_primary":true,"text":[{"text":"pub async fn get_whatsapp_clients(state: State<'_, AppState>) -> Result<Vec<Client>, String> {","highlight_start":35,"highlight_end":40}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\whatsapp.rs:15:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn get_whatsapp_clients(state: State<'_, AppState>) -> Result<Vec<Client>, String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":771,"byte_end":776,"line_start":24,"line_end":24,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":771,"byte_end":776,"line_start":24,"line_end":24,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\whatsapp.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":1080,"byte_end":1085,"line_start":34,"line_end":34,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":1080,"byte_end":1085,"line_start":34,"line_end":34,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\whatsapp.rs:34:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":1390,"byte_end":1395,"line_start":44,"line_end":44,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\whatsapp.rs","byte_start":1390,"byte_end":1395,"line_start":44,"line_end":44,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\whatsapp.rs:44:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":211,"byte_end":216,"line_start":8,"line_end":8,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"pub async fn get_accounts(state: State<'_, AppState>) -> Result<Vec<Account>, String> {","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":211,"byte_end":216,"line_start":8,"line_end":8,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"pub async fn get_accounts(state: State<'_, AppState>) -> Result<Vec<Account>, String> {","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\accounts.rs:8:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn get_accounts(state: State<'_, AppState>) -> Result<Vec<Account>, String> {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":538,"byte_end":543,"line_start":18,"line_end":18,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":538,"byte_end":543,"line_start":18,"line_end":18,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\accounts.rs:18:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":950,"byte_end":955,"line_start":33,"line_end":33,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":950,"byte_end":955,"line_start":33,"line_end":33,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\accounts.rs:33:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":1275,"byte_end":1280,"line_start":45,"line_end":45,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\accounts.rs","byte_start":1275,"byte_end":1280,"line_start":45,"line_end":45,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\accounts.rs:45:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\messages.rs","byte_start":325,"byte_end":330,"line_start":12,"line_end":12,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\messages.rs","byte_start":325,"byte_end":330,"line_start":12,"line_end":12,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\messages.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\messages.rs","byte_start":729,"byte_end":734,"line_start":27,"line_end":27,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\messages.rs","byte_start":729,"byte_end":734,"line_start":27,"line_end":27,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\messages.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\messages.rs","byte_start":1032,"byte_end":1037,"line_start":37,"line_end":37,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\messages.rs","byte_start":1032,"byte_end":1037,"line_start":37,"line_end":37,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\messages.rs:37:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: State<'_, AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\stats.rs","byte_start":221,"byte_end":226,"line_start":8,"line_end":8,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"pub async fn get_stats_overview(state: State<'_, AppState>) -> Result<StatsOverview, String> {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\stats.rs","byte_start":221,"byte_end":226,"line_start":8,"line_end":8,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"pub async fn get_stats_overview(state: State<'_, AppState>) -> Result<StatsOverview, String> {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\stats.rs:8:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn get_stats_overview(state: State<'_, AppState>) -> Result<StatsOverview, String> {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\stats.rs","byte_start":494,"byte_end":499,"line_start":15,"line_end":15,"column_start":32,"column_end":37,"is_primary":true,"text":[{"text":"pub async fn get_message_stats(state: State<'_, AppState>) -> Result<MessageStats, String> {","highlight_start":32,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\stats.rs","byte_start":494,"byte_end":499,"line_start":15,"line_end":15,"column_start":32,"column_end":37,"is_primary":true,"text":[{"text":"pub async fn get_message_stats(state: State<'_, AppState>) -> Result<MessageStats, String> {","highlight_start":32,"highlight_end":37}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\stats.rs:15:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn get_message_stats(state: State<'_, AppState>) -> Result<MessageStats, String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 18 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 18 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0432`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0432`.\u001b[0m\n"}
