name: Go Tests and Quality Checks

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'whatsapp-service/**'
      - '.github/workflows/go-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'whatsapp-service/**'
      - '.github/workflows/go-tests.yml'

jobs:
  test:
    name: Test Go Backend
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: medibridge_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    strategy:
      matrix:
        go-version: [1.21, 1.22]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ matrix.go-version }}-${{ hashFiles('whatsapp-service/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-${{ matrix.go-version }}-

    - name: Install dependencies
      working-directory: ./whatsapp-service
      run: go mod download

    - name: Verify dependencies
      working-directory: ./whatsapp-service
      run: go mod verify

    - name: Run go vet
      working-directory: ./whatsapp-service
      run: go vet ./...

    - name: Install staticcheck
      run: go install honnef.co/go/tools/cmd/staticcheck@latest

    - name: Run staticcheck
      working-directory: ./whatsapp-service
      run: staticcheck ./...

    - name: Install golangci-lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
        working-directory: ./whatsapp-service

    - name: Run tests
      working-directory: ./whatsapp-service
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: medibridge_test
        DB_USER: postgres
        DB_PASSWORD: postgres
        DB_SSLMODE: disable
      run: |
        go test -v -race -coverprofile=coverage.out ./...

    - name: Run benchmark tests
      working-directory: ./whatsapp-service
      run: |
        go test -bench=. -benchmem ./... > benchmark_results.txt

    - name: Generate coverage report
      working-directory: ./whatsapp-service
      run: |
        go tool cover -html=coverage.out -o coverage.html
        go tool cover -func=coverage.out -o coverage.txt

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./whatsapp-service/coverage.out
        directory: ./whatsapp-service
        flags: backend
        name: go-backend-coverage

    - name: Check coverage threshold
      working-directory: ./whatsapp-service
      run: |
        COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print substr($3, 1, length($3)-1)}')
        echo "Coverage: $COVERAGE%"
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
          echo "Coverage $COVERAGE% is below threshold 80%"
          exit 1
        fi

    - name: Run integration tests
      working-directory: ./whatsapp-service
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: medibridge_test
        DB_USER: postgres
        DB_PASSWORD: postgres
        DB_SSLMODE: disable
      run: |
        go test -tags=integration -v ./...

    - name: Test build
      working-directory: ./whatsapp-service
      run: |
        go build -v .

    - name: Run security scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'whatsapp-service/gosec-report.sarif'
      continue-on-error: true

    - name: Upload test artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-go-${{ matrix.go-version }}
        path: |
          whatsapp-service/coverage.out
          whatsapp-service/coverage.html
          whatsapp-service/coverage.txt
          whatsapp-service/benchmark_results.txt

  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21

    - name: Run performance benchmarks
      working-directory: ./whatsapp-service
      run: |
        go test -bench=. -benchmem -count=5 ./... | tee benchmark_results.txt

    - name: Analyze performance
      working-directory: ./whatsapp-service
      run: |
        echo "## Performance Benchmark Results" > performance_report.md
        echo "\`\`\`" >> performance_report.md
        cat benchmark_results.txt >> performance_report.md
        echo "\`\`\`" >> performance_report.md

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          whatsapp-service/benchmark_results.txt
          whatsapp-service/performance_report.md

  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21

    - name: Install quality tools
      run: |
        go install github.com/fzipp/gocyclo/cmd/gocyclo@latest
        go install github.com/client9/misspell/cmd/misspell@latest
        go install github.com/gordonklaus/ineffassign@latest

    - name: Check cyclomatic complexity
      working-directory: ./whatsapp-service
      run: |
        gocyclo -over 15 . || echo "High complexity detected"

    - name: Check for misspellings
      working-directory: ./whatsapp-service
      run: |
        misspell -error .

    - name: Check for ineffective assignments
      working-directory: ./whatsapp-service
      run: |
        ineffassign .

    - name: Generate quality report
      working-directory: ./whatsapp-service
      run: |
        echo "# Code Quality Report" > quality_report.md
        echo "" >> quality_report.md
        echo "## Cyclomatic Complexity" >> quality_report.md
        gocyclo . >> quality_report.md || true
        echo "" >> quality_report.md
        echo "## Test Coverage" >> quality_report.md
        go test -coverprofile=coverage.out ./... > /dev/null 2>&1 || true
        go tool cover -func=coverage.out >> quality_report.md || true

    - name: Upload quality report
      uses: actions/upload-artifact@v3
      with:
        name: quality-report
        path: whatsapp-service/quality_report.md

  docker:
    name: Docker Build Test
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      working-directory: ./whatsapp-service
      run: |
        docker build -t medibridge-whatsapp:test .

    - name: Test Docker image
      run: |
        docker run --rm medibridge-whatsapp:test --version || echo "Version check completed"

  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [test, performance, quality, docker]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.test.result == 'success' && needs.performance.result == 'success' && needs.quality.result == 'success' && needs.docker.result == 'success'
      run: |
        echo "✅ All tests passed successfully!"
        echo "- Unit tests: ✅"
        echo "- Performance tests: ✅"
        echo "- Quality checks: ✅"
        echo "- Docker build: ✅"

    - name: Notify failure
      if: needs.test.result == 'failure' || needs.performance.result == 'failure' || needs.quality.result == 'failure' || needs.docker.result == 'failure'
      run: |
        echo "❌ Some tests failed:"
        echo "- Unit tests: ${{ needs.test.result }}"
        echo "- Performance tests: ${{ needs.performance.result }}"
        echo "- Quality checks: ${{ needs.quality.result }}"
        echo "- Docker build: ${{ needs.docker.result }}"
        exit 1
