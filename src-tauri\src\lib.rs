mod api;
mod commands;
mod error;
mod state;

use commands::*;
use state::try_init_state;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub async fn run() {
    // Set up services
    let app_state = try_init_state()
        .await
        .expect("Failed to initialize application state");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            // WhatsApp commands
            check_whatsapp_health,
            get_whatsapp_clients,
            connect_whatsapp_client,
            disconnect_whatsapp_client,
            get_whatsapp_qr_code,
            // Account commands
            get_accounts,
            create_account,
            update_account_status,
            delete_account,
            // Message commands
            get_messages,
            get_message,
            reprocess_message,
            // Stats commands
            get_stats_overview,
            get_message_stats,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
