mod state;
mod error;

use state::try_init_state;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub async fn run() {
    // Set up services
    let app_state = try_init_state()
        .await
        .expect("Failed to initialize application state");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
