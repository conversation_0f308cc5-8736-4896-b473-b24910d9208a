use reqwest::{Client, Response};
use serde::{Deserialize, Serialize};
use std::time::Duration;
use crate::error::AppError;

/// HTTP client for communicating with WhatsApp service
#[derive(Clone)]
pub struct WhatsAppClient {
    client: Client,
    base_url: String,
}

impl WhatsAppClient {
    /// Create a new WhatsApp client
    pub fn new(base_url: String) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self { client, base_url }
    }

    /// Get the full URL for an endpoint
    fn url(&self, endpoint: &str) -> String {
        format!("{}{}", self.base_url, endpoint)
    }

    /// Make a GET request
    pub async fn get<T>(&self, endpoint: &str) -> Result<T, AppError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let response = self
            .client
            .get(&self.url(endpoint))
            .send()
            .await
            .map_err(|e| AppError::HttpRequest(e.to_string()))?;

        self.handle_response(response).await
    }

    /// Make a POST request with JSON body
    pub async fn post<T, B>(&self, endpoint: &str, body: &B) -> Result<T, AppError>
    where
        T: for<'de> Deserialize<'de>,
        B: Serialize,
    {
        let response = self
            .client
            .post(&self.url(endpoint))
            .json(body)
            .send()
            .await
            .map_err(|e| AppError::HttpRequest(e.to_string()))?;

        self.handle_response(response).await
    }

    /// Make a PUT request with JSON body
    pub async fn put<T, B>(&self, endpoint: &str, body: &B) -> Result<T, AppError>
    where
        T: for<'de> Deserialize<'de>,
        B: Serialize,
    {
        let response = self
            .client
            .put(&self.url(endpoint))
            .json(body)
            .send()
            .await
            .map_err(|e| AppError::HttpRequest(e.to_string()))?;

        self.handle_response(response).await
    }

    /// Make a DELETE request
    pub async fn delete<T>(&self, endpoint: &str) -> Result<T, AppError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let response = self
            .client
            .delete(&self.url(endpoint))
            .send()
            .await
            .map_err(|e| AppError::HttpRequest(e.to_string()))?;

        self.handle_response(response).await
    }

    /// Handle HTTP response and deserialize JSON
    async fn handle_response<T>(&self, response: Response) -> Result<T, AppError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let status = response.status();
        let text = response
            .text()
            .await
            .map_err(|e| AppError::HttpRequest(e.to_string()))?;

        if !status.is_success() {
            return Err(AppError::HttpRequest(format!(
                "HTTP {} - {}",
                status, text
            )));
        }

        serde_json::from_str(&text)
            .map_err(|e| AppError::Serialization(e.to_string()))
    }

    /// Check if the WhatsApp service is healthy
    pub async fn health_check(&self) -> Result<HealthResponse, AppError> {
        self.get("/health").await
    }
}

/// Health check response
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: serde_json::Value,
    pub service: String,
}

/// Generic API response wrapper
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<String>,
}

/// Pagination parameters
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationParams {
    pub limit: Option<u32>,
    pub offset: Option<u32>,
    pub status: Option<String>,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            limit: Some(50),
            offset: Some(0),
            status: None,
        }
    }
}

/// Paginated response
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub total: u32,
    pub limit: u32,
    pub offset: u32,
}
