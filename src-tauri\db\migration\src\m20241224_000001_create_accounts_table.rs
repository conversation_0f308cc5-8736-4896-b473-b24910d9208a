use sea_orm_migration::{prelude::*, schema::*};

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(Accounts::Table)
                    .if_not_exists()
                    .col(pk_auto(Accounts::Id))
                    .col(string_uniq(Accounts::PhoneNumber))
                    .col(string(Accounts::DisplayName))
                    .col(text_null(Accounts::SessionData))
                    .col(string(Accounts::Status).default("inactive"))
                    .col(timestamp_null(Accounts::LastConnectedAt))
                    .col(timestamp_with_time_zone(Accounts::CreatedAt).default(Expr::current_timestamp()))
                    .col(timestamp_with_time_zone(Accounts::UpdatedAt).default(Expr::current_timestamp()))
                    .to_owned(),
            )
            .await?;

        // Create index on phone_number for faster lookups
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_accounts_phone_number")
                    .table(Accounts::Table)
                    .col(Accounts::PhoneNumber)
                    .to_owned(),
            )
            .await?;

        // Create index on status for filtering
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_accounts_status")
                    .table(Accounts::Table)
                    .col(Accounts::Status)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Accounts::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum Accounts {
    Table,
    Id,
    PhoneNumber,
    DisplayName,
    SessionData,
    Status,
    LastConnectedAt,
    CreatedAt,
    UpdatedAt,
}
