use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "medicines")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    
    /// Medicine name (normalized)
    #[sea_orm(unique)]
    pub name: String,
    
    /// Alternative names/spellings (JSON array)
    pub alternative_names: Option<String>,
    
    /// Medicine category/type
    pub category: Option<String>,
    
    /// Medicine description
    pub description: Option<String>,
    
    /// Active ingredient
    pub active_ingredient: Option<String>,
    
    /// Manufacturer
    pub manufacturer: Option<String>,
    
    /// Dosage form (tablet, syrup, injection, etc.)
    pub dosage_form: Option<String>,
    
    /// Strength/concentration
    pub strength: Option<String>,
    
    /// How many times this medicine was mentioned
    pub mention_count: i32,
    
    /// How many times it was requested
    pub request_count: i32,
    
    /// How many times it was offered
    pub offer_count: i32,
    
    /// Record creation timestamp
    pub created_at: DateTime,
    
    /// Last update timestamp
    pub updated_at: DateTime,
    
    /// Last time this medicine was mentioned
    pub last_mentioned_at: Option<DateTime>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::message_medicines::Entity")]
    MessageMedicines,
}

impl Related<super::message_medicines::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MessageMedicines.def()
    }
}

impl Related<super::messages::Entity> for Entity {
    fn to() -> RelationDef {
        super::message_medicines::Relation::Message.def()
    }
    
    fn via() -> Option<RelationDef> {
        Some(super::message_medicines::Relation::Medicine.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Medicine category enum
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MedicineCategory {
    Antibiotic,
    Painkiller,
    Vitamin,
    Supplement,
    Chronic,
    Emergency,
    Pediatric,
    Dermatology,
    Cardiology,
    Diabetes,
    Respiratory,
    Gastrointestinal,
    Neurological,
    Other,
}

impl std::fmt::Display for MedicineCategory {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MedicineCategory::Antibiotic => write!(f, "antibiotic"),
            MedicineCategory::Painkiller => write!(f, "painkiller"),
            MedicineCategory::Vitamin => write!(f, "vitamin"),
            MedicineCategory::Supplement => write!(f, "supplement"),
            MedicineCategory::Chronic => write!(f, "chronic"),
            MedicineCategory::Emergency => write!(f, "emergency"),
            MedicineCategory::Pediatric => write!(f, "pediatric"),
            MedicineCategory::Dermatology => write!(f, "dermatology"),
            MedicineCategory::Cardiology => write!(f, "cardiology"),
            MedicineCategory::Diabetes => write!(f, "diabetes"),
            MedicineCategory::Respiratory => write!(f, "respiratory"),
            MedicineCategory::Gastrointestinal => write!(f, "gastrointestinal"),
            MedicineCategory::Neurological => write!(f, "neurological"),
            MedicineCategory::Other => write!(f, "other"),
        }
    }
}

impl From<String> for MedicineCategory {
    fn from(s: String) -> Self {
        match s.as_str() {
            "antibiotic" => MedicineCategory::Antibiotic,
            "painkiller" => MedicineCategory::Painkiller,
            "vitamin" => MedicineCategory::Vitamin,
            "supplement" => MedicineCategory::Supplement,
            "chronic" => MedicineCategory::Chronic,
            "emergency" => MedicineCategory::Emergency,
            "pediatric" => MedicineCategory::Pediatric,
            "dermatology" => MedicineCategory::Dermatology,
            "cardiology" => MedicineCategory::Cardiology,
            "diabetes" => MedicineCategory::Diabetes,
            "respiratory" => MedicineCategory::Respiratory,
            "gastrointestinal" => MedicineCategory::Gastrointestinal,
            "neurological" => MedicineCategory::Neurological,
            _ => MedicineCategory::Other,
        }
    }
}
