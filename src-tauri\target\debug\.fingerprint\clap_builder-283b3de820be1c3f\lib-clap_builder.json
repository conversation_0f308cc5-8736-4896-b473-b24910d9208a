{"rustc": 16591470773350601817, "features": "[\"color\", \"env\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 11439587820120798860, "path": 4629980607761568073, "deps": [[4858255257716900954, "anstyle", false, 7081659959958797465], [11166530783118767604, "strsim", false, 3129134251221501132], [12553266436076736472, "clap_lex", false, 12581753785985439687], [13237942454122161292, "anstream", false, 2291890646523337243]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-283b3de820be1c3f\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}