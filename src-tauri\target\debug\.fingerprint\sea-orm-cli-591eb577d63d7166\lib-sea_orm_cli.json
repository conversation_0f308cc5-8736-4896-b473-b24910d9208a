{"rustc": 16591470773350601817, "features": "[\"clap\", \"cli\", \"dotenvy\"]", "declared_features": "[\"async-std\", \"clap\", \"cli\", \"codegen\", \"default\", \"dotenvy\", \"postgres-vector\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sea-orm-codegen\", \"sqlx\"]", "target": 5960219669809230312, "profile": 15657897354478470176, "path": 4560770678241591688, "deps": [[3150220818285335163, "url", false, 6530261799351731353], [3405707034081185165, "dotenvy", false, 9497081734834712602], [8606274917505247608, "tracing", false, 13752143806742742294], [9451456094439810778, "regex", false, 1995452313642458860], [9897246384292347999, "chrono", false, 7272009525982716657], [10686516203231854806, "sea_schema", false, 14040046828313086354], [16230660778393187092, "tracing_subscriber", false, 10624335275707051079], [17155886227862585100, "glob", false, 16192797392537921819], [17612818546626403359, "clap", false, 12397158068548163120]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-cli-591eb577d63d7166\\dep-lib-sea_orm_cli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}