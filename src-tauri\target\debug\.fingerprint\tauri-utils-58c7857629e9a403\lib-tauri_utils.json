{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 10762829149086716405, "deps": [[561782849581144631, "html5ever", false, 11445995785079089144], [1200537532907108615, "url<PERSON><PERSON>n", false, 14176211530670977511], [3129130049864710036, "memchr", false, 2482764352869297290], [3150220818285335163, "url", false, 6530261799351731353], [3191507132440681679, "serde_untagged", false, 3352916838612899264], [4899080583175475170, "semver", false, 818495799722532413], [5986029879202738730, "log", false, 3210362874149672375], [6213549728662707793, "serde_with", false, 17294102683623283372], [6262254372177975231, "kuchiki", false, 1942183959293843964], [6606131838865521726, "ctor", false, 10319392224565566374], [7170110829644101142, "json_patch", false, 7301499811442259134], [8319709847752024821, "uuid", false, 2124506860832549886], [8786711029710048183, "toml", false, 7835229229973180693], [9010263965687315507, "http", false, 3354175764241350141], [9451456094439810778, "regex", false, 1995452313642458860], [9689903380558560274, "serde", false, 10996426815917291500], [10806645703491011684, "thiserror", false, 44651847147331727], [11989259058781683633, "dunce", false, 13340844955090052542], [13625485746686963219, "anyhow", false, 8308486080552771400], [14132538657330703225, "brotli", false, 917356868234893168], [15367738274754116744, "serde_json", false, 1559938316259437281], [15622660310229662834, "walkdir", false, 3985564515393692626], [17146114186171651583, "infer", false, 2692251160529805456], [17155886227862585100, "glob", false, 16192797392537921819], [17186037756130803222, "phf", false, 3703714224158823094]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-58c7857629e9a403\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}