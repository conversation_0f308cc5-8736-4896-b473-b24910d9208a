use tauri::State;
use crate::api::{WhatsAppApi, Client};
use crate::error::AppError;
use crate::state::AppState;

/// Check WhatsApp service health
#[tauri::command]
pub async fn check_whatsapp_health(state: State<'_, AppState>) -> Result<bool, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.health_check().await.map_err(|e| e.to_string())
}

/// Get all connected WhatsApp clients
#[tauri::command]
pub async fn get_whatsapp_clients(state: State<'_, AppState>) -> Result<Vec<Client>, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.get_clients().await.map_err(|e| e.to_string())
}

/// Connect a WhatsApp client
#[tauri::command]
pub async fn connect_whatsapp_client(
    phone: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.connect_client(&phone).await.map_err(|e| e.to_string())
}

/// Disconnect a WhatsApp client
#[tauri::command]
pub async fn disconnect_whatsapp_client(
    phone: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.disconnect_client(&phone).await.map_err(|e| e.to_string())
}

/// Get QR code for WhatsApp pairing
#[tauri::command]
pub async fn get_whatsapp_qr_code(
    phone: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.get_qr_code(&phone).await.map_err(|e| e.to_string())
}
