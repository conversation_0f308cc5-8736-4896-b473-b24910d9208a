D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\libdb_migration-2ae1e00c0a41cc34.rmeta: db\migration\src\lib.rs db\migration\src\m20241224_000001_create_accounts_table.rs db\migration\src\m20241224_000002_create_medicines_table.rs db\migration\src\m20241224_000003_create_messages_table.rs db\migration\src\m20241224_000004_create_message_medicines_table.rs db\migration\src\m20241224_000005_create_matches_table.rs Cargo.toml

D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\db_migration-2ae1e00c0a41cc34.d: db\migration\src\lib.rs db\migration\src\m20241224_000001_create_accounts_table.rs db\migration\src\m20241224_000002_create_medicines_table.rs db\migration\src\m20241224_000003_create_messages_table.rs db\migration\src\m20241224_000004_create_message_medicines_table.rs db\migration\src\m20241224_000005_create_matches_table.rs Cargo.toml

db\migration\src\lib.rs:
db\migration\src\m20241224_000001_create_accounts_table.rs:
db\migration\src\m20241224_000002_create_medicines_table.rs:
db\migration\src\m20241224_000003_create_messages_table.rs:
db\migration\src\m20241224_000004_create_message_medicines_table.rs:
db\migration\src\m20241224_000005_create_matches_table.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
