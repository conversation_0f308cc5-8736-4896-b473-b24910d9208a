//! Error types for the service layer

use sea_orm::DbErr;
use thiserror::Error;

/// Custom error type for the service layer
#[derive(E<PERSON><PERSON>, Debug)]
pub enum ServiceError {
    /// Database connection error
    #[error("Database error: {0}")]
    Database(#[from] DbErr),

    /// JSON error
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),

    /// Not found error
    #[error("Not found: {0}")]
    NotFound(String),

    /// Validation error
    #[error("Validation error: {0}")]
    Validation(String),
}
