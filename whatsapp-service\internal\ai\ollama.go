package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"
)

// OllamaClient represents a client for Ollama AI service
type OllamaClient struct {
	baseURL    string
	model      string
	timeout    time.Duration
	httpClient *http.Client
	logger     *zap.Logger
}

// NewOllamaClient creates a new Ollama client
func NewOllamaClient(baseURL, model string, timeout time.Duration, logger *zap.Logger) *OllamaClient {
	return &OllamaClient{
		baseURL: baseURL,
		model:   model,
		timeout: timeout,
		httpClient: &http.Client{
			Timeout: timeout,
		},
		logger: logger,
	}
}

// GenerateRequest represents a request to Ollama generate API
type GenerateRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt"`
	Stream bool   `json:"stream"`
}

// GenerateResponse represents a response from Ollama generate API
type GenerateResponse struct {
	Model     string `json:"model"`
	Response  string `json:"response"`
	Done      bool   `json:"done"`
	Context   []int  `json:"context,omitempty"`
	CreatedAt string `json:"created_at"`
}

// MedicineAnalysis represents the result of AI analysis
type MedicineAnalysis struct {
	MessageType string      `json:"message_type"` // "request", "offer", "unknown"
	Medicines   []Medicine  `json:"medicines"`
	Confidence  float64     `json:"confidence"`
	Language    string      `json:"language"`
	Summary     string      `json:"summary"`
}

// Medicine represents a medicine found in the message
type Medicine struct {
	Name       string  `json:"name"`
	Quantity   string  `json:"quantity,omitempty"`
	Unit       string  `json:"unit,omitempty"`
	Price      string  `json:"price,omitempty"`
	Currency   string  `json:"currency,omitempty"`
	Confidence float64 `json:"confidence"`
	Position   struct {
		Start int `json:"start"`
		End   int `json:"end"`
	} `json:"position,omitempty"`
}

// AnalyzeMessage analyzes a WhatsApp message to extract medicine information
func (c *OllamaClient) AnalyzeMessage(ctx context.Context, message string) (*MedicineAnalysis, error) {
	prompt := c.buildAnalysisPrompt(message)
	
	response, err := c.generate(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to generate AI response: %w", err)
	}

	analysis, err := c.parseAnalysisResponse(response)
	if err != nil {
		c.logger.Error("Failed to parse AI response", 
			zap.Error(err), 
			zap.String("response", response))
		
		// Return a basic analysis if parsing fails
		return &MedicineAnalysis{
			MessageType: "unknown",
			Medicines:   []Medicine{},
			Confidence:  0.0,
			Language:    "ar",
			Summary:     "فشل في تحليل الرسالة",
		}, nil
	}

	return analysis, nil
}

// generate sends a request to Ollama and returns the response
func (c *OllamaClient) generate(ctx context.Context, prompt string) (string, error) {
	request := GenerateRequest{
		Model:  c.model,
		Prompt: prompt,
		Stream: false,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/api/generate", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("ollama returned status %d: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	var response GenerateResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return response.Response, nil
}

// buildAnalysisPrompt creates a prompt for analyzing WhatsApp messages
func (c *OllamaClient) buildAnalysisPrompt(message string) string {
	return fmt.Sprintf(`
أنت خبير في تحليل الرسائل الطبية باللغة العربية. مهمتك هي تحليل رسائل WhatsApp لاستخراج معلومات الأدوية.

قم بتحليل الرسالة التالية وحدد:
1. نوع الرسالة: "request" (طلب دواء) أو "offer" (عرض دواء) أو "unknown" (غير محدد)
2. الأدوية المذكورة مع تفاصيلها
3. الكمية والوحدة إن وجدت
4. السعر والعملة إن وجدت
5. مستوى الثقة في التحليل (0-1)

الرسالة:
"%s"

أرجع النتيجة في صيغة JSON بالشكل التالي:
{
  "message_type": "request|offer|unknown",
  "medicines": [
    {
      "name": "اسم الدواء",
      "quantity": "الكمية",
      "unit": "الوحدة",
      "price": "السعر",
      "currency": "العملة",
      "confidence": 0.95
    }
  ],
  "confidence": 0.85,
  "language": "ar",
  "summary": "ملخص قصير للرسالة"
}

تأكد من أن الإجابة JSON صحيحة ومكتملة.
`, message)
}

// parseAnalysisResponse parses the AI response into MedicineAnalysis
func (c *OllamaClient) parseAnalysisResponse(response string) (*MedicineAnalysis, error) {
	// Clean the response - sometimes AI adds extra text
	response = strings.TrimSpace(response)
	
	// Find JSON content
	start := strings.Index(response, "{")
	end := strings.LastIndex(response, "}")
	
	if start == -1 || end == -1 || start >= end {
		return nil, fmt.Errorf("no valid JSON found in response")
	}
	
	jsonStr := response[start : end+1]
	
	var analysis MedicineAnalysis
	if err := json.Unmarshal([]byte(jsonStr), &analysis); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	
	// Validate and normalize the analysis
	if analysis.MessageType == "" {
		analysis.MessageType = "unknown"
	}
	
	if analysis.Language == "" {
		analysis.Language = "ar"
	}
	
	if analysis.Confidence < 0 {
		analysis.Confidence = 0
	} else if analysis.Confidence > 1 {
		analysis.Confidence = 1
	}
	
	// Validate medicines
	for i := range analysis.Medicines {
		if analysis.Medicines[i].Confidence < 0 {
			analysis.Medicines[i].Confidence = 0
		} else if analysis.Medicines[i].Confidence > 1 {
			analysis.Medicines[i].Confidence = 1
		}
	}
	
	return &analysis, nil
}

// IsHealthy checks if Ollama service is available
func (c *OllamaClient) IsHealthy(ctx context.Context) bool {
	req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/api/tags", nil)
	if err != nil {
		return false
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// GetAvailableModels returns list of available models
func (c *OllamaClient) GetAvailableModels(ctx context.Context) ([]string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/api/tags", nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get models: status %d", resp.StatusCode)
	}

	var response struct {
		Models []struct {
			Name string `json:"name"`
		} `json:"models"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, err
	}

	models := make([]string, len(response.Models))
	for i, model := range response.Models {
		models[i] = model.Name
	}

	return models, nil
}
