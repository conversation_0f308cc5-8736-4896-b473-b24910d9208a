use sea_orm_migration::{prelude::*, schema::*};

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(Messages::Table)
                    .if_not_exists()
                    .col(pk_auto(Messages::Id))
                    .col(string_uniq(Messages::WhatsappMessageId))
                    .col(integer(Messages::AccountId))
                    .col(string(Messages::GroupJid))
                    .col(string(Messages::GroupName))
                    .col(string(Messages::SenderJid))
                    .col(string(Messages::SenderName))
                    .col(text(Messages::Content))
                    .col(string(Messages::MessageType).default("unknown"))
                    .col(string(Messages::ProcessingStatus).default("pending"))
                    .col(text_null(Messages::AiAnalysis))
                    .col(text_null(Messages::ErrorMessage))
                    .col(timestamp_with_time_zone(Messages::MessageTimestamp))
                    .col(timestamp_with_time_zone(Messages::ReceivedAt).default(Expr::current_timestamp()))
                    .col(timestamp_null(Messages::ProcessedAt))
                    .col(timestamp_with_time_zone(Messages::CreatedAt).default(Expr::current_timestamp()))
                    .col(timestamp_with_time_zone(Messages::UpdatedAt).default(Expr::current_timestamp()))
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_messages_account_id")
                            .from(Messages::Table, Messages::AccountId)
                            .to(Accounts::Table, Accounts::Id)
                            .on_delete(ForeignKeyAction::Cascade)
                            .on_update(ForeignKeyAction::Cascade),
                    )
                    .to_owned(),
            )
            .await?;

        // Create index on account_id for filtering by account
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_messages_account_id")
                    .table(Messages::Table)
                    .col(Messages::AccountId)
                    .to_owned(),
            )
            .await?;

        // Create index on group_jid for filtering by group
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_messages_group_jid")
                    .table(Messages::Table)
                    .col(Messages::GroupJid)
                    .to_owned(),
            )
            .await?;

        // Create index on message_type for filtering
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_messages_message_type")
                    .table(Messages::Table)
                    .col(Messages::MessageType)
                    .to_owned(),
            )
            .await?;

        // Create index on processing_status for filtering
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_messages_processing_status")
                    .table(Messages::Table)
                    .col(Messages::ProcessingStatus)
                    .to_owned(),
            )
            .await?;

        // Create index on message_timestamp for chronological ordering
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_messages_message_timestamp")
                    .table(Messages::Table)
                    .col(Messages::MessageTimestamp)
                    .to_owned(),
            )
            .await?;

        // Create index on sender_jid for filtering by sender
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_messages_sender_jid")
                    .table(Messages::Table)
                    .col(Messages::SenderJid)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Messages::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum Messages {
    Table,
    Id,
    WhatsappMessageId,
    AccountId,
    GroupJid,
    GroupName,
    SenderJid,
    SenderName,
    Content,
    MessageType,
    ProcessingStatus,
    AiAnalysis,
    ErrorMessage,
    MessageTimestamp,
    ReceivedAt,
    ProcessedAt,
    CreatedAt,
    UpdatedAt,
}

#[derive(DeriveIden)]
enum Accounts {
    Table,
    Id,
}
