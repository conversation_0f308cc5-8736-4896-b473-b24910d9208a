import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import type React from 'react';
import { useRef } from 'react';

export interface DockProps {
  className?: string;
  children: React.ReactNode;
  direction?: 'top' | 'middle' | 'bottom';
}

const dockVariants = cva(
  'mx-auto flex h-[58px] w-max gap-2 rounded-2xl border p-2 backdrop-blur-md',
  {
    variants: {
      direction: {
        top: 'items-start',
        middle: 'items-center',
        bottom: 'items-end',
      },
    },
    defaultVariants: {
      direction: 'middle',
    },
  },
);

const Dock = ({ className, children, direction, ...props }: DockProps) => {
  return (
    <motion.div
      className={cn(dockVariants({ direction }), className)}
      {...props}
    >
      {children}
    </motion.div>
  );
};
Dock.displayName = 'Dock';

export interface DockIconProps {
  size?: number;
  magnification?: number;
  distance?: number;
  children?: React.ReactNode;
  className?: string;
  icon?: React.ComponentType<any>;
  isActive?: boolean;
  onClick?: () => void;
}

const DockIcon = ({
  size = 40,
  magnification = 60,
  distance = 140,
  children,
  className,
  icon: Icon,
  isActive,
  onClick,
}: DockIconProps) => {
  const ref = useRef<HTMLDivElement>(null);

  const mouseX = useMotionValue(Number.POSITIVE_INFINITY);

  const width = useSpring(
    useTransform(mouseX, [-distance, 0, distance], [size, magnification, size]),
    {
      mass: 0.1,
      stiffness: 150,
      damping: 12,
    },
  );

  const height = useSpring(
    useTransform(mouseX, [-distance, 0, distance], [size, magnification, size]),
    {
      mass: 0.1,
      stiffness: 150,
      damping: 12,
    },
  );

  return (
    <motion.div
      ref={ref}
      style={{ width, height }}
      onMouseMove={e => {
        if (ref.current) {
          const rect = ref.current.getBoundingClientRect();
          const centerX = rect.left + rect.width / 2;
          mouseX.set(e.clientX - centerX);
        }
      }}
      onClick={onClick}
      onMouseLeave={() => mouseX.set(Number.POSITIVE_INFINITY)}
      className={cn(
        'flex aspect-square cursor-pointer items-center justify-center rounded-full',
        isActive && 'bg-primary/10 text-primary',
        className,
      )}
    >
      {Icon ? <Icon className="h-5 w-5" /> : children}
    </motion.div>
  );
};

DockIcon.displayName = 'DockIcon';

export { Dock, DockIcon, dockVariants };
