import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { 
  FileText, 
  Download, 
  Calendar, 
  BarChart3, 
  TrendingUp,
  Filter,
  RefreshCw,
  Printer,
  Share,
  Eye
} from 'lucide-react'

interface ReportData {
  id: string
  title: string
  description: string
  type: 'messages' | 'matches' | 'medicines' | 'performance'
  period: string
  generatedAt: Date
  size: string
  status: 'ready' | 'generating' | 'failed'
}

export const Route = createFileRoute('/reports')({
  component: ReportsPage,
})

function ReportsPage() {
  const [reports, setReports] = useState<ReportData[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState('7d')
  const [selectedType, setSelectedType] = useState('all')

  useEffect(() => {
    loadReports()
  }, [])

  const loadReports = async () => {
    try {
      setLoading(true)
      // Mock data - في التطبيق الحقيقي سيتم جلب البيانات من API
      const mockReports: ReportData[] = [
        {
          id: '1',
          title: 'تقرير الرسائل الأسبوعي',
          description: 'تحليل شامل للرسائل المعالجة خلال الأسبوع الماضي',
          type: 'messages',
          period: '7 أيام',
          generatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          size: '2.3 MB',
          status: 'ready'
        },
        {
          id: '2',
          title: 'تقرير المطابقات الشهري',
          description: 'إحصائيات المطابقات ومعدلات النجاح للشهر الحالي',
          type: 'matches',
          period: '30 يوم',
          generatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          size: '1.8 MB',
          status: 'ready'
        },
        {
          id: '3',
          title: 'تقرير الأدوية المكتشفة',
          description: 'قائمة بالأدوية الجديدة المكتشفة وتصنيفاتها',
          type: 'medicines',
          period: '14 يوم',
          generatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
          size: '950 KB',
          status: 'ready'
        },
        {
          id: '4',
          title: 'تقرير الأداء اليومي',
          description: 'مؤشرات الأداء والإحصائيات التفصيلية',
          type: 'performance',
          period: '24 ساعة',
          generatedAt: new Date(),
          size: '1.2 MB',
          status: 'generating'
        }
      ]
      setReports(mockReports)
    } catch (error) {
      toast.error('فشل في تحميل التقارير')
    } finally {
      setLoading(false)
    }
  }

  const generateReport = async (type: string, period: string) => {
    try {
      setLoading(true)
      // TODO: Implement API call to generate report
      await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call
      toast.success('تم إنشاء التقرير بنجاح')
      loadReports()
    } catch (error) {
      toast.error('فشل في إنشاء التقرير')
    } finally {
      setLoading(false)
    }
  }

  const downloadReport = (reportId: string) => {
    // TODO: Implement download functionality
    toast.success('تم بدء تحميل التقرير')
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'messages':
        return <FileText className="w-4 h-4" />
      case 'matches':
        return <TrendingUp className="w-4 h-4" />
      case 'medicines':
        return <BarChart3 className="w-4 h-4" />
      case 'performance':
        return <Calendar className="w-4 h-4" />
      default:
        return <FileText className="w-4 h-4" />
    }
  }

  const getTypeName = (type: string) => {
    switch (type) {
      case 'messages':
        return 'الرسائل'
      case 'matches':
        return 'المطابقات'
      case 'medicines':
        return 'الأدوية'
      case 'performance':
        return 'الأداء'
      default:
        return type
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ready':
        return <Badge variant="default">جاهز</Badge>
      case 'generating':
        return <Badge variant="outline">قيد الإنشاء</Badge>
      case 'failed':
        return <Badge variant="destructive">فشل</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const filteredReports = reports.filter(report => {
    if (selectedType === 'all') return true
    return report.type === selectedType
  })

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">التقارير</h1>
          <p className="text-muted-foreground">إنشاء وإدارة تقارير النظام</p>
        </div>
        
        <Button onClick={() => generateReport(selectedType, selectedPeriod)} disabled={loading}>
          <FileText className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          إنشاء تقرير جديد
        </Button>
      </div>

      {/* Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle>إنشاء تقرير جديد</CardTitle>
          <CardDescription>اختر نوع التقرير والفترة الزمنية</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label>نوع التقرير</Label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="messages">تقرير الرسائل</SelectItem>
                  <SelectItem value="matches">تقرير المطابقات</SelectItem>
                  <SelectItem value="medicines">تقرير الأدوية</SelectItem>
                  <SelectItem value="performance">تقرير الأداء</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>الفترة الزمنية</Label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1d">يوم واحد</SelectItem>
                  <SelectItem value="7d">أسبوع</SelectItem>
                  <SelectItem value="30d">شهر</SelectItem>
                  <SelectItem value="90d">3 أشهر</SelectItem>
                  <SelectItem value="365d">سنة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button 
                onClick={() => generateReport(selectedType, selectedPeriod)} 
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <FileText className="w-4 h-4 mr-2" />
                )}
                إنشاء التقرير
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            فلترة التقارير
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="نوع التقرير" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="messages">الرسائل</SelectItem>
                  <SelectItem value="matches">المطابقات</SelectItem>
                  <SelectItem value="medicines">الأدوية</SelectItem>
                  <SelectItem value="performance">الأداء</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button variant="outline" onClick={loadReports}>
              <RefreshCw className="w-4 h-4 mr-2" />
              تحديث
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Reports List */}
      <div className="grid gap-4">
        {filteredReports.map((report) => (
          <Card key={report.id}>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-muted rounded-lg">
                    {getTypeIcon(report.type)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-semibold">{report.title}</h3>
                      {getStatusBadge(report.status)}
                      <Badge variant="outline">{getTypeName(report.type)}</Badge>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-2">
                      {report.description}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span>الفترة: {report.period}</span>
                      <span>الحجم: {report.size}</span>
                      <span>تم الإنشاء: {formatDate(report.generatedAt)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={report.status !== 'ready'}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    عرض
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadReport(report.id)}
                    disabled={report.status !== 'ready'}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    تحميل
                  </Button>
                  
                  <Button variant="outline" size="sm">
                    <Share className="w-4 h-4 mr-2" />
                    مشاركة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredReports.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <FileText className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">لا توجد تقارير</h3>
              <p className="text-muted-foreground mb-4">
                لم يتم العثور على تقارير مطابقة للفلاتر المحددة
              </p>
              <Button onClick={() => generateReport('messages', '7d')}>
                <FileText className="w-4 h-4 mr-2" />
                إنشاء أول تقرير
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
