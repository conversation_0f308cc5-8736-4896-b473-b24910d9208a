package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"go.uber.org/zap"

	"medibridge-whatsapp/internal/ai"
	"medibridge-whatsapp/internal/api"
	"medibridge-whatsapp/internal/config"
	"medibridge-whatsapp/internal/database"
	"medibridge-whatsapp/internal/matching"
	"medibridge-whatsapp/internal/whatsapp"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Initialize logger
	logger, err := zap.NewProduction()
	if err != nil {
		log.Fatal("Failed to initialize logger:", err)
	}
	defer logger.Sync()

	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		logger.Fatal("Failed to connect to database", zap.Error(err))
	}
	defer db.Close()

	// Initialize Ollama client
	ollamaClient := ai.NewOllamaClient(
		cfg.OllamaURL,
		cfg.AIModel,
		time.Duration(cfg.AITimeout)*time.Second,
		logger,
	)

	// Initialize AI message processor
	aiProcessor := ai.NewMessageProcessor(
		logger,
		db,
		ollamaClient,
		10,             // batch size
		10*time.Second, // processing interval
	)

	// Initialize matching service
	matchingService := matching.NewService(
		logger,
		db,
		5*time.Minute, // matching interval
	)

	// Initialize WhatsApp manager
	waManager := whatsapp.NewManager(logger, db)

	// Initialize API server
	apiServer := api.NewServer(logger, waManager, db, aiProcessor, matchingService)

	// Setup Gin router
	router := gin.Default()
	apiServer.SetupRoutes(router)

	// Create HTTP server
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Port),
		Handler: router,
	}

	// Start AI processor in a goroutine
	ctx, cancel := context.WithCancel(context.Background())
	go aiProcessor.Start(ctx)

	// Start matching service in a goroutine
	go matchingService.Start(ctx)

	// Start server in a goroutine
	go func() {
		logger.Info("Starting WhatsApp service", zap.Int("port", cfg.Port))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Cancel AI processor and matching service
	cancel()

	// Stop matching service
	matchingService.Stop()

	// Graceful shutdown with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Shutdown WhatsApp connections
	waManager.Shutdown()

	// Shutdown HTTP server
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatal("Server forced to shutdown", zap.Error(err))
	}

	logger.Info("Server exited")
}
