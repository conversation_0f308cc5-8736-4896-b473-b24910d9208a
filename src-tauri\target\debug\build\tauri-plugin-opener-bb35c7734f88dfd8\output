cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\build\tauri-plugin-opener-bb35c7734f88dfd8\out\tauri-plugin-opener-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\build\tauri-plugin-opener-bb35c7734f88dfd8\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-opener-2.2.7\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
