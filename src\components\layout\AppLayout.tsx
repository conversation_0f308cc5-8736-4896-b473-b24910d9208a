import { useState } from 'react';
import { Link, useLocation } from '@tanstack/react-router';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import {
  LayoutDashboard,
  Users,
  MessageSquare,
  Pill,
  BarChart3,
  Settings,
  Menu,
  Activity,
  FileText,
  TrendingUp,
} from 'lucide-react';

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
}

const navItems: NavItem[] = [
  {
    title: 'لوحة التحكم',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'الحسابات',
    href: '/accounts',
    icon: Users,
  },
  {
    title: 'الرسائل',
    href: '/messages',
    icon: MessageSquare,
  },
  {
    title: 'الأدوية',
    href: '/medicines',
    icon: Pill,
  },
  {
    title: 'المطابقات',
    href: '/matches',
    icon: Activity,
  },
  {
    title: 'الإحصائيات',
    href: '/statistics',
    icon: TrendingUp,
  },
  {
    title: 'التقارير',
    href: '/reports',
    icon: FileText,
  },
  {
    title: 'الإعدادات',
    href: '/settings',
    icon: Settings,
  },
];

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const isActive = (href: string) => {
    return location.pathname === href;
  };

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      {/* Logo */}
      <div className="flex h-16 items-center border-b px-6">
        <div className="flex items-center space-x-2">
          <div className="bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-lg">
            <Activity className="h-4 w-4" />
          </div>
          <div>
            <h1 className="text-lg font-semibold">MediBridge</h1>
            <p className="text-muted-foreground text-xs">إدارة الأدوية</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4">
        {navItems.map(item => {
          const Icon = item.icon;
          const active = isActive(item.href);

          return (
            <Link
              key={item.href}
              to={item.href}
              className={`flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
                active
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-muted hover:text-foreground'
              }`}
              onClick={() => setSidebarOpen(false)}
            >
              <Icon className="h-4 w-4" />
              <span>{item.title}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="border-t p-4">
        <div className="text-muted-foreground text-xs">
          <p>MediBridge v1.0.0</p>
          <p>نظام إدارة الأدوية الذكي</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-background flex h-screen">
      {/* Desktop Sidebar */}
      <div className="bg-card hidden w-64 border-r lg:block">
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="right" className="w-64 p-0">
          <SidebarContent />
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-card flex h-16 items-center border-b px-6">
          <div className="flex items-center space-x-4">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="lg:hidden">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger>
            </Sheet>

            {/* Breadcrumb */}
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-muted-foreground">MediBridge</span>
              <span className="text-muted-foreground">/</span>
              <span className="font-medium">
                {navItems.find(item => isActive(item.href))?.title ||
                  'الصفحة الرئيسية'}
              </span>
            </div>
          </div>

          <div className="ml-auto flex items-center space-x-4">
            {/* Status Indicator */}
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span className="text-muted-foreground text-xs">متصل</span>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto">{children}</main>
      </div>
    </div>
  );
}
