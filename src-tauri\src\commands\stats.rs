use tauri::State;
use crate::api::{WhatsAppApi, StatsOverview, MessageStats};
use crate::error::AppError;
use crate::state::AppState;

/// Get general statistics overview
#[tauri::command]
pub async fn get_stats_overview(state: State<'_, AppState>) -> Result<StatsOverview, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.get_stats_overview().await.map_err(|e| e.to_string())
}

/// Get message statistics
#[tauri::command]
pub async fn get_message_stats(state: State<'_, AppState>) -> Result<MessageStats, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.get_message_stats().await.map_err(|e| e.to_string())
}
