
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>ai: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">medibridge-whatsapp/internal/ai/ollama.go (0.0%)</option>
				
				<option value="file1">medibridge-whatsapp/internal/ai/processor.go (0.0%)</option>
				
				<option value="file2">medibridge-whatsapp/internal/api/server.go (0.0%)</option>
				
				<option value="file3">medibridge-whatsapp/internal/config/config.go (0.0%)</option>
				
				<option value="file4">medibridge-whatsapp/internal/database/database.go (0.0%)</option>
				
				<option value="file5">medibridge-whatsapp/internal/matching/engine.go (0.0%)</option>
				
				<option value="file6">medibridge-whatsapp/internal/matching/service.go (0.0%)</option>
				
				<option value="file7">medibridge-whatsapp/internal/performance/optimizer.go (88.1%)</option>
				
				<option value="file8">medibridge-whatsapp/internal/whatsapp/manager.go (0.0%)</option>
				
				<option value="file9">medibridge-whatsapp/main.go (0.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package ai

import (
        "bytes"
        "context"
        "encoding/json"
        "fmt"
        "io"
        "net/http"
        "strings"
        "time"

        "go.uber.org/zap"
)

// OllamaClient represents a client for Ollama AI service
type OllamaClient struct {
        baseURL    string
        model      string
        timeout    time.Duration
        httpClient *http.Client
        logger     *zap.Logger
}

// NewOllamaClient creates a new Ollama client
func NewOllamaClient(baseURL, model string, timeout time.Duration, logger *zap.Logger) *OllamaClient <span class="cov0" title="0">{
        return &amp;OllamaClient{
                baseURL: baseURL,
                model:   model,
                timeout: timeout,
                httpClient: &amp;http.Client{
                        Timeout: timeout,
                },
                logger: logger,
        }
}</span>

// GenerateRequest represents a request to Ollama generate API
type GenerateRequest struct {
        Model  string `json:"model"`
        Prompt string `json:"prompt"`
        Stream bool   `json:"stream"`
}

// GenerateResponse represents a response from Ollama generate API
type GenerateResponse struct {
        Model     string `json:"model"`
        Response  string `json:"response"`
        Done      bool   `json:"done"`
        Context   []int  `json:"context,omitempty"`
        CreatedAt string `json:"created_at"`
}

// MedicineAnalysis represents the result of AI analysis
type MedicineAnalysis struct {
        MessageType string      `json:"message_type"` // "request", "offer", "unknown"
        Medicines   []Medicine  `json:"medicines"`
        Confidence  float64     `json:"confidence"`
        Language    string      `json:"language"`
        Summary     string      `json:"summary"`
}

// Medicine represents a medicine found in the message
type Medicine struct {
        Name       string  `json:"name"`
        Quantity   string  `json:"quantity,omitempty"`
        Unit       string  `json:"unit,omitempty"`
        Price      string  `json:"price,omitempty"`
        Currency   string  `json:"currency,omitempty"`
        Confidence float64 `json:"confidence"`
        Position   struct {
                Start int `json:"start"`
                End   int `json:"end"`
        } `json:"position,omitempty"`
}

// AnalyzeMessage analyzes a WhatsApp message to extract medicine information
func (c *OllamaClient) AnalyzeMessage(ctx context.Context, message string) (*MedicineAnalysis, error) <span class="cov0" title="0">{
        prompt := c.buildAnalysisPrompt(message)
        
        response, err := c.generate(ctx, prompt)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to generate AI response: %w", err)
        }</span>

        <span class="cov0" title="0">analysis, err := c.parseAnalysisResponse(response)
        if err != nil </span><span class="cov0" title="0">{
                c.logger.Error("Failed to parse AI response", 
                        zap.Error(err), 
                        zap.String("response", response))
                
                // Return a basic analysis if parsing fails
                return &amp;MedicineAnalysis{
                        MessageType: "unknown",
                        Medicines:   []Medicine{},
                        Confidence:  0.0,
                        Language:    "ar",
                        Summary:     "فشل في تحليل الرسالة",
                }, nil
        }</span>

        <span class="cov0" title="0">return analysis, nil</span>
}

// generate sends a request to Ollama and returns the response
func (c *OllamaClient) generate(ctx context.Context, prompt string) (string, error) <span class="cov0" title="0">{
        request := GenerateRequest{
                Model:  c.model,
                Prompt: prompt,
                Stream: false,
        }

        jsonData, err := json.Marshal(request)
        if err != nil </span><span class="cov0" title="0">{
                return "", fmt.Errorf("failed to marshal request: %w", err)
        }</span>

        <span class="cov0" title="0">req, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/api/generate", bytes.NewBuffer(jsonData))
        if err != nil </span><span class="cov0" title="0">{
                return "", fmt.Errorf("failed to create request: %w", err)
        }</span>

        <span class="cov0" title="0">req.Header.Set("Content-Type", "application/json")

        resp, err := c.httpClient.Do(req)
        if err != nil </span><span class="cov0" title="0">{
                return "", fmt.Errorf("failed to send request: %w", err)
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        if resp.StatusCode != http.StatusOK </span><span class="cov0" title="0">{
                body, _ := io.ReadAll(resp.Body)
                return "", fmt.Errorf("ollama returned status %d: %s", resp.StatusCode, string(body))
        }</span>

        <span class="cov0" title="0">body, err := io.ReadAll(resp.Body)
        if err != nil </span><span class="cov0" title="0">{
                return "", fmt.Errorf("failed to read response body: %w", err)
        }</span>

        <span class="cov0" title="0">var response GenerateResponse
        if err := json.Unmarshal(body, &amp;response); err != nil </span><span class="cov0" title="0">{
                return "", fmt.Errorf("failed to unmarshal response: %w", err)
        }</span>

        <span class="cov0" title="0">return response.Response, nil</span>
}

// buildAnalysisPrompt creates a prompt for analyzing WhatsApp messages
func (c *OllamaClient) buildAnalysisPrompt(message string) string <span class="cov0" title="0">{
        return fmt.Sprintf(`
أنت خبير في تحليل الرسائل الطبية باللغة العربية. مهمتك هي تحليل رسائل WhatsApp لاستخراج معلومات الأدوية.

قم بتحليل الرسالة التالية وحدد:
1. نوع الرسالة: "request" (طلب دواء) أو "offer" (عرض دواء) أو "unknown" (غير محدد)
2. الأدوية المذكورة مع تفاصيلها
3. الكمية والوحدة إن وجدت
4. السعر والعملة إن وجدت
5. مستوى الثقة في التحليل (0-1)

الرسالة:
"%s"

أرجع النتيجة في صيغة JSON بالشكل التالي:
{
  "message_type": "request|offer|unknown",
  "medicines": [
    {
      "name": "اسم الدواء",
      "quantity": "الكمية",
      "unit": "الوحدة",
      "price": "السعر",
      "currency": "العملة",
      "confidence": 0.95
    }
  ],
  "confidence": 0.85,
  "language": "ar",
  "summary": "ملخص قصير للرسالة"
}

تأكد من أن الإجابة JSON صحيحة ومكتملة.
`, message)
}</span>

// parseAnalysisResponse parses the AI response into MedicineAnalysis
func (c *OllamaClient) parseAnalysisResponse(response string) (*MedicineAnalysis, error) <span class="cov0" title="0">{
        // Clean the response - sometimes AI adds extra text
        response = strings.TrimSpace(response)
        
        // Find JSON content
        start := strings.Index(response, "{")
        end := strings.LastIndex(response, "}")
        
        if start == -1 || end == -1 || start &gt;= end </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("no valid JSON found in response")
        }</span>
        
        <span class="cov0" title="0">jsonStr := response[start : end+1]
        
        var analysis MedicineAnalysis
        if err := json.Unmarshal([]byte(jsonStr), &amp;analysis); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
        }</span>
        
        // Validate and normalize the analysis
        <span class="cov0" title="0">if analysis.MessageType == "" </span><span class="cov0" title="0">{
                analysis.MessageType = "unknown"
        }</span>
        
        <span class="cov0" title="0">if analysis.Language == "" </span><span class="cov0" title="0">{
                analysis.Language = "ar"
        }</span>
        
        <span class="cov0" title="0">if analysis.Confidence &lt; 0 </span><span class="cov0" title="0">{
                analysis.Confidence = 0
        }</span> else<span class="cov0" title="0"> if analysis.Confidence &gt; 1 </span><span class="cov0" title="0">{
                analysis.Confidence = 1
        }</span>
        
        // Validate medicines
        <span class="cov0" title="0">for i := range analysis.Medicines </span><span class="cov0" title="0">{
                if analysis.Medicines[i].Confidence &lt; 0 </span><span class="cov0" title="0">{
                        analysis.Medicines[i].Confidence = 0
                }</span> else<span class="cov0" title="0"> if analysis.Medicines[i].Confidence &gt; 1 </span><span class="cov0" title="0">{
                        analysis.Medicines[i].Confidence = 1
                }</span>
        }
        
        <span class="cov0" title="0">return &amp;analysis, nil</span>
}

// IsHealthy checks if Ollama service is available
func (c *OllamaClient) IsHealthy(ctx context.Context) bool <span class="cov0" title="0">{
        req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/api/tags", nil)
        if err != nil </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov0" title="0">resp, err := c.httpClient.Do(req)
        if err != nil </span><span class="cov0" title="0">{
                return false
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        return resp.StatusCode == http.StatusOK</span>
}

// GetAvailableModels returns list of available models
func (c *OllamaClient) GetAvailableModels(ctx context.Context) ([]string, error) <span class="cov0" title="0">{
        req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/api/tags", nil)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">resp, err := c.httpClient.Do(req)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        if resp.StatusCode != http.StatusOK </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get models: status %d", resp.StatusCode)
        }</span>

        <span class="cov0" title="0">var response struct {
                Models []struct {
                        Name string `json:"name"`
                } `json:"models"`
        }

        if err := json.NewDecoder(resp.Body).Decode(&amp;response); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">models := make([]string, len(response.Models))
        for i, model := range response.Models </span><span class="cov0" title="0">{
                models[i] = model.Name
        }</span>

        <span class="cov0" title="0">return models, nil</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package ai

import (
        "context"
        "encoding/json"
        "fmt"
        "time"

        "go.uber.org/zap"

        "medibridge-whatsapp/internal/database"
)

// MessageProcessor handles AI processing of WhatsApp messages
type MessageProcessor struct {
        logger      *zap.Logger
        db          *database.DB
        ollamaClient *OllamaClient
        batchSize   int
        interval    time.Duration
}

// NewMessageProcessor creates a new message processor
func NewMessageProcessor(
        logger *zap.Logger,
        db *database.DB,
        ollamaClient *OllamaClient,
        batchSize int,
        interval time.Duration,
) *MessageProcessor <span class="cov0" title="0">{
        return &amp;MessageProcessor{
                logger:       logger,
                db:           db,
                ollamaClient: ollamaClient,
                batchSize:    batchSize,
                interval:     interval,
        }
}</span>

// Start begins processing messages in batches
func (p *MessageProcessor) Start(ctx context.Context) <span class="cov0" title="0">{
        ticker := time.NewTicker(p.interval)
        defer ticker.Stop()

        p.logger.Info("Starting AI message processor",
                zap.Int("batch_size", p.batchSize),
                zap.Duration("interval", p.interval))

        for </span><span class="cov0" title="0">{
                select </span>{
                case &lt;-ctx.Done():<span class="cov0" title="0">
                        p.logger.Info("AI message processor stopped")
                        return</span>
                case &lt;-ticker.C:<span class="cov0" title="0">
                        if err := p.processBatch(ctx); err != nil </span><span class="cov0" title="0">{
                                p.logger.Error("Failed to process message batch", zap.Error(err))
                        }</span>
                }
        }
}

// processBatch processes a batch of pending messages
func (p *MessageProcessor) processBatch(ctx context.Context) error <span class="cov0" title="0">{
        // Get pending messages
        messages, err := p.db.GetPendingMessages(p.batchSize)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get pending messages: %w", err)
        }</span>

        <span class="cov0" title="0">if len(messages) == 0 </span><span class="cov0" title="0">{
                return nil // No messages to process
        }</span>

        <span class="cov0" title="0">p.logger.Info("Processing message batch", zap.Int("count", len(messages)))

        // Process each message
        for _, msg := range messages </span><span class="cov0" title="0">{
                if err := p.processMessage(ctx, msg); err != nil </span><span class="cov0" title="0">{
                        p.logger.Error("Failed to process message",
                                zap.Int("message_id", msg.ID),
                                zap.Error(err))
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// processMessage processes a single message with AI
func (p *MessageProcessor) processMessage(ctx context.Context, msg *database.Message) error <span class="cov0" title="0">{
        startTime := time.Now()
        
        p.logger.Debug("Processing message",
                zap.Int("message_id", msg.ID),
                zap.String("content", msg.Content[:min(50, len(msg.Content))]))

        // Analyze message with AI
        analysis, err := p.ollamaClient.AnalyzeMessage(ctx, msg.Content)
        if err != nil </span><span class="cov0" title="0">{
                // Mark as failed
                errorMsg := err.Error()
                return p.db.UpdateMessageProcessing(msg.ID, "failed", nil, &amp;errorMsg)
        }</span>

        // Convert analysis to JSON
        <span class="cov0" title="0">analysisJSON, err := json.Marshal(analysis)
        if err != nil </span><span class="cov0" title="0">{
                errorMsg := fmt.Sprintf("failed to marshal analysis: %v", err)
                return p.db.UpdateMessageProcessing(msg.ID, "failed", nil, &amp;errorMsg)
        }</span>

        <span class="cov0" title="0">analysisStr := string(analysisJSON)

        // Update message with analysis
        if err := p.db.UpdateMessageProcessing(msg.ID, "processed", &amp;analysisStr, nil); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to update message processing: %w", err)
        }</span>

        // Update message type if determined
        <span class="cov0" title="0">if analysis.MessageType != "unknown" </span><span class="cov0" title="0">{
                if err := p.updateMessageType(msg.ID, analysis.MessageType); err != nil </span><span class="cov0" title="0">{
                        p.logger.Error("Failed to update message type",
                                zap.Int("message_id", msg.ID),
                                zap.Error(err))
                }</span>
        }

        // Store medicines if found
        <span class="cov0" title="0">if len(analysis.Medicines) &gt; 0 </span><span class="cov0" title="0">{
                if err := p.storeMedicines(msg, analysis.Medicines); err != nil </span><span class="cov0" title="0">{
                        p.logger.Error("Failed to store medicines",
                                zap.Int("message_id", msg.ID),
                                zap.Error(err))
                }</span>
        }

        <span class="cov0" title="0">processingTime := time.Since(startTime)
        p.logger.Info("Message processed successfully",
                zap.Int("message_id", msg.ID),
                zap.String("type", analysis.MessageType),
                zap.Int("medicines_count", len(analysis.Medicines)),
                zap.Float64("confidence", analysis.Confidence),
                zap.Duration("processing_time", processingTime))

        return nil</span>
}

// updateMessageType updates the message type in the database
func (p *MessageProcessor) updateMessageType(messageID int, messageType string) error <span class="cov0" title="0">{
        query := `UPDATE messages SET message_type = $1, updated_at = NOW() WHERE id = $2`
        _, err := p.db.Exec(query, messageType, messageID)
        return err
}</span>

// storeMedicines stores extracted medicines in the database
func (p *MessageProcessor) storeMedicines(msg *database.Message, medicines []Medicine) error <span class="cov0" title="0">{
        for _, medicine := range medicines </span><span class="cov0" title="0">{
                // First, ensure the medicine exists in the medicines table
                medicineID, err := p.ensureMedicineExists(medicine.Name)
                if err != nil </span><span class="cov0" title="0">{
                        p.logger.Error("Failed to ensure medicine exists",
                                zap.String("medicine", medicine.Name),
                                zap.Error(err))
                        continue</span>
                }

                // Insert into message_medicines table
                <span class="cov0" title="0">if err := p.insertMessageMedicine(msg.ID, medicineID, medicine); err != nil </span><span class="cov0" title="0">{
                        p.logger.Error("Failed to insert message medicine",
                                zap.Int("message_id", msg.ID),
                                zap.Int("medicine_id", medicineID),
                                zap.Error(err))
                        continue</span>
                }

                // Update medicine statistics
                <span class="cov0" title="0">isRequest := msg.MessageType == "request"
                if err := p.updateMedicineStats(medicineID, isRequest); err != nil </span><span class="cov0" title="0">{
                        p.logger.Error("Failed to update medicine stats",
                                zap.Int("medicine_id", medicineID),
                                zap.Error(err))
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// ensureMedicineExists ensures a medicine exists in the database and returns its ID
func (p *MessageProcessor) ensureMedicineExists(name string) (int, error) <span class="cov0" title="0">{
        // Try to find existing medicine
        var id int
        query := `SELECT id FROM medicines WHERE name = $1`
        err := p.db.QueryRow(query, name).Scan(&amp;id)
        
        if err == nil </span><span class="cov0" title="0">{
                return id, nil // Medicine exists
        }</span>

        // Medicine doesn't exist, create it
        <span class="cov0" title="0">insertQuery := `
                INSERT INTO medicines (name, mention_count, request_count, offer_count, created_at, updated_at, last_mentioned_at)
                VALUES ($1, 1, 0, 0, NOW(), NOW(), NOW())
                RETURNING id
        `
        
        err = p.db.QueryRow(insertQuery, name).Scan(&amp;id)
        if err != nil </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("failed to create medicine: %w", err)
        }</span>

        <span class="cov0" title="0">return id, nil</span>
}

// insertMessageMedicine inserts a message-medicine relationship
func (p *MessageProcessor) insertMessageMedicine(messageID, medicineID int, medicine Medicine) error <span class="cov0" title="0">{
        query := `
                INSERT INTO message_medicines (
                        message_id, medicine_id, confidence_score, quantity, unit, 
                        price, currency, position_start, position_end, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
                ON CONFLICT (message_id, medicine_id) DO NOTHING
        `

        var positionStart, positionEnd *int
        if medicine.Position.Start &gt; 0 </span><span class="cov0" title="0">{
                positionStart = &amp;medicine.Position.Start
        }</span>
        <span class="cov0" title="0">if medicine.Position.End &gt; 0 </span><span class="cov0" title="0">{
                positionEnd = &amp;medicine.Position.End
        }</span>

        <span class="cov0" title="0">var quantity, unit, price, currency *string
        if medicine.Quantity != "" </span><span class="cov0" title="0">{
                quantity = &amp;medicine.Quantity
        }</span>
        <span class="cov0" title="0">if medicine.Unit != "" </span><span class="cov0" title="0">{
                unit = &amp;medicine.Unit
        }</span>
        <span class="cov0" title="0">if medicine.Price != "" </span><span class="cov0" title="0">{
                price = &amp;medicine.Price
        }</span>
        <span class="cov0" title="0">if medicine.Currency != "" </span><span class="cov0" title="0">{
                currency = &amp;medicine.Currency
        }</span>

        <span class="cov0" title="0">_, err := p.db.Exec(query,
                messageID,
                medicineID,
                medicine.Confidence,
                quantity,
                unit,
                price,
                currency,
                positionStart,
                positionEnd,
        )

        return err</span>
}

// updateMedicineStats updates medicine statistics
func (p *MessageProcessor) updateMedicineStats(medicineID int, isRequest bool) error <span class="cov0" title="0">{
        var query string
        if isRequest </span><span class="cov0" title="0">{
                query = `
                        UPDATE medicines 
                        SET mention_count = mention_count + 1, 
                            request_count = request_count + 1,
                            last_mentioned_at = NOW(),
                            updated_at = NOW()
                        WHERE id = $1
                `
        }</span> else<span class="cov0" title="0"> {
                query = `
                        UPDATE medicines 
                        SET mention_count = mention_count + 1, 
                            offer_count = offer_count + 1,
                            last_mentioned_at = NOW(),
                            updated_at = NOW()
                        WHERE id = $1
                `
        }</span>

        <span class="cov0" title="0">_, err := p.db.Exec(query, medicineID)
        return err</span>
}

// ProcessSingleMessage processes a single message immediately (for manual reprocessing)
func (p *MessageProcessor) ProcessSingleMessage(ctx context.Context, messageID int) error <span class="cov0" title="0">{
        // Get the message
        query := `
                SELECT id, whatsapp_message_id, account_id, group_jid, group_name,
                       sender_jid, sender_name, content, message_type, processing_status,
                       ai_analysis, error_message, message_timestamp, received_at,
                       processed_at, created_at, updated_at
                FROM messages 
                WHERE id = $1
        `

        msg := &amp;database.Message{}
        err := p.db.QueryRow(query, messageID).Scan(
                &amp;msg.ID,
                &amp;msg.WhatsAppMessageID,
                &amp;msg.AccountID,
                &amp;msg.GroupJID,
                &amp;msg.GroupName,
                &amp;msg.SenderJID,
                &amp;msg.SenderName,
                &amp;msg.Content,
                &amp;msg.MessageType,
                &amp;msg.ProcessingStatus,
                &amp;msg.AIAnalysis,
                &amp;msg.ErrorMessage,
                &amp;msg.MessageTimestamp,
                &amp;msg.ReceivedAt,
                &amp;msg.ProcessedAt,
                &amp;msg.CreatedAt,
                &amp;msg.UpdatedAt,
        )

        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get message: %w", err)
        }</span>

        // Reset processing status
        <span class="cov0" title="0">if err := p.db.UpdateMessageProcessing(messageID, "pending", nil, nil); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to reset message status: %w", err)
        }</span>

        // Process the message
        <span class="cov0" title="0">return p.processMessage(ctx, msg)</span>
}

// min returns the minimum of two integers
func min(a, b int) int <span class="cov0" title="0">{
        if a &lt; b </span><span class="cov0" title="0">{
                return a
        }</span>
        <span class="cov0" title="0">return b</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package api

import (
        "fmt"
        "medibridge-whatsapp/internal/ai"
        "medibridge-whatsapp/internal/database"
        "medibridge-whatsapp/internal/matching"
        "medibridge-whatsapp/internal/whatsapp"
        "net/http"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
        "go.uber.org/zap"
)

// Server represents the API server
type Server struct {
        logger          *zap.Logger
        waManager       *whatsapp.Manager
        db              *database.DB
        aiProcessor     *ai.MessageProcessor
        matchingService *matching.Service
}

// NewServer creates a new API server
func NewServer(logger *zap.Logger, waManager *whatsapp.Manager, db *database.DB, aiProcessor *ai.MessageProcessor, matchingService *matching.Service) *Server <span class="cov0" title="0">{
        return &amp;Server{
                logger:          logger,
                waManager:       waManager,
                db:              db,
                aiProcessor:     aiProcessor,
                matchingService: matchingService,
        }
}</span>

// SetupRoutes sets up all API routes
func (s *Server) SetupRoutes(router *gin.Engine) <span class="cov0" title="0">{
        // Health check
        router.GET("/health", s.healthCheck)

        // API v1 routes
        v1 := router.Group("/api/v1")
        </span><span class="cov0" title="0">{
                // Account management
                accounts := v1.Group("/accounts")
                </span><span class="cov0" title="0">{
                        accounts.GET("", s.getAccounts)
                        accounts.POST("", s.createAccount)
                        accounts.GET("/:id", s.getAccount)
                        accounts.PUT("/:id/status", s.updateAccountStatus)
                        accounts.DELETE("/:id", s.deleteAccount)
                }</span>

                // WhatsApp client management
                <span class="cov0" title="0">clients := v1.Group("/clients")
                </span><span class="cov0" title="0">{
                        clients.GET("", s.getClients)
                        clients.POST("/:phone/connect", s.connectClient)
                        clients.POST("/:phone/disconnect", s.disconnectClient)
                        clients.GET("/:phone/qr", s.getQRCode)
                }</span>

                // Message management
                <span class="cov0" title="0">messages := v1.Group("/messages")
                </span><span class="cov0" title="0">{
                        messages.GET("", s.getMessages)
                        messages.GET("/:id", s.getMessage)
                        messages.POST("/:id/reprocess", s.reprocessMessage)
                }</span>

                // Matching
                <span class="cov0" title="0">matches := v1.Group("/matches")
                </span><span class="cov0" title="0">{
                        matches.GET("", s.getMatches)
                        matches.GET("/:id", s.getMatch)
                        matches.PUT("/:id/status", s.updateMatchStatus)
                        matches.POST("/run", s.runMatching)
                        matches.GET("/stats", s.getMatchingStats)
                }</span>

                // Statistics
                <span class="cov0" title="0">stats := v1.Group("/stats")
                </span><span class="cov0" title="0">{
                        stats.GET("/overview", s.getStatsOverview)
                        stats.GET("/messages", s.getMessageStats)
                        stats.GET("/medicines", s.getMedicineStats)
                }</span>
        }
}

// healthCheck returns the health status of the service
func (s *Server) healthCheck(c *gin.Context) <span class="cov0" title="0">{
        c.JSON(http.StatusOK, gin.H{
                "status":    "healthy",
                "timestamp": gin.H{"unix": gin.H{"seconds": **********}},
                "service":   "medibridge-whatsapp",
        })
}</span>

// getAccounts returns all accounts
func (s *Server) getAccounts(c *gin.Context) <span class="cov0" title="0">{
        accounts, err := s.db.GetActiveAccounts()
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to get accounts", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get accounts"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "accounts": accounts,
                "total":    len(accounts),
        })</span>
}

// createAccount creates a new account
func (s *Server) createAccount(c *gin.Context) <span class="cov0" title="0">{
        var req struct {
                PhoneNumber string `json:"phone_number" binding:"required"`
                DisplayName string `json:"display_name" binding:"required"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // Create account in database
        <span class="cov0" title="0">accountID := fmt.Sprintf("acc_%d", time.Now().Unix())

        query := `
                INSERT INTO whatsapp_accounts (account_id, phone_number, status, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5)
        `

        now := time.Now()
        _, err := s.db.Exec(query, accountID, req.PhoneNumber, "disconnected", now, now)
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to create account", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create account"})
                return
        }</span>

        <span class="cov0" title="0">s.logger.Info("Account created successfully",
                zap.String("account_id", accountID),
                zap.String("phone", req.PhoneNumber))

        c.JSON(http.StatusCreated, gin.H{
                "message":    "Account created successfully",
                "account_id": accountID,
                "phone":      req.PhoneNumber,
        })</span>
}

// getAccount returns a specific account
func (s *Server) getAccount(c *gin.Context) <span class="cov0" title="0">{
        idStr := c.Param("id")
        id, err := strconv.Atoi(idStr)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
                return
        }</span>

        <span class="cov0" title="0">query := `
                SELECT account_id, phone_number, status, last_seen, created_at, updated_at
                FROM whatsapp_accounts
                WHERE id = $1
        `

        var account struct {
                AccountID   string     `json:"account_id"`
                PhoneNumber string     `json:"phone_number"`
                Status      string     `json:"status"`
                LastSeen    *time.Time `json:"last_seen"`
                CreatedAt   time.Time  `json:"created_at"`
                UpdatedAt   time.Time  `json:"updated_at"`
        }

        row := s.db.QueryRow(query, id)
        err = row.Scan(&amp;account.AccountID, &amp;account.PhoneNumber, &amp;account.Status,
                &amp;account.LastSeen, &amp;account.CreatedAt, &amp;account.UpdatedAt)

        if err != nil </span><span class="cov0" title="0">{
                if err.Error() == "sql: no rows in result set" </span><span class="cov0" title="0">{
                        c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
                        return
                }</span>
                <span class="cov0" title="0">s.logger.Error("Failed to get account", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get account"})
                return</span>
        }

        <span class="cov0" title="0">c.JSON(http.StatusOK, account)</span>
}

// updateAccountStatus updates an account's status
func (s *Server) updateAccountStatus(c *gin.Context) <span class="cov0" title="0">{
        idStr := c.Param("id")
        id, err := strconv.Atoi(idStr)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
                return
        }</span>

        <span class="cov0" title="0">var req struct {
                Status string `json:"status" binding:"required"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">if err := s.db.UpdateAccountStatus(id, req.Status); err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to update account status", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update account status"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "message": "Account status updated successfully",
        })</span>
}

// deleteAccount deletes an account
func (s *Server) deleteAccount(c *gin.Context) <span class="cov0" title="0">{
        idStr := c.Param("id")
        id, err := strconv.Atoi(idStr)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
                return
        }</span>

        // First check if account exists
        <span class="cov0" title="0">checkQuery := `SELECT account_id FROM whatsapp_accounts WHERE id = $1`
        var accountID string
        err = s.db.QueryRow(checkQuery, id).Scan(&amp;accountID)
        if err != nil </span><span class="cov0" title="0">{
                if err.Error() == "sql: no rows in result set" </span><span class="cov0" title="0">{
                        c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
                        return
                }</span>
                <span class="cov0" title="0">s.logger.Error("Failed to check account", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check account"})
                return</span>
        }

        // Delete account from database
        <span class="cov0" title="0">deleteQuery := `DELETE FROM whatsapp_accounts WHERE id = $1`
        result, err := s.db.Exec(deleteQuery, id)
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to delete account", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete account"})
                return
        }</span>

        <span class="cov0" title="0">rowsAffected, _ := result.RowsAffected()
        if rowsAffected == 0 </span><span class="cov0" title="0">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
                return
        }</span>

        <span class="cov0" title="0">s.logger.Info("Account deleted successfully", zap.String("account_id", accountID))
        c.JSON(http.StatusOK, gin.H{
                "message":    "Account deleted successfully",
                "account_id": accountID,
        })</span>
}

// getClients returns all connected WhatsApp clients
func (s *Server) getClients(c *gin.Context) <span class="cov0" title="0">{
        clients := s.waManager.GetClients()

        c.JSON(http.StatusOK, gin.H{
                "clients": clients,
                "total":   len(clients),
        })
}</span>

// connectClient connects a WhatsApp client
func (s *Server) connectClient(c *gin.Context) <span class="cov0" title="0">{
        phone := c.Param("phone")

        // TODO: Implement client connection
        c.JSON(http.StatusOK, gin.H{
                "message": "Client connection initiated",
                "phone":   phone,
        })
}</span>

// disconnectClient disconnects a WhatsApp client
func (s *Server) disconnectClient(c *gin.Context) <span class="cov0" title="0">{
        phone := c.Param("phone")

        // TODO: Implement client disconnection
        c.JSON(http.StatusOK, gin.H{
                "message": "Client disconnected",
                "phone":   phone,
        })
}</span>

// getQRCode returns QR code for pairing
func (s *Server) getQRCode(c *gin.Context) <span class="cov0" title="0">{
        phone := c.Param("phone")

        // TODO: Implement QR code generation
        c.JSON(http.StatusOK, gin.H{
                "message": "QR code generated",
                "phone":   phone,
                "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
        })
}</span>

// getMessages returns messages with pagination
func (s *Server) getMessages(c *gin.Context) <span class="cov0" title="0">{
        // Get query parameters
        limitStr := c.DefaultQuery("limit", "50")
        offsetStr := c.DefaultQuery("offset", "0")
        status := c.Query("status")

        limit, _ := strconv.Atoi(limitStr)
        offset, _ := strconv.Atoi(offsetStr)

        // Build query with filters
        baseQuery := `
                SELECT id, whatsapp_message_id, account_id, group_jid, group_name,
                           sender_jid, sender_name, content, message_type, processing_status,
                           ai_analysis, error_message, message_timestamp, received_at,
                           processed_at, created_at, updated_at
                FROM messages
        `

        var whereClause string
        var args []interface{}
        argIndex := 1

        if status != "" </span><span class="cov0" title="0">{
                whereClause = " WHERE processing_status = $" + strconv.Itoa(argIndex)
                args = append(args, status)
                argIndex++
        }</span>

        // Get total count
        <span class="cov0" title="0">countQuery := "SELECT COUNT(*) FROM messages" + whereClause
        var total int
        err := s.db.QueryRow(countQuery, args...).Scan(&amp;total)
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to get message count", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get message count"})
                return
        }</span>

        // Get messages with pagination
        <span class="cov0" title="0">query := baseQuery + whereClause +
                " ORDER BY message_timestamp DESC LIMIT $" + strconv.Itoa(argIndex) +
                " OFFSET $" + strconv.Itoa(argIndex+1)
        args = append(args, limit, offset)

        rows, err := s.db.Query(query, args...)
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to get messages", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get messages"})
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var messages []map[string]interface{}
        for rows.Next() </span><span class="cov0" title="0">{
                var msg struct {
                        ID                int        `json:"id"`
                        WhatsAppMessageID string     `json:"whatsapp_message_id"`
                        AccountID         string     `json:"account_id"`
                        GroupJID          string     `json:"group_jid"`
                        GroupName         string     `json:"group_name"`
                        SenderJID         string     `json:"sender_jid"`
                        SenderName        string     `json:"sender_name"`
                        Content           string     `json:"content"`
                        MessageType       *string    `json:"message_type"`
                        ProcessingStatus  string     `json:"processing_status"`
                        AIAnalysis        *string    `json:"ai_analysis"`
                        ErrorMessage      *string    `json:"error_message"`
                        MessageTimestamp  time.Time  `json:"message_timestamp"`
                        ReceivedAt        time.Time  `json:"received_at"`
                        ProcessedAt       *time.Time `json:"processed_at"`
                        CreatedAt         time.Time  `json:"created_at"`
                        UpdatedAt         time.Time  `json:"updated_at"`
                }

                err := rows.Scan(&amp;msg.ID, &amp;msg.WhatsAppMessageID, &amp;msg.AccountID, &amp;msg.GroupJID,
                        &amp;msg.GroupName, &amp;msg.SenderJID, &amp;msg.SenderName, &amp;msg.Content,
                        &amp;msg.MessageType, &amp;msg.ProcessingStatus, &amp;msg.AIAnalysis, &amp;msg.ErrorMessage,
                        &amp;msg.MessageTimestamp, &amp;msg.ReceivedAt, &amp;msg.ProcessedAt,
                        &amp;msg.CreatedAt, &amp;msg.UpdatedAt)
                if err != nil </span><span class="cov0" title="0">{
                        s.logger.Error("Failed to scan message", zap.Error(err))
                        continue</span>
                }

                <span class="cov0" title="0">messages = append(messages, map[string]interface{}{
                        "id":                  msg.ID,
                        "whatsapp_message_id": msg.WhatsAppMessageID,
                        "account_id":          msg.AccountID,
                        "group_jid":           msg.GroupJID,
                        "group_name":          msg.GroupName,
                        "sender_jid":          msg.SenderJID,
                        "sender_name":         msg.SenderName,
                        "content":             msg.Content,
                        "message_type":        msg.MessageType,
                        "processing_status":   msg.ProcessingStatus,
                        "ai_analysis":         msg.AIAnalysis,
                        "error_message":       msg.ErrorMessage,
                        "message_timestamp":   msg.MessageTimestamp,
                        "received_at":         msg.ReceivedAt,
                        "processed_at":        msg.ProcessedAt,
                        "created_at":          msg.CreatedAt,
                        "updated_at":          msg.UpdatedAt,
                })</span>
        }

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "messages": messages,
                "total":    total,
                "limit":    limit,
                "offset":   offset,
                "status":   status,
        })</span>
}

// getMessage returns a specific message
func (s *Server) getMessage(c *gin.Context) <span class="cov0" title="0">{
        idStr := c.Param("id")
        id, err := strconv.Atoi(idStr)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message ID"})
                return
        }</span>

        <span class="cov0" title="0">query := `
                SELECT id, whatsapp_message_id, account_id, group_jid, group_name,
                           sender_jid, sender_name, content, message_type, processing_status,
                           ai_analysis, error_message, message_timestamp, received_at,
                           processed_at, created_at, updated_at
                FROM messages
                WHERE id = $1
        `

        var msg struct {
                ID                int        `json:"id"`
                WhatsAppMessageID string     `json:"whatsapp_message_id"`
                AccountID         string     `json:"account_id"`
                GroupJID          string     `json:"group_jid"`
                GroupName         string     `json:"group_name"`
                SenderJID         string     `json:"sender_jid"`
                SenderName        string     `json:"sender_name"`
                Content           string     `json:"content"`
                MessageType       *string    `json:"message_type"`
                ProcessingStatus  string     `json:"processing_status"`
                AIAnalysis        *string    `json:"ai_analysis"`
                ErrorMessage      *string    `json:"error_message"`
                MessageTimestamp  time.Time  `json:"message_timestamp"`
                ReceivedAt        time.Time  `json:"received_at"`
                ProcessedAt       *time.Time `json:"processed_at"`
                CreatedAt         time.Time  `json:"created_at"`
                UpdatedAt         time.Time  `json:"updated_at"`
        }

        row := s.db.QueryRow(query, id)
        err = row.Scan(&amp;msg.ID, &amp;msg.WhatsAppMessageID, &amp;msg.AccountID, &amp;msg.GroupJID,
                &amp;msg.GroupName, &amp;msg.SenderJID, &amp;msg.SenderName, &amp;msg.Content,
                &amp;msg.MessageType, &amp;msg.ProcessingStatus, &amp;msg.AIAnalysis, &amp;msg.ErrorMessage,
                &amp;msg.MessageTimestamp, &amp;msg.ReceivedAt, &amp;msg.ProcessedAt,
                &amp;msg.CreatedAt, &amp;msg.UpdatedAt)

        if err != nil </span><span class="cov0" title="0">{
                if err.Error() == "sql: no rows in result set" </span><span class="cov0" title="0">{
                        c.JSON(http.StatusNotFound, gin.H{"error": "Message not found"})
                        return
                }</span>
                <span class="cov0" title="0">s.logger.Error("Failed to get message", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get message"})
                return</span>
        }

        // Get associated medicines
        <span class="cov0" title="0">medicinesQuery := `
                SELECT mm.id, mm.medicine_id, m.name, mm.confidence_score,
                           mm.quantity, mm.unit, mm.price, mm.currency
                FROM message_medicines mm
                JOIN medicines m ON mm.medicine_id = m.id
                WHERE mm.message_id = $1
        `

        medicineRows, err := s.db.Query(medicinesQuery, id)
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to get message medicines", zap.Error(err))
        }</span> else<span class="cov0" title="0"> {
                defer medicineRows.Close()

                var medicines []map[string]interface{}
                for medicineRows.Next() </span><span class="cov0" title="0">{
                        var med struct {
                                ID              int     `json:"id"`
                                MedicineID      int     `json:"medicine_id"`
                                Name            string  `json:"name"`
                                ConfidenceScore float64 `json:"confidence_score"`
                                Quantity        *string `json:"quantity"`
                                Unit            *string `json:"unit"`
                                Price           *string `json:"price"`
                                Currency        *string `json:"currency"`
                        }

                        err := medicineRows.Scan(&amp;med.ID, &amp;med.MedicineID, &amp;med.Name,
                                &amp;med.ConfidenceScore, &amp;med.Quantity, &amp;med.Unit, &amp;med.Price, &amp;med.Currency)
                        if err != nil </span><span class="cov0" title="0">{
                                s.logger.Error("Failed to scan medicine", zap.Error(err))
                                continue</span>
                        }

                        <span class="cov0" title="0">medicines = append(medicines, map[string]interface{}{
                                "id":               med.ID,
                                "medicine_id":      med.MedicineID,
                                "name":             med.Name,
                                "confidence_score": med.ConfidenceScore,
                                "quantity":         med.Quantity,
                                "unit":             med.Unit,
                                "price":            med.Price,
                                "currency":         med.Currency,
                        })</span>
                }

                <span class="cov0" title="0">response := map[string]interface{}{
                        "id":                  msg.ID,
                        "whatsapp_message_id": msg.WhatsAppMessageID,
                        "account_id":          msg.AccountID,
                        "group_jid":           msg.GroupJID,
                        "group_name":          msg.GroupName,
                        "sender_jid":          msg.SenderJID,
                        "sender_name":         msg.SenderName,
                        "content":             msg.Content,
                        "message_type":        msg.MessageType,
                        "processing_status":   msg.ProcessingStatus,
                        "ai_analysis":         msg.AIAnalysis,
                        "error_message":       msg.ErrorMessage,
                        "message_timestamp":   msg.MessageTimestamp,
                        "received_at":         msg.ReceivedAt,
                        "processed_at":        msg.ProcessedAt,
                        "created_at":          msg.CreatedAt,
                        "updated_at":          msg.UpdatedAt,
                        "medicines":           medicines,
                }

                c.JSON(http.StatusOK, response)
                return</span>
        }

        // Fallback without medicines
        <span class="cov0" title="0">c.JSON(http.StatusOK, msg)</span>
}

// reprocessMessage reprocesses a message with AI
func (s *Server) reprocessMessage(c *gin.Context) <span class="cov0" title="0">{
        idStr := c.Param("id")
        id, err := strconv.Atoi(idStr)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message ID"})
                return
        }</span>

        // Process the message with AI
        <span class="cov0" title="0">ctx := c.Request.Context()
        if err := s.aiProcessor.ProcessSingleMessage(ctx, id); err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to reprocess message", zap.Int("message_id", id), zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reprocess message"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "message":    "Message reprocessed successfully",
                "message_id": id,
        })</span>
}

// getStatsOverview returns general statistics
func (s *Server) getStatsOverview(c *gin.Context) <span class="cov0" title="0">{
        // Get account statistics
        var totalAccounts, activeAccounts int
        s.db.QueryRow("SELECT COUNT(*) FROM whatsapp_accounts").Scan(&amp;totalAccounts)
        s.db.QueryRow("SELECT COUNT(*) FROM whatsapp_accounts WHERE status = 'connected'").Scan(&amp;activeAccounts)

        // Get message statistics
        var totalMessages, processedMessages, pendingMessages int
        s.db.QueryRow("SELECT COUNT(*) FROM messages").Scan(&amp;totalMessages)
        s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'processed'").Scan(&amp;processedMessages)
        s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'pending'").Scan(&amp;pendingMessages)

        // Get medicine statistics
        var totalMedicines int
        s.db.QueryRow("SELECT COUNT(*) FROM medicines").Scan(&amp;totalMedicines)

        // Get match statistics
        var totalMatches int
        s.db.QueryRow("SELECT COUNT(*) FROM matches").Scan(&amp;totalMatches)

        c.JSON(http.StatusOK, gin.H{
                "total_accounts":     totalAccounts,
                "active_accounts":    activeAccounts,
                "total_messages":     totalMessages,
                "processed_messages": processedMessages,
                "pending_messages":   pendingMessages,
                "total_medicines":    totalMedicines,
                "total_matches":      totalMatches,
        })
}</span>

// getMessageStats returns message statistics
func (s *Server) getMessageStats(c *gin.Context) <span class="cov0" title="0">{
        // Get message type statistics
        var requestCount, offerCount, unknownCount int
        s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE message_type = 'request'").Scan(&amp;requestCount)
        s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE message_type = 'offer'").Scan(&amp;offerCount)
        s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE message_type IS NULL OR message_type = 'unknown'").Scan(&amp;unknownCount)

        // Get processing statistics
        var pendingCount, processedCount, failedCount int
        s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'pending'").Scan(&amp;pendingCount)
        s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'processed'").Scan(&amp;processedCount)
        s.db.QueryRow("SELECT COUNT(*) FROM messages WHERE processing_status = 'failed'").Scan(&amp;failedCount)

        // Get daily message counts for the last 7 days
        dailyQuery := `
                SELECT DATE(message_timestamp) as date, COUNT(*) as count
                FROM messages
                WHERE message_timestamp &gt;= CURRENT_DATE - INTERVAL '7 days'
                GROUP BY DATE(message_timestamp)
                ORDER BY date DESC
        `

        rows, err := s.db.Query(dailyQuery)
        var dailyMessages []map[string]interface{}

        if err == nil </span><span class="cov0" title="0">{
                defer rows.Close()
                for rows.Next() </span><span class="cov0" title="0">{
                        var date string
                        var count int
                        if err := rows.Scan(&amp;date, &amp;count); err == nil </span><span class="cov0" title="0">{
                                dailyMessages = append(dailyMessages, map[string]interface{}{
                                        "date":  date,
                                        "count": count,
                                })
                        }</span>
                }
        }

        <span class="cov0" title="0">if dailyMessages == nil </span><span class="cov0" title="0">{
                dailyMessages = []map[string]interface{}{}
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "daily_messages":   dailyMessages,
                "message_types":    gin.H{"request": requestCount, "offer": offerCount, "unknown": unknownCount},
                "processing_stats": gin.H{"pending": pendingCount, "processed": processedCount, "failed": failedCount},
        })</span>
}

// getMedicineStats returns medicine statistics
func (s *Server) getMedicineStats(c *gin.Context) <span class="cov0" title="0">{
        // Get most requested medicines
        requestedQuery := `
                SELECT m.name, COUNT(*) as count
                FROM message_medicines mm
                JOIN medicines m ON mm.medicine_id = m.id
                JOIN messages msg ON mm.message_id = msg.id
                WHERE msg.message_type = 'request'
                GROUP BY m.name
                ORDER BY count DESC
                LIMIT 10
        `

        requestedRows, err := s.db.Query(requestedQuery)
        var mostRequested []map[string]interface{}

        if err == nil </span><span class="cov0" title="0">{
                defer requestedRows.Close()
                for requestedRows.Next() </span><span class="cov0" title="0">{
                        var name string
                        var count int
                        if err := requestedRows.Scan(&amp;name, &amp;count); err == nil </span><span class="cov0" title="0">{
                                mostRequested = append(mostRequested, map[string]interface{}{
                                        "name":  name,
                                        "count": count,
                                })
                        }</span>
                }
        }

        // Get most offered medicines
        <span class="cov0" title="0">offeredQuery := `
                SELECT m.name, COUNT(*) as count
                FROM message_medicines mm
                JOIN medicines m ON mm.medicine_id = m.id
                JOIN messages msg ON mm.message_id = msg.id
                WHERE msg.message_type = 'offer'
                GROUP BY m.name
                ORDER BY count DESC
                LIMIT 10
        `

        offeredRows, err := s.db.Query(offeredQuery)
        var mostOffered []map[string]interface{}

        if err == nil </span><span class="cov0" title="0">{
                defer offeredRows.Close()
                for offeredRows.Next() </span><span class="cov0" title="0">{
                        var name string
                        var count int
                        if err := offeredRows.Scan(&amp;name, &amp;count); err == nil </span><span class="cov0" title="0">{
                                mostOffered = append(mostOffered, map[string]interface{}{
                                        "name":  name,
                                        "count": count,
                                })
                        }</span>
                }
        }

        // Get medicine categories
        <span class="cov0" title="0">categoryQuery := `
                SELECT category, COUNT(*) as count
                FROM medicines
                WHERE category IS NOT NULL AND category != ''
                GROUP BY category
                ORDER BY count DESC
        `

        categoryRows, err := s.db.Query(categoryQuery)
        categories := make(map[string]int)

        if err == nil </span><span class="cov0" title="0">{
                defer categoryRows.Close()
                for categoryRows.Next() </span><span class="cov0" title="0">{
                        var category string
                        var count int
                        if err := categoryRows.Scan(&amp;category, &amp;count); err == nil </span><span class="cov0" title="0">{
                                categories[category] = count
                        }</span>
                }
        }

        <span class="cov0" title="0">if mostRequested == nil </span><span class="cov0" title="0">{
                mostRequested = []map[string]interface{}{}
        }</span>
        <span class="cov0" title="0">if mostOffered == nil </span><span class="cov0" title="0">{
                mostOffered = []map[string]interface{}{}
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "most_requested": mostRequested,
                "most_offered":   mostOffered,
                "categories":     categories,
        })</span>
}

// getMatches returns matches with pagination and filtering
func (s *Server) getMatches(c *gin.Context) <span class="cov0" title="0">{
        limitStr := c.DefaultQuery("limit", "50")
        offsetStr := c.DefaultQuery("offset", "0")
        status := c.DefaultQuery("status", "")

        limit, _ := strconv.Atoi(limitStr)
        offset, _ := strconv.Atoi(offsetStr)

        matches, total, err := s.matchingService.GetMatches(limit, offset, status)
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to get matches", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get matches"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "matches": matches,
                "total":   total,
                "limit":   limit,
                "offset":  offset,
        })</span>
}

// getMatch returns a specific match by ID
func (s *Server) getMatch(c *gin.Context) <span class="cov0" title="0">{
        idStr := c.Param("id")
        id, err := strconv.Atoi(idStr)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid match ID"})
                return
        }</span>

        <span class="cov0" title="0">match, err := s.matchingService.GetMatchDetails(id)
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to get match details", zap.Int("match_id", id), zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get match details"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{"match": match})</span>
}

// updateMatchStatus updates the status of a match
func (s *Server) updateMatchStatus(c *gin.Context) <span class="cov0" title="0">{
        idStr := c.Param("id")
        id, err := strconv.Atoi(idStr)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid match ID"})
                return
        }</span>

        <span class="cov0" title="0">var req struct {
                Status string  `json:"status" binding:"required"`
                Notes  *string `json:"notes"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">if err := s.matchingService.UpdateMatchStatus(id, req.Status, req.Notes); err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to update match status", zap.Int("match_id", id), zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update match status"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{"message": "Match status updated successfully"})</span>
}

// runMatching triggers manual matching process
func (s *Server) runMatching(c *gin.Context) <span class="cov0" title="0">{
        ctx := c.Request.Context()
        if err := s.matchingService.RunManualMatching(ctx); err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to run manual matching", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to run matching"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{"message": "Matching process started successfully"})</span>
}

// getMatchingStats returns matching statistics
func (s *Server) getMatchingStats(c *gin.Context) <span class="cov0" title="0">{
        stats, err := s.matchingService.GetMatchingStats()
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error("Failed to get matching stats", zap.Error(err))
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get matching stats"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, stats)</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">package config

import (
        "os"
        "strconv"
)

// Config holds all configuration for the WhatsApp service
type Config struct {
        Port        int
        DatabaseURL string
        LogLevel    string
        
        // WhatsApp specific settings
        SessionDir     string
        WebhookURL     string
        MaxRetries     int
        RetryDelay     int // seconds
        
        // AI service settings
        OllamaURL      string
        AIModel        string
        AITimeout      int // seconds
}

// Load loads configuration from environment variables with defaults
func Load() *Config <span class="cov0" title="0">{
        return &amp;Config{
                Port:           getEnvAsInt("PORT", 8080),
                DatabaseURL:    getEnv("DATABASE_URL", "postgres://user:password@localhost/medibridge?sslmode=disable"),
                LogLevel:       getEnv("LOG_LEVEL", "info"),
                
                SessionDir:     getEnv("SESSION_DIR", "./sessions"),
                WebhookURL:     getEnv("WEBHOOK_URL", ""),
                MaxRetries:     getEnvAsInt("MAX_RETRIES", 3),
                RetryDelay:     getEnvAsInt("RETRY_DELAY", 5),
                
                OllamaURL:      getEnv("OLLAMA_URL", "http://localhost:11434"),
                AIModel:        getEnv("AI_MODEL", "llama2"),
                AITimeout:      getEnvAsInt("AI_TIMEOUT", 30),
        }
}</span>

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string <span class="cov0" title="0">{
        if value := os.Getenv(key); value != "" </span><span class="cov0" title="0">{
                return value
        }</span>
        <span class="cov0" title="0">return defaultValue</span>
}

// getEnvAsInt gets an environment variable as integer with a default value
func getEnvAsInt(key string, defaultValue int) int <span class="cov0" title="0">{
        if value := os.Getenv(key); value != "" </span><span class="cov0" title="0">{
                if intValue, err := strconv.Atoi(value); err == nil </span><span class="cov0" title="0">{
                        return intValue
                }</span>
        }
        <span class="cov0" title="0">return defaultValue</span>
}
</pre>
		
		<pre class="file" id="file4" style="display: none">package database

import (
        "database/sql"
        "time"

        _ "github.com/lib/pq"
)

// DB wraps the sql.DB connection
type DB struct {
        *sql.DB
}

// Connect establishes a connection to the database
func Connect(databaseURL string) (*DB, error) <span class="cov0" title="0">{
        db, err := sql.Open("postgres", databaseURL)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // Configure connection pool
        <span class="cov0" title="0">db.SetMaxOpenConns(25)
        db.SetMaxIdleConns(5)
        db.SetConnMaxLifetime(5 * time.Minute)

        // Test the connection
        if err := db.Ping(); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">return &amp;DB{db}, nil</span>
}

// Account represents a WhatsApp account
type Account struct {
        ID              int       `json:"id"`
        PhoneNumber     string    `json:"phone_number"`
        DisplayName     string    `json:"display_name"`
        SessionData     *string   `json:"session_data,omitempty"`
        Status          string    `json:"status"`
        LastConnectedAt *time.Time `json:"last_connected_at,omitempty"`
        CreatedAt       time.Time `json:"created_at"`
        UpdatedAt       time.Time `json:"updated_at"`
}

// Message represents a WhatsApp message
type Message struct {
        ID                 int       `json:"id"`
        WhatsAppMessageID  string    `json:"whatsapp_message_id"`
        AccountID          int       `json:"account_id"`
        GroupJID           string    `json:"group_jid"`
        GroupName          string    `json:"group_name"`
        SenderJID          string    `json:"sender_jid"`
        SenderName         string    `json:"sender_name"`
        Content            string    `json:"content"`
        MessageType        string    `json:"message_type"`
        ProcessingStatus   string    `json:"processing_status"`
        AIAnalysis         *string   `json:"ai_analysis,omitempty"`
        ErrorMessage       *string   `json:"error_message,omitempty"`
        MessageTimestamp   time.Time `json:"message_timestamp"`
        ReceivedAt         time.Time `json:"received_at"`
        ProcessedAt        *time.Time `json:"processed_at,omitempty"`
        CreatedAt          time.Time `json:"created_at"`
        UpdatedAt          time.Time `json:"updated_at"`
}

// GetAccountByPhone retrieves an account by phone number
func (db *DB) GetAccountByPhone(phoneNumber string) (*Account, error) <span class="cov0" title="0">{
        query := `
                SELECT id, phone_number, display_name, session_data, status, 
                       last_connected_at, created_at, updated_at
                FROM accounts 
                WHERE phone_number = $1
        `
        
        account := &amp;Account{}
        err := db.QueryRow(query, phoneNumber).Scan(
                &amp;account.ID,
                &amp;account.PhoneNumber,
                &amp;account.DisplayName,
                &amp;account.SessionData,
                &amp;account.Status,
                &amp;account.LastConnectedAt,
                &amp;account.CreatedAt,
                &amp;account.UpdatedAt,
        )
        
        if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                return nil, nil
        }</span>
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        
        <span class="cov0" title="0">return account, nil</span>
}

// UpdateAccountStatus updates the status of an account
func (db *DB) UpdateAccountStatus(accountID int, status string) error <span class="cov0" title="0">{
        query := `
                UPDATE accounts 
                SET status = $1, updated_at = NOW()
                WHERE id = $2
        `
        
        _, err := db.Exec(query, status, accountID)
        return err
}</span>

// UpdateAccountSession updates the session data of an account
func (db *DB) UpdateAccountSession(accountID int, sessionData string) error <span class="cov0" title="0">{
        query := `
                UPDATE accounts 
                SET session_data = $1, last_connected_at = NOW(), updated_at = NOW()
                WHERE id = $2
        `
        
        _, err := db.Exec(query, sessionData, accountID)
        return err
}</span>

// InsertMessage inserts a new message into the database
func (db *DB) InsertMessage(msg *Message) error <span class="cov0" title="0">{
        query := `
                INSERT INTO messages (
                        whatsapp_message_id, account_id, group_jid, group_name,
                        sender_jid, sender_name, content, message_type,
                        processing_status, message_timestamp, received_at,
                        created_at, updated_at
                ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
                ) RETURNING id
        `
        
        now := time.Now()
        err := db.QueryRow(
                query,
                msg.WhatsAppMessageID,
                msg.AccountID,
                msg.GroupJID,
                msg.GroupName,
                msg.SenderJID,
                msg.SenderName,
                msg.Content,
                msg.MessageType,
                msg.ProcessingStatus,
                msg.MessageTimestamp,
                msg.ReceivedAt,
                now,
                now,
        ).Scan(&amp;msg.ID)
        
        return err
}</span>

// UpdateMessageProcessing updates the processing status and AI analysis of a message
func (db *DB) UpdateMessageProcessing(messageID int, status string, aiAnalysis *string, errorMsg *string) error <span class="cov0" title="0">{
        query := `
                UPDATE messages 
                SET processing_status = $1, ai_analysis = $2, error_message = $3,
                    processed_at = NOW(), updated_at = NOW()
                WHERE id = $4
        `
        
        _, err := db.Exec(query, status, aiAnalysis, errorMsg, messageID)
        return err
}</span>

// GetActiveAccounts retrieves all active accounts
func (db *DB) GetActiveAccounts() ([]*Account, error) <span class="cov0" title="0">{
        query := `
                SELECT id, phone_number, display_name, session_data, status,
                       last_connected_at, created_at, updated_at
                FROM accounts 
                WHERE status = 'active'
                ORDER BY last_connected_at DESC
        `
        
        rows, err := db.Query(query)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()
        
        var accounts []*Account
        for rows.Next() </span><span class="cov0" title="0">{
                account := &amp;Account{}
                err := rows.Scan(
                        &amp;account.ID,
                        &amp;account.PhoneNumber,
                        &amp;account.DisplayName,
                        &amp;account.SessionData,
                        &amp;account.Status,
                        &amp;account.LastConnectedAt,
                        &amp;account.CreatedAt,
                        &amp;account.UpdatedAt,
                )
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>
                <span class="cov0" title="0">accounts = append(accounts, account)</span>
        }
        
        <span class="cov0" title="0">return accounts, nil</span>
}

// GetPendingMessages retrieves messages that need AI processing
func (db *DB) GetPendingMessages(limit int) ([]*Message, error) <span class="cov0" title="0">{
        query := `
                SELECT id, whatsapp_message_id, account_id, group_jid, group_name,
                       sender_jid, sender_name, content, message_type, processing_status,
                       ai_analysis, error_message, message_timestamp, received_at,
                       processed_at, created_at, updated_at
                FROM messages 
                WHERE processing_status = 'pending'
                ORDER BY received_at ASC
                LIMIT $1
        `
        
        rows, err := db.Query(query, limit)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()
        
        var messages []*Message
        for rows.Next() </span><span class="cov0" title="0">{
                msg := &amp;Message{}
                err := rows.Scan(
                        &amp;msg.ID,
                        &amp;msg.WhatsAppMessageID,
                        &amp;msg.AccountID,
                        &amp;msg.GroupJID,
                        &amp;msg.GroupName,
                        &amp;msg.SenderJID,
                        &amp;msg.SenderName,
                        &amp;msg.Content,
                        &amp;msg.MessageType,
                        &amp;msg.ProcessingStatus,
                        &amp;msg.AIAnalysis,
                        &amp;msg.ErrorMessage,
                        &amp;msg.MessageTimestamp,
                        &amp;msg.ReceivedAt,
                        &amp;msg.ProcessedAt,
                        &amp;msg.CreatedAt,
                        &amp;msg.UpdatedAt,
                )
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>
                <span class="cov0" title="0">messages = append(messages, msg)</span>
        }
        
        <span class="cov0" title="0">return messages, nil</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">package matching

import (
        "context"
        "encoding/json"
        "fmt"
        "math"
        "strings"
        "time"

        "go.uber.org/zap"

        "medibridge-whatsapp/internal/database"
)

// MatchingEngine handles the logic for matching medicine requests with offers
type MatchingEngine struct {
        logger *zap.Logger
        db     *database.DB
}

// NewMatchingEngine creates a new matching engine
func NewMatchingEngine(logger *zap.Logger, db *database.DB) *MatchingEngine <span class="cov0" title="0">{
        return &amp;MatchingEngine{
                logger: logger,
                db:     db,
        }
}</span>

// MatchingCriteria represents the criteria used for matching
type MatchingCriteria struct {
        PriceMatch        *bool   `json:"price_match,omitempty"`
        QuantityMatch     *bool   `json:"quantity_match,omitempty"`
        LocationProximity *float64 `json:"location_proximity,omitempty"`
        TimeRelevance     float64 `json:"time_relevance"`
        UserReputation    *float64 `json:"user_reputation,omitempty"`
        NameSimilarity    float64 `json:"name_similarity"`
}

// Match represents a potential match between a request and an offer
type Match struct {
        RequestMessageID  int              `json:"request_message_id"`
        OfferMessageID    int              `json:"offer_message_id"`
        MedicineID        int              `json:"medicine_id"`
        ConfidenceScore   float64          `json:"confidence_score"`
        MatchingCriteria  MatchingCriteria `json:"matching_criteria"`
        RequestMessage    *database.Message `json:"request_message,omitempty"`
        OfferMessage      *database.Message `json:"offer_message,omitempty"`
}

// FindMatches finds potential matches for medicine requests and offers
func (me *MatchingEngine) FindMatches(ctx context.Context) error <span class="cov0" title="0">{
        me.logger.Info("Starting matching process")

        // Get unprocessed requests (messages with type 'request')
        requests, err := me.getUnprocessedRequests()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get requests: %w", err)
        }</span>

        // Get available offers (messages with type 'offer')
        <span class="cov0" title="0">offers, err := me.getAvailableOffers()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get offers: %w", err)
        }</span>

        <span class="cov0" title="0">me.logger.Info("Found messages for matching",
                zap.Int("requests", len(requests)),
                zap.Int("offers", len(offers)))

        // Find matches for each request
        for _, request := range requests </span><span class="cov0" title="0">{
                matches, err := me.findMatchesForRequest(request, offers)
                if err != nil </span><span class="cov0" title="0">{
                        me.logger.Error("Failed to find matches for request",
                                zap.Int("request_id", request.ID),
                                zap.Error(err))
                        continue</span>
                }

                // Store the best matches (top 3)
                <span class="cov0" title="0">for i, match := range matches </span><span class="cov0" title="0">{
                        if i &gt;= 3 </span><span class="cov0" title="0">{ // Limit to top 3 matches
                                break</span>
                        }

                        <span class="cov0" title="0">if err := me.storeMatch(match); err != nil </span><span class="cov0" title="0">{
                                me.logger.Error("Failed to store match",
                                        zap.Int("request_id", match.RequestMessageID),
                                        zap.Int("offer_id", match.OfferMessageID),
                                        zap.Error(err))
                        }</span>
                }
        }

        <span class="cov0" title="0">me.logger.Info("Matching process completed")
        return nil</span>
}

// getUnprocessedRequests gets messages that are requests and haven't been processed for matching
func (me *MatchingEngine) getUnprocessedRequests() ([]*database.Message, error) <span class="cov0" title="0">{
        query := `
                SELECT m.id, m.whatsapp_message_id, m.account_id, m.group_jid, m.group_name,
                       m.sender_jid, m.sender_name, m.content, m.message_type, m.processing_status,
                       m.ai_analysis, m.error_message, m.message_timestamp, m.received_at,
                       m.processed_at, m.created_at, m.updated_at
                FROM messages m
                WHERE m.message_type = 'request' 
                  AND m.processing_status = 'processed'
                  AND m.message_timestamp &gt; NOW() - INTERVAL '7 days'
                  AND NOT EXISTS (
                      SELECT 1 FROM matches ma WHERE ma.request_message_id = m.id
                  )
                ORDER BY m.message_timestamp DESC
                LIMIT 50
        `

        rows, err := me.db.Query(query)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var messages []*database.Message
        for rows.Next() </span><span class="cov0" title="0">{
                msg := &amp;database.Message{}
                err := rows.Scan(
                        &amp;msg.ID, &amp;msg.WhatsAppMessageID, &amp;msg.AccountID, &amp;msg.GroupJID, &amp;msg.GroupName,
                        &amp;msg.SenderJID, &amp;msg.SenderName, &amp;msg.Content, &amp;msg.MessageType, &amp;msg.ProcessingStatus,
                        &amp;msg.AIAnalysis, &amp;msg.ErrorMessage, &amp;msg.MessageTimestamp, &amp;msg.ReceivedAt,
                        &amp;msg.ProcessedAt, &amp;msg.CreatedAt, &amp;msg.UpdatedAt,
                )
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>
                <span class="cov0" title="0">messages = append(messages, msg)</span>
        }

        <span class="cov0" title="0">return messages, nil</span>
}

// getAvailableOffers gets messages that are offers and are still relevant
func (me *MatchingEngine) getAvailableOffers() ([]*database.Message, error) <span class="cov0" title="0">{
        query := `
                SELECT m.id, m.whatsapp_message_id, m.account_id, m.group_jid, m.group_name,
                       m.sender_jid, m.sender_name, m.content, m.message_type, m.processing_status,
                       m.ai_analysis, m.error_message, m.message_timestamp, m.received_at,
                       m.processed_at, m.created_at, m.updated_at
                FROM messages m
                WHERE m.message_type = 'offer' 
                  AND m.processing_status = 'processed'
                  AND m.message_timestamp &gt; NOW() - INTERVAL '14 days'
                ORDER BY m.message_timestamp DESC
                LIMIT 100
        `

        rows, err := me.db.Query(query)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var messages []*database.Message
        for rows.Next() </span><span class="cov0" title="0">{
                msg := &amp;database.Message{}
                err := rows.Scan(
                        &amp;msg.ID, &amp;msg.WhatsAppMessageID, &amp;msg.AccountID, &amp;msg.GroupJID, &amp;msg.GroupName,
                        &amp;msg.SenderJID, &amp;msg.SenderName, &amp;msg.Content, &amp;msg.MessageType, &amp;msg.ProcessingStatus,
                        &amp;msg.AIAnalysis, &amp;msg.ErrorMessage, &amp;msg.MessageTimestamp, &amp;msg.ReceivedAt,
                        &amp;msg.ProcessedAt, &amp;msg.CreatedAt, &amp;msg.UpdatedAt,
                )
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>
                <span class="cov0" title="0">messages = append(messages, msg)</span>
        }

        <span class="cov0" title="0">return messages, nil</span>
}

// findMatchesForRequest finds potential matches for a specific request
func (me *MatchingEngine) findMatchesForRequest(request *database.Message, offers []*database.Message) ([]Match, error) <span class="cov0" title="0">{
        // Get medicines mentioned in the request
        requestMedicines, err := me.getMessageMedicines(request.ID)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">var matches []Match

        // For each medicine in the request, find matching offers
        for _, reqMedicine := range requestMedicines </span><span class="cov0" title="0">{
                for _, offer := range offers </span><span class="cov0" title="0">{
                        // Skip if same sender
                        if request.SenderJID == offer.SenderJID </span><span class="cov0" title="0">{
                                continue</span>
                        }

                        // Get medicines mentioned in the offer
                        <span class="cov0" title="0">offerMedicines, err := me.getMessageMedicines(offer.ID)
                        if err != nil </span><span class="cov0" title="0">{
                                me.logger.Error("Failed to get offer medicines",
                                        zap.Int("offer_id", offer.ID),
                                        zap.Error(err))
                                continue</span>
                        }

                        // Check if any offer medicine matches the request medicine
                        <span class="cov0" title="0">for _, offerMedicine := range offerMedicines </span><span class="cov0" title="0">{
                                if me.medicinesMatch(reqMedicine, offerMedicine) </span><span class="cov0" title="0">{
                                        match := me.calculateMatch(request, offer, reqMedicine, offerMedicine)
                                        if match.ConfidenceScore &gt;= 0.5 </span><span class="cov0" title="0">{ // Minimum confidence threshold
                                                matches = append(matches, match)
                                        }</span>
                                }
                        }
                }
        }

        // Sort matches by confidence score (highest first)
        <span class="cov0" title="0">for i := 0; i &lt; len(matches)-1; i++ </span><span class="cov0" title="0">{
                for j := i + 1; j &lt; len(matches); j++ </span><span class="cov0" title="0">{
                        if matches[i].ConfidenceScore &lt; matches[j].ConfidenceScore </span><span class="cov0" title="0">{
                                matches[i], matches[j] = matches[j], matches[i]
                        }</span>
                }
        }

        <span class="cov0" title="0">return matches, nil</span>
}

// MessageMedicine represents a medicine mentioned in a message
type MessageMedicine struct {
        ID             int     `json:"id"`
        MessageID      int     `json:"message_id"`
        MedicineID     int     `json:"medicine_id"`
        MedicineName   string  `json:"medicine_name"`
        ConfidenceScore float64 `json:"confidence_score"`
        Quantity       *string `json:"quantity,omitempty"`
        Unit           *string `json:"unit,omitempty"`
        Price          *string `json:"price,omitempty"`
        Currency       *string `json:"currency,omitempty"`
}

// getMessageMedicines gets medicines mentioned in a specific message
func (me *MatchingEngine) getMessageMedicines(messageID int) ([]MessageMedicine, error) <span class="cov0" title="0">{
        query := `
                SELECT mm.id, mm.message_id, mm.medicine_id, m.name, mm.confidence_score,
                       mm.quantity, mm.unit, mm.price, mm.currency
                FROM message_medicines mm
                JOIN medicines m ON mm.medicine_id = m.id
                WHERE mm.message_id = $1
                ORDER BY mm.confidence_score DESC
        `

        rows, err := me.db.Query(query, messageID)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var medicines []MessageMedicine
        for rows.Next() </span><span class="cov0" title="0">{
                med := MessageMedicine{}
                err := rows.Scan(
                        &amp;med.ID, &amp;med.MessageID, &amp;med.MedicineID, &amp;med.MedicineName,
                        &amp;med.ConfidenceScore, &amp;med.Quantity, &amp;med.Unit, &amp;med.Price, &amp;med.Currency,
                )
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>
                <span class="cov0" title="0">medicines = append(medicines, med)</span>
        }

        <span class="cov0" title="0">return medicines, nil</span>
}

// medicinesMatch checks if two medicines are the same or similar
func (me *MatchingEngine) medicinesMatch(req, offer MessageMedicine) bool <span class="cov0" title="0">{
        // Exact medicine ID match
        if req.MedicineID == offer.MedicineID </span><span class="cov0" title="0">{
                return true
        }</span>

        // Name similarity check
        <span class="cov0" title="0">similarity := me.calculateNameSimilarity(req.MedicineName, offer.MedicineName)
        return similarity &gt;= 0.8</span> // 80% similarity threshold
}

// calculateNameSimilarity calculates similarity between two medicine names
func (me *MatchingEngine) calculateNameSimilarity(name1, name2 string) float64 <span class="cov0" title="0">{
        // Simple similarity calculation based on common words
        words1 := strings.Fields(strings.ToLower(name1))
        words2 := strings.Fields(strings.ToLower(name2))

        if len(words1) == 0 || len(words2) == 0 </span><span class="cov0" title="0">{
                return 0.0
        }</span>

        <span class="cov0" title="0">commonWords := 0
        for _, word1 := range words1 </span><span class="cov0" title="0">{
                for _, word2 := range words2 </span><span class="cov0" title="0">{
                        if word1 == word2 </span><span class="cov0" title="0">{
                                commonWords++
                                break</span>
                        }
                }
        }

        <span class="cov0" title="0">maxWords := math.Max(float64(len(words1)), float64(len(words2)))
        return float64(commonWords) / maxWords</span>
}

// calculateMatch calculates the match score and criteria
func (me *MatchingEngine) calculateMatch(request, offer *database.Message, reqMed, offerMed MessageMedicine) Match <span class="cov0" title="0">{
        criteria := MatchingCriteria{}

        // Calculate name similarity
        criteria.NameSimilarity = me.calculateNameSimilarity(reqMed.MedicineName, offerMed.MedicineName)

        // Calculate time relevance (newer messages get higher scores)
        timeDiff := offer.MessageTimestamp.Sub(request.MessageTimestamp)
        if timeDiff &lt; 0 </span><span class="cov0" title="0">{
                timeDiff = -timeDiff
        }</span>
        
        // Messages within 24 hours get full score, older messages get reduced score
        <span class="cov0" title="0">hours := timeDiff.Hours()
        if hours &lt;= 24 </span><span class="cov0" title="0">{
                criteria.TimeRelevance = 1.0
        }</span> else<span class="cov0" title="0"> if hours &lt;= 168 </span><span class="cov0" title="0">{ // 7 days
                criteria.TimeRelevance = 1.0 - (hours-24)/(168-24)*0.5
        }</span> else<span class="cov0" title="0"> {
                criteria.TimeRelevance = 0.5
        }</span>

        // Check price compatibility
        <span class="cov0" title="0">if reqMed.Price != nil &amp;&amp; offerMed.Price != nil </span><span class="cov0" title="0">{
                priceMatch := true // Simple check - can be enhanced
                criteria.PriceMatch = &amp;priceMatch
        }</span>

        // Check quantity compatibility
        <span class="cov0" title="0">if reqMed.Quantity != nil &amp;&amp; offerMed.Quantity != nil </span><span class="cov0" title="0">{
                quantityMatch := true // Simple check - can be enhanced
                criteria.QuantityMatch = &amp;quantityMatch
        }</span>

        // Calculate overall confidence score
        <span class="cov0" title="0">confidenceScore := criteria.NameSimilarity * 0.4 + // 40% weight on name similarity
                criteria.TimeRelevance*0.3 + // 30% weight on time relevance
                reqMed.ConfidenceScore*0.15 + // 15% weight on request AI confidence
                offerMed.ConfidenceScore*0.15 // 15% weight on offer AI confidence

        // Bonus for exact medicine match
        if reqMed.MedicineID == offerMed.MedicineID </span><span class="cov0" title="0">{
                confidenceScore += 0.1
        }</span>

        // Bonus for price/quantity compatibility
        <span class="cov0" title="0">if criteria.PriceMatch != nil &amp;&amp; *criteria.PriceMatch </span><span class="cov0" title="0">{
                confidenceScore += 0.05
        }</span>
        <span class="cov0" title="0">if criteria.QuantityMatch != nil &amp;&amp; *criteria.QuantityMatch </span><span class="cov0" title="0">{
                confidenceScore += 0.05
        }</span>

        // Ensure score is between 0 and 1
        <span class="cov0" title="0">if confidenceScore &gt; 1.0 </span><span class="cov0" title="0">{
                confidenceScore = 1.0
        }</span>

        <span class="cov0" title="0">return Match{
                RequestMessageID: request.ID,
                OfferMessageID:   offer.ID,
                MedicineID:       reqMed.MedicineID,
                ConfidenceScore:  confidenceScore,
                MatchingCriteria: criteria,
                RequestMessage:   request,
                OfferMessage:     offer,
        }</span>
}

// storeMatch stores a match in the database
func (me *MatchingEngine) storeMatch(match Match) error <span class="cov0" title="0">{
        // Check if match already exists
        var existingID int
        checkQuery := `
                SELECT id FROM matches 
                WHERE request_message_id = $1 AND offer_message_id = $2 AND medicine_id = $3
        `
        err := me.db.QueryRow(checkQuery, match.RequestMessageID, match.OfferMessageID, match.MedicineID).Scan(&amp;existingID)
        if err == nil </span><span class="cov0" title="0">{
                // Match already exists
                return nil
        }</span>

        // Convert criteria to JSON
        <span class="cov0" title="0">criteriaJSON, err := json.Marshal(match.MatchingCriteria)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to marshal criteria: %w", err)
        }</span>

        // Calculate expiry date (7 days from now)
        <span class="cov0" title="0">expiresAt := time.Now().Add(7 * 24 * time.Hour)

        insertQuery := `
                INSERT INTO matches (
                        request_message_id, offer_message_id, medicine_id, confidence_score,
                        status, matching_criteria, created_at, updated_at, expires_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `

        _, err = me.db.Exec(insertQuery,
                match.RequestMessageID,
                match.OfferMessageID,
                match.MedicineID,
                match.ConfidenceScore,
                "pending",
                string(criteriaJSON),
                time.Now(),
                time.Now(),
                expiresAt,
        )

        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to insert match: %w", err)
        }</span>

        <span class="cov0" title="0">me.logger.Info("Match stored successfully",
                zap.Int("request_id", match.RequestMessageID),
                zap.Int("offer_id", match.OfferMessageID),
                zap.Int("medicine_id", match.MedicineID),
                zap.Float64("confidence", match.ConfidenceScore))

        return nil</span>
}

// CleanupExpiredMatches removes expired matches from the database
func (me *MatchingEngine) CleanupExpiredMatches() error <span class="cov0" title="0">{
        query := `
                UPDATE matches 
                SET status = 'expired', updated_at = NOW()
                WHERE expires_at &lt; NOW() AND status IN ('pending', 'notified')
        `

        result, err := me.db.Exec(query)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">rowsAffected, _ := result.RowsAffected()
        me.logger.Info("Expired matches cleaned up", zap.Int64("count", rowsAffected))

        return nil</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package matching

import (
        "context"
        "time"

        "go.uber.org/zap"

        "medibridge-whatsapp/internal/database"
)

// Service handles periodic matching operations
type Service struct {
        logger  *zap.Logger
        db      *database.DB
        engine  *MatchingEngine
        ticker  *time.Ticker
        done    chan struct{}
        running bool
}

// NewService creates a new matching service
func NewService(logger *zap.Logger, db *database.DB, interval time.Duration) *Service <span class="cov0" title="0">{
        engine := NewMatchingEngine(logger, db)
        
        return &amp;Service{
                logger: logger,
                db:     db,
                engine: engine,
                ticker: time.NewTicker(interval),
                done:   make(chan struct{}),
        }
}</span>

// Start begins the periodic matching process
func (s *Service) Start(ctx context.Context) <span class="cov0" title="0">{
        if s.running </span><span class="cov0" title="0">{
                s.logger.Warn("Matching service is already running")
                return
        }</span>

        <span class="cov0" title="0">s.running = true
        s.logger.Info("Starting matching service")

        // Run initial matching
        go func() </span><span class="cov0" title="0">{
                if err := s.engine.FindMatches(ctx); err != nil </span><span class="cov0" title="0">{
                        s.logger.Error("Initial matching failed", zap.Error(err))
                }</span>
        }()

        // Start periodic matching
        <span class="cov0" title="0">go s.run(ctx)</span>
}

// Stop stops the matching service
func (s *Service) Stop() <span class="cov0" title="0">{
        if !s.running </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">s.logger.Info("Stopping matching service")
        s.running = false
        s.ticker.Stop()
        close(s.done)</span>
}

// run executes the periodic matching loop
func (s *Service) run(ctx context.Context) <span class="cov0" title="0">{
        for </span><span class="cov0" title="0">{
                select </span>{
                case &lt;-ctx.Done():<span class="cov0" title="0">
                        s.logger.Info("Matching service stopped due to context cancellation")
                        return</span>
                case &lt;-s.done:<span class="cov0" title="0">
                        s.logger.Info("Matching service stopped")
                        return</span>
                case &lt;-s.ticker.C:<span class="cov0" title="0">
                        s.logger.Debug("Running periodic matching")
                        
                        // Run matching process
                        if err := s.engine.FindMatches(ctx); err != nil </span><span class="cov0" title="0">{
                                s.logger.Error("Periodic matching failed", zap.Error(err))
                        }</span>
                        
                        // Cleanup expired matches
                        <span class="cov0" title="0">if err := s.engine.CleanupExpiredMatches(); err != nil </span><span class="cov0" title="0">{
                                s.logger.Error("Failed to cleanup expired matches", zap.Error(err))
                        }</span>
                }
        }
}

// RunManualMatching triggers a manual matching process
func (s *Service) RunManualMatching(ctx context.Context) error <span class="cov0" title="0">{
        s.logger.Info("Running manual matching")
        return s.engine.FindMatches(ctx)
}</span>

// GetMatchingStats returns statistics about the matching process
func (s *Service) GetMatchingStats() (*MatchingStats, error) <span class="cov0" title="0">{
        stats := &amp;MatchingStats{}

        // Get total matches
        err := s.db.QueryRow("SELECT COUNT(*) FROM matches").Scan(&amp;stats.TotalMatches)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // Get matches by status
        <span class="cov0" title="0">statusQuery := `
                SELECT status, COUNT(*) 
                FROM matches 
                GROUP BY status
        `
        rows, err := s.db.Query(statusQuery)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        stats.MatchesByStatus = make(map[string]int)
        for rows.Next() </span><span class="cov0" title="0">{
                var status string
                var count int
                if err := rows.Scan(&amp;status, &amp;count); err != nil </span><span class="cov0" title="0">{
                        continue</span>
                }
                <span class="cov0" title="0">stats.MatchesByStatus[status] = count</span>
        }

        // Get recent matches (last 24 hours)
        <span class="cov0" title="0">err = s.db.QueryRow(`
                SELECT COUNT(*) FROM matches 
                WHERE created_at &gt; NOW() - INTERVAL '24 hours'
        `).Scan(&amp;stats.RecentMatches)
        if err != nil </span><span class="cov0" title="0">{
                stats.RecentMatches = 0
        }</span>

        // Get average confidence score
        <span class="cov0" title="0">err = s.db.QueryRow(`
                SELECT AVG(confidence_score) FROM matches 
                WHERE status != 'expired'
        `).Scan(&amp;stats.AverageConfidence)
        if err != nil </span><span class="cov0" title="0">{
                stats.AverageConfidence = 0.0
        }</span>

        // Get success rate (completed matches / total matches)
        <span class="cov0" title="0">var completedMatches int
        err = s.db.QueryRow(`
                SELECT COUNT(*) FROM matches WHERE status = 'completed'
        `).Scan(&amp;completedMatches)
        if err != nil </span><span class="cov0" title="0">{
                completedMatches = 0
        }</span>

        <span class="cov0" title="0">if stats.TotalMatches &gt; 0 </span><span class="cov0" title="0">{
                stats.SuccessRate = float64(completedMatches) / float64(stats.TotalMatches)
        }</span>

        <span class="cov0" title="0">return stats, nil</span>
}

// MatchingStats represents statistics about the matching process
type MatchingStats struct {
        TotalMatches       int             `json:"total_matches"`
        RecentMatches      int             `json:"recent_matches"`
        MatchesByStatus    map[string]int  `json:"matches_by_status"`
        AverageConfidence  float64         `json:"average_confidence"`
        SuccessRate        float64         `json:"success_rate"`
}

// GetMatchDetails returns detailed information about a specific match
func (s *Service) GetMatchDetails(matchID int) (*MatchDetails, error) <span class="cov0" title="0">{
        query := `
                SELECT 
                        m.id, m.request_message_id, m.offer_message_id, m.medicine_id,
                        m.confidence_score, m.status, m.matching_criteria, m.notes,
                        m.created_at, m.updated_at, m.notified_at, m.completed_at, m.expires_at,
                        med.name as medicine_name,
                        req.sender_name as requester_name, req.sender_jid as requester_jid,
                        req.content as request_content, req.message_timestamp as request_timestamp,
                        off.sender_name as offerer_name, off.sender_jid as offerer_jid,
                        off.content as offer_content, off.message_timestamp as offer_timestamp
                FROM matches m
                JOIN medicines med ON m.medicine_id = med.id
                JOIN messages req ON m.request_message_id = req.id
                JOIN messages off ON m.offer_message_id = off.id
                WHERE m.id = $1
        `

        details := &amp;MatchDetails{}
        err := s.db.QueryRow(query, matchID).Scan(
                &amp;details.ID, &amp;details.RequestMessageID, &amp;details.OfferMessageID, &amp;details.MedicineID,
                &amp;details.ConfidenceScore, &amp;details.Status, &amp;details.MatchingCriteria, &amp;details.Notes,
                &amp;details.CreatedAt, &amp;details.UpdatedAt, &amp;details.NotifiedAt, &amp;details.CompletedAt, &amp;details.ExpiresAt,
                &amp;details.MedicineName,
                &amp;details.RequesterName, &amp;details.RequesterJID,
                &amp;details.RequestContent, &amp;details.RequestTimestamp,
                &amp;details.OffererName, &amp;details.OffererJID,
                &amp;details.OfferContent, &amp;details.OfferTimestamp,
        )

        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">return details, nil</span>
}

// MatchDetails represents detailed information about a match
type MatchDetails struct {
        ID                int        `json:"id"`
        RequestMessageID  int        `json:"request_message_id"`
        OfferMessageID    int        `json:"offer_message_id"`
        MedicineID        int        `json:"medicine_id"`
        ConfidenceScore   float64    `json:"confidence_score"`
        Status            string     `json:"status"`
        MatchingCriteria  *string    `json:"matching_criteria"`
        Notes             *string    `json:"notes"`
        CreatedAt         time.Time  `json:"created_at"`
        UpdatedAt         time.Time  `json:"updated_at"`
        NotifiedAt        *time.Time `json:"notified_at"`
        CompletedAt       *time.Time `json:"completed_at"`
        ExpiresAt         *time.Time `json:"expires_at"`
        MedicineName      string     `json:"medicine_name"`
        RequesterName     string     `json:"requester_name"`
        RequesterJID      string     `json:"requester_jid"`
        RequestContent    string     `json:"request_content"`
        RequestTimestamp  time.Time  `json:"request_timestamp"`
        OffererName       string     `json:"offerer_name"`
        OffererJID        string     `json:"offerer_jid"`
        OfferContent      string     `json:"offer_content"`
        OfferTimestamp    time.Time  `json:"offer_timestamp"`
}

// UpdateMatchStatus updates the status of a match
func (s *Service) UpdateMatchStatus(matchID int, status string, notes *string) error <span class="cov0" title="0">{
        var query string
        var args []interface{}

        switch status </span>{
        case "notified":<span class="cov0" title="0">
                query = `
                        UPDATE matches 
                        SET status = $1, notified_at = NOW(), updated_at = NOW()
                        WHERE id = $2
                `
                args = []interface{}{status, matchID}</span>
        case "completed":<span class="cov0" title="0">
                query = `
                        UPDATE matches 
                        SET status = $1, completed_at = NOW(), updated_at = NOW()
                        WHERE id = $2
                `
                args = []interface{}{status, matchID}</span>
        default:<span class="cov0" title="0">
                query = `
                        UPDATE matches 
                        SET status = $1, updated_at = NOW()
                        WHERE id = $2
                `
                args = []interface{}{status, matchID}</span>
        }

        <span class="cov0" title="0">if notes != nil </span><span class="cov0" title="0">{
                query = `
                        UPDATE matches 
                        SET status = $1, notes = $2, updated_at = NOW()
                        WHERE id = $3
                `
                args = []interface{}{status, *notes, matchID}
        }</span>

        <span class="cov0" title="0">_, err := s.db.Exec(query, args...)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">s.logger.Info("Match status updated",
                zap.Int("match_id", matchID),
                zap.String("status", status))

        return nil</span>
}

// GetMatches returns a list of matches with pagination and filtering
func (s *Service) GetMatches(limit, offset int, status string) ([]MatchDetails, int, error) <span class="cov0" title="0">{
        var whereClause string
        var args []interface{}
        argIndex := 1

        if status != "" &amp;&amp; status != "all" </span><span class="cov0" title="0">{
                whereClause = "WHERE m.status = $" + string(rune('0'+argIndex))
                args = append(args, status)
                argIndex++
        }</span>

        // Get total count
        <span class="cov0" title="0">countQuery := `
                SELECT COUNT(*) FROM matches m
                JOIN medicines med ON m.medicine_id = med.id
                JOIN messages req ON m.request_message_id = req.id
                JOIN messages off ON m.offer_message_id = off.id
                ` + whereClause

        var total int
        err := s.db.QueryRow(countQuery, args...).Scan(&amp;total)
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // Get matches
        <span class="cov0" title="0">query := `
                SELECT 
                        m.id, m.request_message_id, m.offer_message_id, m.medicine_id,
                        m.confidence_score, m.status, m.matching_criteria, m.notes,
                        m.created_at, m.updated_at, m.notified_at, m.completed_at, m.expires_at,
                        med.name as medicine_name,
                        req.sender_name as requester_name, req.sender_jid as requester_jid,
                        req.content as request_content, req.message_timestamp as request_timestamp,
                        off.sender_name as offerer_name, off.sender_jid as offerer_jid,
                        off.content as offer_content, off.message_timestamp as offer_timestamp
                FROM matches m
                JOIN medicines med ON m.medicine_id = med.id
                JOIN messages req ON m.request_message_id = req.id
                JOIN messages off ON m.offer_message_id = off.id
                ` + whereClause + `
                ORDER BY m.created_at DESC
                LIMIT $` + string(rune('0'+argIndex)) + ` OFFSET $` + string(rune('0'+argIndex+1))

        args = append(args, limit, offset)

        rows, err := s.db.Query(query, args...)
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var matches []MatchDetails
        for rows.Next() </span><span class="cov0" title="0">{
                details := MatchDetails{}
                err := rows.Scan(
                        &amp;details.ID, &amp;details.RequestMessageID, &amp;details.OfferMessageID, &amp;details.MedicineID,
                        &amp;details.ConfidenceScore, &amp;details.Status, &amp;details.MatchingCriteria, &amp;details.Notes,
                        &amp;details.CreatedAt, &amp;details.UpdatedAt, &amp;details.NotifiedAt, &amp;details.CompletedAt, &amp;details.ExpiresAt,
                        &amp;details.MedicineName,
                        &amp;details.RequesterName, &amp;details.RequesterJID,
                        &amp;details.RequestContent, &amp;details.RequestTimestamp,
                        &amp;details.OffererName, &amp;details.OffererJID,
                        &amp;details.OfferContent, &amp;details.OfferTimestamp,
                )
                if err != nil </span><span class="cov0" title="0">{
                        continue</span>
                }
                <span class="cov0" title="0">matches = append(matches, details)</span>
        }

        <span class="cov0" title="0">return matches, total, nil</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">package performance

import (
        "context"
        "runtime"
        "sync"
        "time"

        "go.uber.org/zap"
)

// Optimizer handles performance optimization for the application
type Optimizer struct {
        logger           *zap.Logger
        metrics          *Metrics
        connectionPool   *ConnectionPool
        cacheManager     *CacheManager
        rateLimiter      *RateLimiter
        memoryThreshold  float64
        cpuThreshold     float64
        cleanupInterval  time.Duration
        mu               sync.RWMutex
}

// Metrics holds performance metrics
type Metrics struct {
        RequestCount       int64     `json:"request_count"`
        AverageResponseTime time.Duration `json:"average_response_time"`
        ErrorRate          float64   `json:"error_rate"`
        MemoryUsage        float64   `json:"memory_usage_mb"`
        CPUUsage           float64   `json:"cpu_usage_percent"`
        ActiveConnections  int       `json:"active_connections"`
        CacheHitRate       float64   `json:"cache_hit_rate"`
        LastUpdated        time.Time `json:"last_updated"`
}

// ConnectionPool manages database connections efficiently
type ConnectionPool struct {
        maxConnections int
        activeConns    int
        idleConns      int
        mu             sync.RWMutex
}

// CacheManager handles in-memory caching for frequently accessed data
type CacheManager struct {
        cache     map[string]CacheItem
        maxSize   int
        hits      int64
        misses    int64
        mu        sync.RWMutex
}

// CacheItem represents a cached item
type CacheItem struct {
        Value     interface{}
        ExpiresAt time.Time
}

// RateLimiter controls request rates to prevent overload
type RateLimiter struct {
        requests map[string][]time.Time
        limit    int
        window   time.Duration
        mu       sync.RWMutex
}

// NewOptimizer creates a new performance optimizer
func NewOptimizer(logger *zap.Logger) *Optimizer <span class="cov8" title="1">{
        return &amp;Optimizer{
                logger:          logger,
                metrics:         &amp;Metrics{},
                connectionPool:  NewConnectionPool(50), // max 50 connections
                cacheManager:    NewCacheManager(1000), // max 1000 items
                rateLimiter:     NewRateLimiter(100, time.Minute), // 100 requests per minute
                memoryThreshold: 80.0, // 80% memory usage threshold
                cpuThreshold:    70.0, // 70% CPU usage threshold
                cleanupInterval: 5 * time.Minute,
        }
}</span>

// NewConnectionPool creates a new connection pool
func NewConnectionPool(maxConnections int) *ConnectionPool <span class="cov8" title="1">{
        return &amp;ConnectionPool{
                maxConnections: maxConnections,
        }
}</span>

// NewCacheManager creates a new cache manager
func NewCacheManager(maxSize int) *CacheManager <span class="cov8" title="1">{
        return &amp;CacheManager{
                cache:   make(map[string]CacheItem),
                maxSize: maxSize,
        }
}</span>

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(limit int, window time.Duration) *RateLimiter <span class="cov8" title="1">{
        return &amp;RateLimiter{
                requests: make(map[string][]time.Time),
                limit:    limit,
                window:   window,
        }
}</span>

// Start begins the performance optimization process
func (o *Optimizer) Start(ctx context.Context) <span class="cov8" title="1">{
        o.logger.Info("Starting performance optimizer")

        // Start metrics collection
        go o.collectMetrics(ctx)

        // Start cleanup routines
        go o.runCleanup(ctx)

        // Start memory monitoring
        go o.monitorResources(ctx)
}</span>

// collectMetrics collects and updates performance metrics
func (o *Optimizer) collectMetrics(ctx context.Context) <span class="cov8" title="1">{
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()

        for </span><span class="cov8" title="1">{
                select </span>{
                case &lt;-ctx.Done():<span class="cov8" title="1">
                        return</span>
                case &lt;-ticker.C:<span class="cov0" title="0">
                        o.updateMetrics()</span>
                }
        }
}

// updateMetrics updates the current metrics
func (o *Optimizer) updateMetrics() <span class="cov0" title="0">{
        o.mu.Lock()
        defer o.mu.Unlock()

        var m runtime.MemStats
        runtime.ReadMemStats(&amp;m)

        o.metrics.MemoryUsage = float64(m.Alloc) / 1024 / 1024 // Convert to MB
        o.metrics.ActiveConnections = o.connectionPool.GetActiveConnections()
        o.metrics.CacheHitRate = o.cacheManager.GetHitRate()
        o.metrics.LastUpdated = time.Now()

        // Log metrics if thresholds are exceeded
        if o.metrics.MemoryUsage &gt; o.memoryThreshold </span><span class="cov0" title="0">{
                o.logger.Warn("High memory usage detected",
                        zap.Float64("usage_mb", o.metrics.MemoryUsage),
                        zap.Float64("threshold_mb", o.memoryThreshold))
        }</span>
}

// runCleanup performs periodic cleanup operations
func (o *Optimizer) runCleanup(ctx context.Context) <span class="cov8" title="1">{
        ticker := time.NewTicker(o.cleanupInterval)
        defer ticker.Stop()

        for </span><span class="cov8" title="1">{
                select </span>{
                case &lt;-ctx.Done():<span class="cov8" title="1">
                        return</span>
                case &lt;-ticker.C:<span class="cov0" title="0">
                        o.performCleanup()</span>
                }
        }
}

// performCleanup performs various cleanup operations
func (o *Optimizer) performCleanup() <span class="cov8" title="1">{
        o.logger.Debug("Performing cleanup operations")

        // Clean expired cache items
        o.cacheManager.CleanExpired()

        // Clean old rate limiter entries
        o.rateLimiter.CleanOld()

        // Force garbage collection if memory usage is high
        if o.metrics.MemoryUsage &gt; o.memoryThreshold </span><span class="cov0" title="0">{
                runtime.GC()
                o.logger.Info("Forced garbage collection due to high memory usage")
        }</span>
}

// monitorResources monitors system resources and takes action if needed
func (o *Optimizer) monitorResources(ctx context.Context) <span class="cov8" title="1">{
        ticker := time.NewTicker(1 * time.Minute)
        defer ticker.Stop()

        for </span><span class="cov8" title="1">{
                select </span>{
                case &lt;-ctx.Done():<span class="cov8" title="1">
                        return</span>
                case &lt;-ticker.C:<span class="cov0" title="0">
                        o.checkResourceUsage()</span>
                }
        }
}

// checkResourceUsage checks current resource usage and optimizes if needed
func (o *Optimizer) checkResourceUsage() <span class="cov8" title="1">{
        var m runtime.MemStats
        runtime.ReadMemStats(&amp;m)

        memoryUsageMB := float64(m.Alloc) / 1024 / 1024

        if memoryUsageMB &gt; o.memoryThreshold </span><span class="cov8" title="1">{
                o.logger.Warn("High memory usage, performing optimization",
                        zap.Float64("usage_mb", memoryUsageMB))
                
                // Reduce cache size
                o.cacheManager.ReduceSize(0.5) // Reduce by 50%
                
                // Force garbage collection
                runtime.GC()
        }</span>
}

// GetMetrics returns current performance metrics
func (o *Optimizer) GetMetrics() *Metrics <span class="cov8" title="1">{
        o.mu.RLock()
        defer o.mu.RUnlock()

        // Create a copy to avoid race conditions
        metricsCopy := *o.metrics
        return &amp;metricsCopy
}</span>

// Connection Pool methods

// GetActiveConnections returns the number of active connections
func (cp *ConnectionPool) GetActiveConnections() int <span class="cov8" title="1">{
        cp.mu.RLock()
        defer cp.mu.RUnlock()
        return cp.activeConns
}</span>

// AcquireConnection acquires a connection from the pool
func (cp *ConnectionPool) AcquireConnection() bool <span class="cov8" title="1">{
        cp.mu.Lock()
        defer cp.mu.Unlock()

        if cp.activeConns &gt;= cp.maxConnections </span><span class="cov8" title="1">{
                return false
        }</span>

        <span class="cov8" title="1">cp.activeConns++
        return true</span>
}

// ReleaseConnection releases a connection back to the pool
func (cp *ConnectionPool) ReleaseConnection() <span class="cov8" title="1">{
        cp.mu.Lock()
        defer cp.mu.Unlock()

        if cp.activeConns &gt; 0 </span><span class="cov8" title="1">{
                cp.activeConns--
        }</span>
}

// Cache Manager methods

// Get retrieves an item from the cache
func (cm *CacheManager) Get(key string) (interface{}, bool) <span class="cov8" title="1">{
        cm.mu.RLock()
        defer cm.mu.RUnlock()

        item, exists := cm.cache[key]
        if !exists </span><span class="cov8" title="1">{
                cm.misses++
                return nil, false
        }</span>

        <span class="cov8" title="1">if time.Now().After(item.ExpiresAt) </span><span class="cov8" title="1">{
                cm.misses++
                delete(cm.cache, key)
                return nil, false
        }</span>

        <span class="cov8" title="1">cm.hits++
        return item.Value, true</span>
}

// Set stores an item in the cache
func (cm *CacheManager) Set(key string, value interface{}, ttl time.Duration) <span class="cov8" title="1">{
        cm.mu.Lock()
        defer cm.mu.Unlock()

        // Remove oldest items if cache is full
        if len(cm.cache) &gt;= cm.maxSize </span><span class="cov8" title="1">{
                cm.evictOldest()
        }</span>

        <span class="cov8" title="1">cm.cache[key] = CacheItem{
                Value:     value,
                ExpiresAt: time.Now().Add(ttl),
        }</span>
}

// GetHitRate returns the cache hit rate
func (cm *CacheManager) GetHitRate() float64 <span class="cov8" title="1">{
        cm.mu.RLock()
        defer cm.mu.RUnlock()

        total := cm.hits + cm.misses
        if total == 0 </span><span class="cov0" title="0">{
                return 0
        }</span>

        <span class="cov8" title="1">return float64(cm.hits) / float64(total)</span>
}

// CleanExpired removes expired items from the cache
func (cm *CacheManager) CleanExpired() <span class="cov8" title="1">{
        cm.mu.Lock()
        defer cm.mu.Unlock()

        now := time.Now()
        for key, item := range cm.cache </span><span class="cov8" title="1">{
                if now.After(item.ExpiresAt) </span><span class="cov8" title="1">{
                        delete(cm.cache, key)
                }</span>
        }
}

// ReduceSize reduces the cache size by the given factor
func (cm *CacheManager) ReduceSize(factor float64) <span class="cov8" title="1">{
        cm.mu.Lock()
        defer cm.mu.Unlock()

        targetSize := int(float64(len(cm.cache)) * (1 - factor))
        for len(cm.cache) &gt; targetSize </span><span class="cov8" title="1">{
                cm.evictOldest()
        }</span>
}

// evictOldest removes the oldest item from the cache
func (cm *CacheManager) evictOldest() <span class="cov8" title="1">{
        var oldestKey string
        var oldestTime time.Time

        for key, item := range cm.cache </span><span class="cov8" title="1">{
                if oldestKey == "" || item.ExpiresAt.Before(oldestTime) </span><span class="cov8" title="1">{
                        oldestKey = key
                        oldestTime = item.ExpiresAt
                }</span>
        }

        <span class="cov8" title="1">if oldestKey != "" </span><span class="cov8" title="1">{
                delete(cm.cache, oldestKey)
        }</span>
}

// Rate Limiter methods

// Allow checks if a request is allowed for the given identifier
func (rl *RateLimiter) Allow(identifier string) bool <span class="cov8" title="1">{
        rl.mu.Lock()
        defer rl.mu.Unlock()

        now := time.Now()
        windowStart := now.Add(-rl.window)

        // Get existing requests for this identifier
        requests := rl.requests[identifier]

        // Remove old requests outside the window
        var validRequests []time.Time
        for _, reqTime := range requests </span><span class="cov8" title="1">{
                if reqTime.After(windowStart) </span><span class="cov8" title="1">{
                        validRequests = append(validRequests, reqTime)
                }</span>
        }

        // Check if limit is exceeded
        <span class="cov8" title="1">if len(validRequests) &gt;= rl.limit </span><span class="cov8" title="1">{
                rl.requests[identifier] = validRequests
                return false
        }</span>

        // Add current request
        <span class="cov8" title="1">validRequests = append(validRequests, now)
        rl.requests[identifier] = validRequests

        return true</span>
}

// CleanOld removes old entries from the rate limiter
func (rl *RateLimiter) CleanOld() <span class="cov8" title="1">{
        rl.mu.Lock()
        defer rl.mu.Unlock()

        now := time.Now()
        windowStart := now.Add(-rl.window)

        for identifier, requests := range rl.requests </span><span class="cov8" title="1">{
                var validRequests []time.Time
                for _, reqTime := range requests </span><span class="cov8" title="1">{
                        if reqTime.After(windowStart) </span><span class="cov8" title="1">{
                                validRequests = append(validRequests, reqTime)
                        }</span>
                }

                <span class="cov8" title="1">if len(validRequests) == 0 </span><span class="cov8" title="1">{
                        delete(rl.requests, identifier)
                }</span> else<span class="cov8" title="1"> {
                        rl.requests[identifier] = validRequests
                }</span>
        }
}
</pre>
		
		<pre class="file" id="file8" style="display: none">package whatsapp

import (
        "context"
        "fmt"
        "sync"
        "time"

        "go.mau.fi/whatsmeow"
        "go.mau.fi/whatsmeow/store/sqlstore"
        "go.mau.fi/whatsmeow/types/events"
        "go.uber.org/zap"

        "medibridge-whatsapp/internal/database"
)

// Manager manages multiple WhatsApp client connections
type Manager struct {
        logger   *zap.Logger
        db       *database.DB
        clients  map[string]*Client
        store    *sqlstore.Container
        mutex    sync.RWMutex
        shutdown chan struct{}
}

// Client represents a single WhatsApp client connection
type Client struct {
        ID          int
        PhoneNumber string
        DisplayName string
        Client      *whatsmeow.Client
        Connected   bool
        LastSeen    time.Time
}

// NewManager creates a new WhatsApp manager
func NewManager(logger *zap.Logger, db *database.DB) *Manager <span class="cov0" title="0">{
        // Initialize WhatsApp store
        store := sqlstore.NewWithDB(db.DB, "postgres", nil)

        return &amp;Manager{
                logger:   logger,
                db:       db,
                clients:  make(map[string]*Client),
                store:    store,
                shutdown: make(chan struct{}),
        }
}</span>

// Start initializes and starts all active WhatsApp connections
func (m *Manager) Start() error <span class="cov0" title="0">{
        m.logger.Info("Starting WhatsApp manager")

        // Get all active accounts from database
        accounts, err := m.db.GetActiveAccounts()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get active accounts: %w", err)
        }</span>

        // Start each account
        <span class="cov0" title="0">for _, account := range accounts </span><span class="cov0" title="0">{
                if err := m.startClient(account); err != nil </span><span class="cov0" title="0">{
                        m.logger.Error("Failed to start client",
                                zap.String("phone", account.PhoneNumber),
                                zap.Error(err))
                }</span>
        }

        // Start message processing goroutine
        <span class="cov0" title="0">go m.processMessages()

        return nil</span>
}

// startClient starts a WhatsApp client for the given account
func (m *Manager) startClient(account *database.Account) error <span class="cov0" title="0">{
        m.mutex.Lock()
        defer m.mutex.Unlock()

        // Check if client already exists
        if _, exists := m.clients[account.PhoneNumber]; exists </span><span class="cov0" title="0">{
                return fmt.Errorf("client already exists for %s", account.PhoneNumber)
        }</span>

        // Get device store
        <span class="cov0" title="0">ctx := context.Background()
        deviceStore, err := m.store.GetFirstDevice(ctx)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get device store: %w", err)
        }</span>

        // Create WhatsApp client
        <span class="cov0" title="0">client := whatsmeow.NewClient(deviceStore, nil)

        // Set up event handler
        client.AddEventHandler(m.eventHandler(account.ID))

        // Create our client wrapper
        waClient := &amp;Client{
                ID:          account.ID,
                PhoneNumber: account.PhoneNumber,
                DisplayName: account.DisplayName,
                Client:      client,
                Connected:   false,
                LastSeen:    time.Now(),
        }

        // Store client
        m.clients[account.PhoneNumber] = waClient

        // Connect client
        if client.Store.ID == nil </span><span class="cov0" title="0">{
                // Need to pair/login
                m.logger.Info("Client needs pairing", zap.String("phone", account.PhoneNumber))
                // For now, we'll mark as inactive and require manual pairing
                m.db.UpdateAccountStatus(account.ID, "inactive")
                return fmt.Errorf("client needs pairing: %s", account.PhoneNumber)
        }</span> else<span class="cov0" title="0"> {
                // Connect existing session
                err := client.Connect()
                if err != nil </span><span class="cov0" title="0">{
                        m.logger.Error("Failed to connect client",
                                zap.String("phone", account.PhoneNumber),
                                zap.Error(err))
                        m.db.UpdateAccountStatus(account.ID, "error")
                        return err
                }</span>

                <span class="cov0" title="0">waClient.Connected = true
                m.logger.Info("Client connected successfully", zap.String("phone", account.PhoneNumber))</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// eventHandler creates an event handler for a specific account
func (m *Manager) eventHandler(accountID int) func(interface{}) <span class="cov0" title="0">{
        return func(evt interface{}) </span><span class="cov0" title="0">{
                switch v := evt.(type) </span>{
                case *events.Message:<span class="cov0" title="0">
                        m.handleMessage(accountID, v)</span>
                case *events.Connected:<span class="cov0" title="0">
                        m.handleConnected(accountID)</span>
                case *events.Disconnected:<span class="cov0" title="0">
                        m.handleDisconnected(accountID)</span>
                case *events.LoggedOut:<span class="cov0" title="0">
                        m.handleLoggedOut(accountID)</span>
                }
        }
}

// handleMessage processes incoming WhatsApp messages
func (m *Manager) handleMessage(accountID int, evt *events.Message) <span class="cov0" title="0">{
        // Skip if message is from us
        if evt.Info.IsFromMe </span><span class="cov0" title="0">{
                return
        }</span>

        // Only process group messages for now
        <span class="cov0" title="0">if !evt.Info.IsGroup </span><span class="cov0" title="0">{
                return
        }</span>

        // Extract message content
        <span class="cov0" title="0">var content string
        if evt.Message.GetConversation() != "" </span><span class="cov0" title="0">{
                content = evt.Message.GetConversation()
        }</span> else<span class="cov0" title="0"> if evt.Message.GetExtendedTextMessage() != nil </span><span class="cov0" title="0">{
                content = evt.Message.GetExtendedTextMessage().GetText()
        }</span> else<span class="cov0" title="0"> {
                // Skip non-text messages for now
                return
        }</span>

        // Create message record
        <span class="cov0" title="0">msg := &amp;database.Message{
                WhatsAppMessageID: evt.Info.ID,
                AccountID:         accountID,
                GroupJID:          evt.Info.Chat.String(),
                GroupName:         "", // We'll need to get this separately
                SenderJID:         evt.Info.Sender.String(),
                SenderName:        evt.Info.PushName,
                Content:           content,
                MessageType:       "unknown",
                ProcessingStatus:  "pending",
                MessageTimestamp:  evt.Info.Timestamp,
                ReceivedAt:        time.Now(),
        }

        // Insert message into database
        if err := m.db.InsertMessage(msg); err != nil </span><span class="cov0" title="0">{
                m.logger.Error("Failed to insert message",
                        zap.Error(err),
                        zap.String("message_id", evt.Info.ID))
        }</span> else<span class="cov0" title="0"> {
                m.logger.Info("Message received and stored",
                        zap.String("message_id", evt.Info.ID),
                        zap.String("sender", evt.Info.PushName),
                        zap.String("content", content[:min(50, len(content))]))
        }</span>
}

// handleConnected handles client connection events
func (m *Manager) handleConnected(accountID int) <span class="cov0" title="0">{
        m.logger.Info("Client connected", zap.Int("account_id", accountID))
        m.db.UpdateAccountStatus(accountID, "active")

        // Update client status
        m.mutex.Lock()
        for _, client := range m.clients </span><span class="cov0" title="0">{
                if client.ID == accountID </span><span class="cov0" title="0">{
                        client.Connected = true
                        client.LastSeen = time.Now()
                        break</span>
                }
        }
        <span class="cov0" title="0">m.mutex.Unlock()</span>
}

// handleDisconnected handles client disconnection events
func (m *Manager) handleDisconnected(accountID int) <span class="cov0" title="0">{
        m.logger.Info("Client disconnected", zap.Int("account_id", accountID))
        m.db.UpdateAccountStatus(accountID, "inactive")

        // Update client status
        m.mutex.Lock()
        for _, client := range m.clients </span><span class="cov0" title="0">{
                if client.ID == accountID </span><span class="cov0" title="0">{
                        client.Connected = false
                        break</span>
                }
        }
        <span class="cov0" title="0">m.mutex.Unlock()</span>
}

// handleLoggedOut handles client logout events
func (m *Manager) handleLoggedOut(accountID int) <span class="cov0" title="0">{
        m.logger.Info("Client logged out", zap.Int("account_id", accountID))
        m.db.UpdateAccountStatus(accountID, "inactive")

        // Remove client
        m.mutex.Lock()
        for phone, client := range m.clients </span><span class="cov0" title="0">{
                if client.ID == accountID </span><span class="cov0" title="0">{
                        delete(m.clients, phone)
                        break</span>
                }
        }
        <span class="cov0" title="0">m.mutex.Unlock()</span>
}

// processMessages processes pending messages with AI
func (m *Manager) processMessages() <span class="cov0" title="0">{
        ticker := time.NewTicker(10 * time.Second)
        defer ticker.Stop()

        for </span><span class="cov0" title="0">{
                select </span>{
                case &lt;-ticker.C:<span class="cov0" title="0">
                        messages, err := m.db.GetPendingMessages(10)
                        if err != nil </span><span class="cov0" title="0">{
                                m.logger.Error("Failed to get pending messages", zap.Error(err))
                                continue</span>
                        }

                        <span class="cov0" title="0">for _, msg := range messages </span><span class="cov0" title="0">{
                                // TODO: Process with AI
                                // For now, just mark as processed
                                m.db.UpdateMessageProcessing(msg.ID, "processed", nil, nil)
                        }</span>

                case &lt;-m.shutdown:<span class="cov0" title="0">
                        return</span>
                }
        }
}

// GetClients returns information about all connected clients
func (m *Manager) GetClients() map[string]*Client <span class="cov0" title="0">{
        m.mutex.RLock()
        defer m.mutex.RUnlock()

        clients := make(map[string]*Client)
        for k, v := range m.clients </span><span class="cov0" title="0">{
                clients[k] = v
        }</span>
        <span class="cov0" title="0">return clients</span>
}

// Shutdown gracefully shuts down all WhatsApp connections
func (m *Manager) Shutdown() <span class="cov0" title="0">{
        m.logger.Info("Shutting down WhatsApp manager")

        close(m.shutdown)

        m.mutex.Lock()
        defer m.mutex.Unlock()

        for phone, client := range m.clients </span><span class="cov0" title="0">{
                if client.Connected </span><span class="cov0" title="0">{
                        client.Client.Disconnect()
                        m.logger.Info("Disconnected client", zap.String("phone", phone))
                }</span>
        }

        <span class="cov0" title="0">m.clients = make(map[string]*Client)</span>
}

// min returns the minimum of two integers
func min(a, b int) int <span class="cov0" title="0">{
        if a &lt; b </span><span class="cov0" title="0">{
                return a
        }</span>
        <span class="cov0" title="0">return b</span>
}
</pre>
		
		<pre class="file" id="file9" style="display: none">package main

import (
        "context"
        "fmt"
        "log"
        "net/http"
        "os"
        "os/signal"
        "syscall"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/joho/godotenv"
        "go.uber.org/zap"

        "medibridge-whatsapp/internal/ai"
        "medibridge-whatsapp/internal/api"
        "medibridge-whatsapp/internal/config"
        "medibridge-whatsapp/internal/database"
        "medibridge-whatsapp/internal/matching"
        "medibridge-whatsapp/internal/performance"
        "medibridge-whatsapp/internal/whatsapp"
)

func main() <span class="cov0" title="0">{
        // Load environment variables
        if err := godotenv.Load(); err != nil </span><span class="cov0" title="0">{
                log.Println("No .env file found")
        }</span>

        // Initialize logger
        <span class="cov0" title="0">logger, err := zap.NewProduction()
        if err != nil </span><span class="cov0" title="0">{
                log.Fatal("Failed to initialize logger:", err)
        }</span>
        <span class="cov0" title="0">defer logger.Sync()

        // Load configuration
        cfg := config.Load()

        // Initialize database
        db, err := database.Connect(cfg.DatabaseURL)
        if err != nil </span><span class="cov0" title="0">{
                logger.Fatal("Failed to connect to database", zap.Error(err))
        }</span>
        <span class="cov0" title="0">defer db.Close()

        // Initialize Ollama client
        ollamaClient := ai.NewOllamaClient(
                cfg.OllamaURL,
                cfg.AIModel,
                time.Duration(cfg.AITimeout)*time.Second,
                logger,
        )

        // Initialize AI message processor
        aiProcessor := ai.NewMessageProcessor(
                logger,
                db,
                ollamaClient,
                10,             // batch size
                10*time.Second, // processing interval
        )

        // Initialize matching service
        matchingService := matching.NewService(
                logger,
                db,
                5*time.Minute, // matching interval
        )

        // Initialize performance optimizer
        optimizer := performance.NewOptimizer(logger)

        // Initialize WhatsApp manager
        waManager := whatsapp.NewManager(logger, db)

        // Initialize API server
        apiServer := api.NewServer(logger, waManager, db, aiProcessor, matchingService)

        // Setup Gin router
        router := gin.Default()
        apiServer.SetupRoutes(router)

        // Create HTTP server
        server := &amp;http.Server{
                Addr:    fmt.Sprintf(":%d", cfg.Port),
                Handler: router,
        }

        // Start AI processor in a goroutine
        ctx, cancel := context.WithCancel(context.Background())
        go aiProcessor.Start(ctx)

        // Start matching service in a goroutine
        go matchingService.Start(ctx)

        // Start performance optimizer
        go optimizer.Start(ctx)

        // Start server in a goroutine
        go func() </span><span class="cov0" title="0">{
                logger.Info("Starting WhatsApp service", zap.Int("port", cfg.Port))
                if err := server.ListenAndServe(); err != nil &amp;&amp; err != http.ErrServerClosed </span><span class="cov0" title="0">{
                        logger.Fatal("Failed to start server", zap.Error(err))
                }</span>
        }()

        // Wait for interrupt signal to gracefully shutdown the server
        <span class="cov0" title="0">quit := make(chan os.Signal, 1)
        signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
        &lt;-quit

        logger.Info("Shutting down server...")

        // Cancel AI processor and matching service
        cancel()

        // Stop matching service
        matchingService.Stop()

        // Graceful shutdown with timeout
        shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
        defer shutdownCancel()

        // Shutdown WhatsApp connections
        waManager.Shutdown()

        // Shutdown HTTP server
        if err := server.Shutdown(shutdownCtx); err != nil </span><span class="cov0" title="0">{
                logger.Fatal("Server forced to shutdown", zap.Error(err))
        }</span>

        <span class="cov0" title="0">logger.Info("Server exited")</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
