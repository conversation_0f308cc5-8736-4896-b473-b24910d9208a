[workspace]
members = [".", "db/*", "crates/*"]
resolver = "2"

[package]
name = "app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
chrono = { workspace = true }
dotenv = { workspace = true }
derive-getters = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
rust_decimal = { workspace = true }
sea-orm = { workspace = true }
sea-orm-migration = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tauri = { workspace = true }
tauri-plugin-opener = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true, features = ["full"] }
toml = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
typed-builder = { workspace = true }
url = { workspace = true }
uuid = { workspace = true }
validator = { workspace = true, features = ["derive"] }

app_config = { workspace = true }
db_entity = { workspace = true }
db_migration = { workspace = true }
db_service = { workspace = true }

[workspace.dependencies]
argon2 = "0.5.3"
async-recursion = "1.1.1"
async-trait = "0.1"
chrono = "0.4.40"
config = "0.15.11"
derive_more = "2.0.1"
derive-getters = "0.5"
dotenv = "0.15.0"
jsonwebtoken = "9.3.1"
pretty_assertions = "1.4.1"
reqwest = "0.11"
rust_decimal = "1.37.1"
sea-orm = "1"
sea-orm-migration = "1"
serde = "1.0"
serde_json = "1.0"
tauri = "2.5.1"
tauri-plugin-opener = "2.2.7"
thiserror = "2"
tokio = "1.45.1"
toml = "0.8.8"
tracing = "0.1"
tracing-subscriber = "0.3"
typed-builder = "0.21.0"
url = "2"
uuid = "1.17.0"
validator = "0.20.0"

app_config = { path = "crates/config" }
db_entity = { path = "db/entity" }
db_migration = { path = "db/migration" }
db_service = { path = "db/service" }
