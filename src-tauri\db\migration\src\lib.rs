use sea_orm_migration::{DbErr, Mi<PERSON><PERSON><PERSON><PERSON>, Mi<PERSON><PERSON><PERSON>rai<PERSON>, sea_orm::DatabaseConnection};

mod m20241224_000001_create_accounts_table;
mod m20241224_000002_create_medicines_table;
mod m20241224_000003_create_messages_table;
mod m20241224_000004_create_message_medicines_table;
mod m20241224_000005_create_matches_table;

pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            Box::new(m20241224_000001_create_accounts_table::Migration),
            Box::new(m20241224_000002_create_medicines_table::Migration),
            Box::new(m20241224_000003_create_messages_table::Migration),
            Box::new(m20241224_000004_create_message_medicines_table::Migration),
            Box::new(m20241224_000005_create_matches_table::Migration),
        ]
    }
}

pub async fn run_migrations(db: &DatabaseConnection) -> Result<(), DbErr> {
    Migrator::up(db, None).await
}
