import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { invoke } from '@tauri-apps/api/core'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { Plus, Smartphone, Wifi, WifiOff, Trash2, Settings } from 'lucide-react'

interface Account {
  id: number
  phone_number: string
  display_name: string
  status: string
  last_connected_at?: string
  created_at: string
  updated_at: string
}

interface Client {
  id: number
  phone_number: string
  display_name: string
  connected: boolean
  last_seen: string
}

export const Route = createFileRoute('/accounts')({
  component: AccountsPage,
})

function AccountsPage() {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newAccount, setNewAccount] = useState({ phone_number: '', display_name: '' })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [accountsData, clientsData] = await Promise.all([
        invoke<Account[]>('get_accounts'),
        invoke<Client[]>('get_whatsapp_clients')
      ])
      setAccounts(accountsData)
      setClients(clientsData)
    } catch (error) {
      console.error('Failed to load data:', error)
      toast.error('فشل في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const createAccount = async () => {
    if (!newAccount.phone_number || !newAccount.display_name) {
      toast.error('يرجى ملء جميع الحقول')
      return
    }

    try {
      await invoke('create_account', {
        phoneNumber: newAccount.phone_number,
        displayName: newAccount.display_name
      })
      toast.success('تم إنشاء الحساب بنجاح')
      setIsCreateDialogOpen(false)
      setNewAccount({ phone_number: '', display_name: '' })
      loadData()
    } catch (error) {
      console.error('Failed to create account:', error)
      toast.error('فشل في إنشاء الحساب')
    }
  }

  const deleteAccount = async (accountId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
      return
    }

    try {
      await invoke('delete_account', { accountId })
      toast.success('تم حذف الحساب بنجاح')
      loadData()
    } catch (error) {
      console.error('Failed to delete account:', error)
      toast.error('فشل في حذف الحساب')
    }
  }

  const connectClient = async (phone: string) => {
    try {
      await invoke('connect_whatsapp_client', { phone })
      toast.success('تم بدء الاتصال')
      loadData()
    } catch (error) {
      console.error('Failed to connect client:', error)
      toast.error('فشل في الاتصال')
    }
  }

  const disconnectClient = async (phone: string) => {
    try {
      await invoke('disconnect_whatsapp_client', { phone })
      toast.success('تم قطع الاتصال')
      loadData()
    } catch (error) {
      console.error('Failed to disconnect client:', error)
      toast.error('فشل في قطع الاتصال')
    }
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: { label: 'نشط', variant: 'default' as const },
      inactive: { label: 'غير نشط', variant: 'secondary' as const },
      error: { label: 'خطأ', variant: 'destructive' as const },
      connecting: { label: 'يتصل', variant: 'outline' as const }
    }
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'outline' as const }
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>
  }

  const getClientForAccount = (phoneNumber: string) => {
    return clients.find(client => client.phone_number === phoneNumber)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدارة الحسابات</h1>
          <p className="text-muted-foreground">إدارة حسابات WhatsApp والاتصالات</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              إضافة حساب جديد
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>إضافة حساب WhatsApp جديد</DialogTitle>
              <DialogDescription>
                أدخل رقم الهاتف واسم العرض للحساب الجديد
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="phone">رقم الهاتف</Label>
                <Input
                  id="phone"
                  placeholder="************"
                  value={newAccount.phone_number}
                  onChange={(e) => setNewAccount(prev => ({ ...prev, phone_number: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="name">اسم العرض</Label>
                <Input
                  id="name"
                  placeholder="صيدلية الشفاء"
                  value={newAccount.display_name}
                  onChange={(e) => setNewAccount(prev => ({ ...prev, display_name: e.target.value }))}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  إلغاء
                </Button>
                <Button onClick={createAccount}>
                  إنشاء الحساب
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {accounts.map((account) => {
          const client = getClientForAccount(account.phone_number)
          const isConnected = client?.connected || false
          
          return (
            <Card key={account.id} className="relative">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Smartphone className="w-5 h-5" />
                    <CardTitle className="text-lg">{account.display_name}</CardTitle>
                  </div>
                  <div className="flex items-center space-x-2">
                    {isConnected ? (
                      <Wifi className="w-4 h-4 text-green-500" />
                    ) : (
                      <WifiOff className="w-4 h-4 text-gray-400" />
                    )}
                    {getStatusBadge(account.status)}
                  </div>
                </div>
                <CardDescription>{account.phone_number}</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  <div>تم الإنشاء: {new Date(account.created_at).toLocaleDateString('ar-EG')}</div>
                  {account.last_connected_at && (
                    <div>آخر اتصال: {new Date(account.last_connected_at).toLocaleDateString('ar-EG')}</div>
                  )}
                  {client?.last_seen && (
                    <div>آخر ظهور: {new Date(client.last_seen).toLocaleDateString('ar-EG')}</div>
                  )}
                </div>
                
                <div className="flex space-x-2">
                  {isConnected ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => disconnectClient(account.phone_number)}
                    >
                      قطع الاتصال
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      onClick={() => connectClient(account.phone_number)}
                    >
                      اتصال
                    </Button>
                  )}
                  
                  <Button variant="outline" size="sm">
                    <Settings className="w-4 h-4" />
                  </Button>
                  
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => deleteAccount(account.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {accounts.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Smartphone className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">لا توجد حسابات</h3>
            <p className="text-muted-foreground text-center mb-4">
              ابدأ بإضافة حساب WhatsApp الأول لبدء مراقبة الرسائل
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              إضافة حساب جديد
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
