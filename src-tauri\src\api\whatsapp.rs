use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::api::client::{WhatsAppClient, PaginationParams, PaginatedResponse};
use crate::error::AppError;

/// WhatsApp account information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Account {
    pub id: i32,
    pub phone_number: String,
    pub display_name: String,
    pub status: String,
    pub last_connected_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// WhatsApp client information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Client {
    pub id: i32,
    pub phone_number: String,
    pub display_name: String,
    pub connected: bool,
    pub last_seen: DateTime<Utc>,
}

/// WhatsApp message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: i32,
    pub whatsapp_message_id: String,
    pub account_id: i32,
    pub group_jid: String,
    pub group_name: String,
    pub sender_jid: String,
    pub sender_name: String,
    pub content: String,
    pub message_type: String,
    pub processing_status: String,
    pub ai_analysis: Option<String>,
    pub error_message: Option<String>,
    pub message_timestamp: DateTime<Utc>,
    pub received_at: DateTime<Utc>,
    pub processed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Statistics overview
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatsOverview {
    pub total_accounts: u32,
    pub active_accounts: u32,
    pub total_messages: u32,
    pub processed_messages: u32,
    pub pending_messages: u32,
    pub total_medicines: u32,
    pub total_matches: u32,
}

/// Message statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageStats {
    pub daily_messages: Vec<DailyMessageCount>,
    pub message_types: MessageTypeCount,
    pub processing_stats: ProcessingStats,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DailyMessageCount {
    pub date: String,
    pub count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageTypeCount {
    pub request: u32,
    pub offer: u32,
    pub unknown: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingStats {
    pub pending: u32,
    pub processed: u32,
    pub failed: u32,
}

/// Request/Response DTOs
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateAccountRequest {
    pub phone_number: String,
    pub display_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateAccountStatusRequest {
    pub status: String,
}

/// WhatsApp API service
pub struct WhatsAppApi {
    client: WhatsAppClient,
}

impl WhatsAppApi {
    pub fn new(base_url: String) -> Self {
        Self {
            client: WhatsAppClient::new(base_url),
        }
    }

    /// Check service health
    pub async fn health_check(&self) -> Result<bool, AppError> {
        match self.client.health_check().await {
            Ok(response) => Ok(response.status == "healthy"),
            Err(_) => Ok(false),
        }
    }

    /// Get all accounts
    pub async fn get_accounts(&self) -> Result<Vec<Account>, AppError> {
        #[derive(Deserialize)]
        struct Response {
            accounts: Vec<Account>,
        }

        let response: Response = self.client.get("/api/v1/accounts").await?;
        Ok(response.accounts)
    }

    /// Create a new account
    pub async fn create_account(&self, request: CreateAccountRequest) -> Result<String, AppError> {
        #[derive(Deserialize)]
        struct Response {
            message: String,
        }

        let response: Response = self.client.post("/api/v1/accounts", &request).await?;
        Ok(response.message)
    }

    /// Update account status
    pub async fn update_account_status(&self, account_id: i32, status: String) -> Result<String, AppError> {
        #[derive(Deserialize)]
        struct Response {
            message: String,
        }

        let request = UpdateAccountStatusRequest { status };
        let endpoint = format!("/api/v1/accounts/{}/status", account_id);
        let response: Response = self.client.put(&endpoint, &request).await?;
        Ok(response.message)
    }

    /// Delete account
    pub async fn delete_account(&self, account_id: i32) -> Result<String, AppError> {
        #[derive(Deserialize)]
        struct Response {
            message: String,
        }

        let endpoint = format!("/api/v1/accounts/{}", account_id);
        let response: Response = self.client.delete(&endpoint).await?;
        Ok(response.message)
    }

    /// Get all connected clients
    pub async fn get_clients(&self) -> Result<Vec<Client>, AppError> {
        #[derive(Deserialize)]
        struct Response {
            clients: std::collections::HashMap<String, Client>,
        }

        let response: Response = self.client.get("/api/v1/clients").await?;
        Ok(response.clients.into_values().collect())
    }

    /// Connect a client
    pub async fn connect_client(&self, phone: &str) -> Result<String, AppError> {
        #[derive(Deserialize)]
        struct Response {
            message: String,
        }

        let endpoint = format!("/api/v1/clients/{}/connect", phone);
        let response: Response = self.client.post(&endpoint, &()).await?;
        Ok(response.message)
    }

    /// Disconnect a client
    pub async fn disconnect_client(&self, phone: &str) -> Result<String, AppError> {
        #[derive(Deserialize)]
        struct Response {
            message: String,
        }

        let endpoint = format!("/api/v1/clients/{}/disconnect", phone);
        let response: Response = self.client.post(&endpoint, &()).await?;
        Ok(response.message)
    }

    /// Get QR code for pairing
    pub async fn get_qr_code(&self, phone: &str) -> Result<String, AppError> {
        #[derive(Deserialize)]
        struct Response {
            qr_code: String,
        }

        let endpoint = format!("/api/v1/clients/{}/qr", phone);
        let response: Response = self.client.get(&endpoint).await?;
        Ok(response.qr_code)
    }

    /// Get messages with pagination
    pub async fn get_messages(&self, params: PaginationParams) -> Result<PaginatedResponse<Message>, AppError> {
        let mut endpoint = "/api/v1/messages?".to_string();
        
        if let Some(limit) = params.limit {
            endpoint.push_str(&format!("limit={}&", limit));
        }
        if let Some(offset) = params.offset {
            endpoint.push_str(&format!("offset={}&", offset));
        }
        if let Some(status) = params.status {
            endpoint.push_str(&format!("status={}&", status));
        }

        // Remove trailing &
        if endpoint.ends_with('&') {
            endpoint.pop();
        }

        self.client.get(&endpoint).await
    }

    /// Get a specific message
    pub async fn get_message(&self, message_id: i32) -> Result<Message, AppError> {
        let endpoint = format!("/api/v1/messages/{}", message_id);
        self.client.get(&endpoint).await
    }

    /// Reprocess a message
    pub async fn reprocess_message(&self, message_id: i32) -> Result<String, AppError> {
        #[derive(Deserialize)]
        struct Response {
            message: String,
        }

        let endpoint = format!("/api/v1/messages/{}/reprocess", message_id);
        let response: Response = self.client.post(&endpoint, &()).await?;
        Ok(response.message)
    }

    /// Get statistics overview
    pub async fn get_stats_overview(&self) -> Result<StatsOverview, AppError> {
        self.client.get("/api/v1/stats/overview").await
    }

    /// Get message statistics
    pub async fn get_message_stats(&self) -> Result<MessageStats, AppError> {
        self.client.get("/api/v1/stats/messages").await
    }
}
