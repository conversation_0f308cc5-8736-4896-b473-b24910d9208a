package performance

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
)

func TestNewOptimizer(t *testing.T) {
	logger := zaptest.NewLogger(t)
	optimizer := NewOptimizer(logger)

	assert.NotNil(t, optimizer)
	assert.Equal(t, logger, optimizer.logger)
	assert.NotNil(t, optimizer.metrics)
	assert.NotNil(t, optimizer.connectionPool)
	assert.NotNil(t, optimizer.cacheManager)
	assert.NotNil(t, optimizer.rateLimiter)
	assert.Equal(t, 80.0, optimizer.memoryThreshold)
	assert.Equal(t, 70.0, optimizer.cpuThreshold)
}

func TestConnectionPool(t *testing.T) {
	pool := NewConnectionPool(5)

	// Test initial state
	assert.Equal(t, 0, pool.GetActiveConnections())

	// Test acquiring connections
	for i := 0; i < 5; i++ {
		acquired := pool.AcquireConnection()
		assert.True(t, acquired)
		assert.Equal(t, i+1, pool.GetActiveConnections())
	}

	// Test exceeding limit
	acquired := pool.AcquireConnection()
	assert.False(t, acquired)
	assert.Equal(t, 5, pool.GetActiveConnections())

	// Test releasing connections
	pool.ReleaseConnection()
	assert.Equal(t, 4, pool.GetActiveConnections())

	// Test releasing when no active connections
	for i := 0; i < 5; i++ {
		pool.ReleaseConnection()
	}
	assert.Equal(t, 0, pool.GetActiveConnections())
}

func TestCacheManager(t *testing.T) {
	cache := NewCacheManager(3)

	// Test setting and getting items
	cache.Set("key1", "value1", time.Hour)
	cache.Set("key2", "value2", time.Hour)

	value, exists := cache.Get("key1")
	assert.True(t, exists)
	assert.Equal(t, "value1", value)

	value, exists = cache.Get("key2")
	assert.True(t, exists)
	assert.Equal(t, "value2", value)

	// Test non-existent key
	value, exists = cache.Get("nonexistent")
	assert.False(t, exists)
	assert.Nil(t, value)

	// Test cache eviction when full
	cache.Set("key3", "value3", time.Hour)
	cache.Set("key4", "value4", time.Hour) // Should evict oldest

	// Check hit rate
	hitRate := cache.GetHitRate()
	assert.Greater(t, hitRate, 0.0)
	assert.LessOrEqual(t, hitRate, 1.0)
}

func TestCacheExpiration(t *testing.T) {
	cache := NewCacheManager(10)

	// Set item with short TTL
	cache.Set("short", "value", 10*time.Millisecond)
	cache.Set("long", "value", time.Hour)

	// Immediately should exist
	value, exists := cache.Get("short")
	assert.True(t, exists)
	assert.Equal(t, "value", value)

	// Wait for expiration
	time.Sleep(20 * time.Millisecond)

	// Should be expired
	value, exists = cache.Get("short")
	assert.False(t, exists)
	assert.Nil(t, value)

	// Long TTL should still exist
	value, exists = cache.Get("long")
	assert.True(t, exists)
	assert.Equal(t, "value", value)
}

func TestCacheCleanExpired(t *testing.T) {
	cache := NewCacheManager(10)

	// Add items with different TTLs
	cache.Set("expired1", "value1", 10*time.Millisecond)
	cache.Set("expired2", "value2", 10*time.Millisecond)
	cache.Set("valid", "value3", time.Hour)

	// Wait for some to expire
	time.Sleep(20 * time.Millisecond)

	// Clean expired items
	cache.CleanExpired()

	// Check that expired items are gone
	_, exists := cache.Get("expired1")
	assert.False(t, exists)

	_, exists = cache.Get("expired2")
	assert.False(t, exists)

	// Valid item should still exist
	value, exists := cache.Get("valid")
	assert.True(t, exists)
	assert.Equal(t, "value3", value)
}

func TestCacheReduceSize(t *testing.T) {
	cache := NewCacheManager(10)

	// Fill cache
	for i := 0; i < 10; i++ {
		cache.Set(fmt.Sprintf("key%d", i), fmt.Sprintf("value%d", i), time.Hour)
	}

	// Reduce by 50%
	cache.ReduceSize(0.5)

	// Should have approximately 5 items left
	count := 0
	for i := 0; i < 10; i++ {
		_, exists := cache.Get(fmt.Sprintf("key%d", i))
		if exists {
			count++
		}
	}
	assert.LessOrEqual(t, count, 5)
}

func TestRateLimiter(t *testing.T) {
	limiter := NewRateLimiter(3, time.Minute)

	identifier := "test-user"

	// Should allow first 3 requests
	for i := 0; i < 3; i++ {
		allowed := limiter.Allow(identifier)
		assert.True(t, allowed)
	}

	// Should deny 4th request
	allowed := limiter.Allow(identifier)
	assert.False(t, allowed)

	// Different identifier should be allowed
	allowed = limiter.Allow("other-user")
	assert.True(t, allowed)
}

func TestRateLimiterTimeWindow(t *testing.T) {
	limiter := NewRateLimiter(2, 100*time.Millisecond)

	identifier := "test-user"

	// Use up the limit
	assert.True(t, limiter.Allow(identifier))
	assert.True(t, limiter.Allow(identifier))
	assert.False(t, limiter.Allow(identifier))

	// Wait for window to reset
	time.Sleep(150 * time.Millisecond)

	// Should be allowed again
	assert.True(t, limiter.Allow(identifier))
}

func TestRateLimiterCleanOld(t *testing.T) {
	limiter := NewRateLimiter(5, 100*time.Millisecond)

	// Add requests for multiple identifiers
	limiter.Allow("user1")
	limiter.Allow("user2")
	limiter.Allow("user3")

	// Wait for entries to become old
	time.Sleep(150 * time.Millisecond)

	// Clean old entries
	limiter.CleanOld()

	// Verify internal state is cleaned (indirect test)
	// After cleaning, all users should have fresh limits
	for i := 0; i < 5; i++ {
		assert.True(t, limiter.Allow("user1"))
	}
	assert.False(t, limiter.Allow("user1"))
}

func TestOptimizerMetrics(t *testing.T) {
	logger := zaptest.NewLogger(t)
	optimizer := NewOptimizer(logger)

	// Get initial metrics
	metrics := optimizer.GetMetrics()
	assert.NotNil(t, metrics)
	assert.GreaterOrEqual(t, metrics.MemoryUsage, 0.0)
	assert.GreaterOrEqual(t, metrics.ActiveConnections, 0)
	assert.GreaterOrEqual(t, metrics.CacheHitRate, 0.0)
	assert.LessOrEqual(t, metrics.CacheHitRate, 1.0)
}

func TestOptimizerStart(t *testing.T) {
	logger := zaptest.NewLogger(t)
	optimizer := NewOptimizer(logger)

	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	// Start optimizer (should not block)
	optimizer.Start(ctx)

	// Wait for context to expire
	<-ctx.Done()

	// Test should complete without hanging
}

func TestPerformCleanup(t *testing.T) {
	logger := zaptest.NewLogger(t)
	optimizer := NewOptimizer(logger)

	// Add some cache items
	optimizer.cacheManager.Set("test1", "value1", 10*time.Millisecond)
	optimizer.cacheManager.Set("test2", "value2", time.Hour)

	// Add some rate limiter entries
	optimizer.rateLimiter.Allow("user1")

	// Wait for some items to expire
	time.Sleep(20 * time.Millisecond)

	// Perform cleanup
	optimizer.performCleanup()

	// Expired cache items should be cleaned
	_, exists := optimizer.cacheManager.Get("test1")
	assert.False(t, exists)

	// Valid items should remain
	_, exists = optimizer.cacheManager.Get("test2")
	assert.True(t, exists)
}

func TestCheckResourceUsage(t *testing.T) {
	logger := zaptest.NewLogger(t)
	optimizer := NewOptimizer(logger)

	// Set a very low memory threshold to trigger optimization
	optimizer.memoryThreshold = 0.1 // 0.1 MB

	// Add items to cache
	for i := 0; i < 100; i++ {
		optimizer.cacheManager.Set(fmt.Sprintf("key%d", i), "large value data", time.Hour)
	}

	// Check resource usage (should trigger optimization)
	optimizer.checkResourceUsage()

	// Cache should be reduced
	count := 0
	for i := 0; i < 100; i++ {
		_, exists := optimizer.cacheManager.Get(fmt.Sprintf("key%d", i))
		if exists {
			count++
		}
	}
	assert.Less(t, count, 100) // Some items should be evicted
}

// Benchmark tests
func BenchmarkCacheSet(b *testing.B) {
	cache := NewCacheManager(1000)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		cache.Set(fmt.Sprintf("key%d", i), fmt.Sprintf("value%d", i), time.Hour)
	}
}

func BenchmarkCacheGet(b *testing.B) {
	cache := NewCacheManager(1000)

	// Pre-populate cache
	for i := 0; i < 1000; i++ {
		cache.Set(fmt.Sprintf("key%d", i), fmt.Sprintf("value%d", i), time.Hour)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		cache.Get(fmt.Sprintf("key%d", i%1000))
	}
}

func BenchmarkRateLimiterAllow(b *testing.B) {
	limiter := NewRateLimiter(1000, time.Minute)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		limiter.Allow(fmt.Sprintf("user%d", i%100))
	}
}

func BenchmarkConnectionPoolAcquire(b *testing.B) {
	pool := NewConnectionPool(100)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		if pool.AcquireConnection() {
			pool.ReleaseConnection()
		}
	}
}

// Test concurrent access
func TestConcurrentCacheAccess(t *testing.T) {
	cache := NewCacheManager(100)

	done := make(chan bool, 20)

	// Concurrent writers
	for i := 0; i < 10; i++ {
		go func(id int) {
			for j := 0; j < 10; j++ {
				cache.Set(fmt.Sprintf("key%d-%d", id, j), fmt.Sprintf("value%d-%d", id, j), time.Hour)
			}
			done <- true
		}(i)
	}

	// Concurrent readers
	for i := 0; i < 10; i++ {
		go func(id int) {
			for j := 0; j < 10; j++ {
				cache.Get(fmt.Sprintf("key%d-%d", id, j))
			}
			done <- true
		}(i)
	}

	// Wait for all goroutines
	for i := 0; i < 20; i++ {
		<-done
	}

	// Test should complete without race conditions
}

func TestConcurrentRateLimiter(t *testing.T) {
	limiter := NewRateLimiter(100, time.Minute)

	done := make(chan bool, 10)

	// Concurrent access
	for i := 0; i < 10; i++ {
		go func(id int) {
			for j := 0; j < 10; j++ {
				limiter.Allow(fmt.Sprintf("user%d", id))
			}
			done <- true
		}(i)
	}

	// Wait for all goroutines
	for i := 0; i < 10; i++ {
		<-done
	}

	// Test should complete without race conditions
}
