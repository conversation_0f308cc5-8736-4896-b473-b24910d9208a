use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "accounts")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    
    /// WhatsApp phone number (e.g., "************")
    #[sea_orm(unique)]
    pub phone_number: String,
    
    /// Display name for the account
    pub display_name: String,
    
    /// WhatsApp session data (encrypted)
    pub session_data: Option<String>,
    
    /// Account status: active, inactive, error
    pub status: String,
    
    /// Last connection timestamp
    pub last_connected_at: Option<DateTime>,
    
    /// Account creation timestamp
    pub created_at: DateTime,
    
    /// Last update timestamp
    pub updated_at: DateTime,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::messages::Entity")]
    Messages,
}

impl Related<super::messages::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Messages.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Account status enum
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AccountStatus {
    Active,
    Inactive,
    Error,
    Connecting,
}

impl std::fmt::Display for AccountStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AccountStatus::Active => write!(f, "active"),
            AccountStatus::Inactive => write!(f, "inactive"),
            AccountStatus::Error => write!(f, "error"),
            AccountStatus::Connecting => write!(f, "connecting"),
        }
    }
}

impl From<String> for AccountStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "active" => AccountStatus::Active,
            "inactive" => AccountStatus::Inactive,
            "error" => AccountStatus::Error,
            "connecting" => AccountStatus::Connecting,
            _ => AccountStatus::Inactive,
        }
    }
}
