package matching

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap/zaptest"

	"medibridge-whatsapp/internal/database"
)

// MockDB is a mock implementation of database.DB for testing
type MockDB struct {
	mock.Mock
}

func (m *MockDB) Query(query string, args ...interface{}) (*database.Rows, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Rows), mockArgs.Error(1)
}

func (m *MockDB) QueryRow(query string, args ...interface{}) *database.Row {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Row)
}

func (m *MockDB) Exec(query string, args ...interface{}) (database.Result, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(database.Result), mockArgs.Error(1)
}

func TestNewMatchingEngine(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}

	engine := NewMatchingEngine(logger, mockDB)

	assert.NotNil(t, engine)
	assert.Equal(t, logger, engine.logger)
	assert.Equal(t, mockDB, engine.db)
}

func TestCalculateNameSimilarity(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	tests := []struct {
		name1    string
		name2    string
		expected float64
	}{
		{"paracetamol", "paracetamol", 1.0},
		{"paracetamol 500mg", "paracetamol", 0.5},
		{"aspirin", "paracetamol", 0.0},
		{"vitamin d", "vitamin d3", 0.5},
		{"", "paracetamol", 0.0},
		{"paracetamol", "", 0.0},
	}

	for _, test := range tests {
		t.Run(test.name1+"_vs_"+test.name2, func(t *testing.T) {
			similarity := engine.calculateNameSimilarity(test.name1, test.name2)
			assert.Equal(t, test.expected, similarity)
		})
	}
}

func TestMedicinesMatch(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	tests := []struct {
		name     string
		req      MessageMedicine
		offer    MessageMedicine
		expected bool
	}{
		{
			name: "exact_id_match",
			req:  MessageMedicine{MedicineID: 1, MedicineName: "paracetamol"},
			offer: MessageMedicine{MedicineID: 1, MedicineName: "paracetamol"},
			expected: true,
		},
		{
			name: "name_similarity_match",
			req:  MessageMedicine{MedicineID: 1, MedicineName: "paracetamol 500mg"},
			offer: MessageMedicine{MedicineID: 2, MedicineName: "paracetamol"},
			expected: false, // Only 50% similarity, below 80% threshold
		},
		{
			name: "high_similarity_match",
			req:  MessageMedicine{MedicineID: 1, MedicineName: "vitamin d drops"},
			offer: MessageMedicine{MedicineID: 2, MedicineName: "vitamin d"},
			expected: false, // 66% similarity, below 80% threshold
		},
		{
			name: "no_match",
			req:  MessageMedicine{MedicineID: 1, MedicineName: "aspirin"},
			offer: MessageMedicine{MedicineID: 2, MedicineName: "paracetamol"},
			expected: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := engine.medicinesMatch(test.req, test.offer)
			assert.Equal(t, test.expected, result)
		})
	}
}

func TestCalculateMatch(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	now := time.Now()
	request := &database.Message{
		ID:               1,
		MessageTimestamp: now.Add(-1 * time.Hour),
	}
	offer := &database.Message{
		ID:               2,
		MessageTimestamp: now,
	}

	reqMed := MessageMedicine{
		MedicineID:      1,
		MedicineName:    "paracetamol",
		ConfidenceScore: 0.9,
	}
	offerMed := MessageMedicine{
		MedicineID:      1,
		MedicineName:    "paracetamol",
		ConfidenceScore: 0.8,
	}

	match := engine.calculateMatch(request, offer, reqMed, offerMed)

	assert.Equal(t, request.ID, match.RequestMessageID)
	assert.Equal(t, offer.ID, match.OfferMessageID)
	assert.Equal(t, reqMed.MedicineID, match.MedicineID)
	assert.Greater(t, match.ConfidenceScore, 0.0)
	assert.LessOrEqual(t, match.ConfidenceScore, 1.0)
	
	// Should get bonus for exact medicine match
	assert.Greater(t, match.ConfidenceScore, 0.8)
}

func TestStoreMatch(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	match := Match{
		RequestMessageID: 1,
		OfferMessageID:   2,
		MedicineID:       1,
		ConfidenceScore:  0.85,
		MatchingCriteria: MatchingCriteria{
			NameSimilarity: 1.0,
			TimeRelevance:  0.8,
		},
	}

	// Mock the check query to return no existing match
	mockDB.On("QueryRow", mock.AnythingOfType("string"), 1, 2, 1).Return(&database.Row{})

	// Mock the insert query
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(database.Result{}, nil)

	err := engine.storeMatch(match)
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestCleanupExpiredMatches(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	// Mock the cleanup query
	mockResult := &MockResult{rowsAffected: 5}
	mockDB.On("Exec", mock.AnythingOfType("string")).Return(mockResult, nil)

	err := engine.CleanupExpiredMatches()
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

// MockResult implements database.Result interface for testing
type MockResult struct {
	rowsAffected int64
}

func (m *MockResult) RowsAffected() (int64, error) {
	return m.rowsAffected, nil
}

func (m *MockResult) LastInsertId() (int64, error) {
	return 0, nil
}

// Benchmark tests for performance
func BenchmarkCalculateNameSimilarity(b *testing.B) {
	logger := zaptest.NewLogger(b)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	name1 := "paracetamol 500mg tablets"
	name2 := "paracetamol extra strength"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.calculateNameSimilarity(name1, name2)
	}
}

func BenchmarkCalculateMatch(b *testing.B) {
	logger := zaptest.NewLogger(b)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	now := time.Now()
	request := &database.Message{
		ID:               1,
		MessageTimestamp: now.Add(-1 * time.Hour),
	}
	offer := &database.Message{
		ID:               2,
		MessageTimestamp: now,
	}

	reqMed := MessageMedicine{
		MedicineID:      1,
		MedicineName:    "paracetamol 500mg",
		ConfidenceScore: 0.9,
	}
	offerMed := MessageMedicine{
		MedicineID:      1,
		MedicineName:    "paracetamol",
		ConfidenceScore: 0.8,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.calculateMatch(request, offer, reqMed, offerMed)
	}
}

// Integration test with real database (requires test database)
func TestMatchingEngineIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// This would require a test database setup
	// For now, we'll skip this test
	t.Skip("Integration test requires test database setup")
}

// Test error handling
func TestFindMatchesErrorHandling(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	// Mock database error
	mockDB.On("Query", mock.AnythingOfType("string")).Return((*database.Rows)(nil), assert.AnError)

	ctx := context.Background()
	err := engine.FindMatches(ctx)
	
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get requests")

	mockDB.AssertExpectations(t)
}
