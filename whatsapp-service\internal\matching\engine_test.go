package matching

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"

	"medibridge-whatsapp/internal/database"
)

// MockDB is a mock implementation of database.DB for testing
type MockDB struct {
	mock.Mock
}

func (m *MockDB) Query(query string, args ...interface{}) (*database.Rows, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Rows), mockArgs.Error(1)
}

func (m *MockDB) QueryRow(query string, args ...interface{}) *database.Row {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Row)
}

func (m *MockDB) Exec(query string, args ...interface{}) (database.Result, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(database.Result), mockArgs.Error(1)
}

func TestNewMatchingEngine(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}

	engine := NewMatchingEngine(logger, mockDB)

	assert.NotNil(t, engine)
	assert.Equal(t, logger, engine.logger)
	assert.Equal(t, mockDB, engine.db)
}

func TestCalculateNameSimilarity(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	tests := []struct {
		name1    string
		name2    string
		expected float64
	}{
		{"paracetamol", "paracetamol", 1.0},
		{"paracetamol 500mg", "paracetamol", 0.5},
		{"aspirin", "paracetamol", 0.0},
		{"vitamin d", "vitamin d3", 0.5},
		{"", "paracetamol", 0.0},
		{"paracetamol", "", 0.0},
	}

	for _, test := range tests {
		t.Run(test.name1+"_vs_"+test.name2, func(t *testing.T) {
			similarity := engine.calculateNameSimilarity(test.name1, test.name2)
			assert.Equal(t, test.expected, similarity)
		})
	}
}

func TestMedicinesMatch(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	tests := []struct {
		name     string
		req      MessageMedicine
		offer    MessageMedicine
		expected bool
	}{
		{
			name:     "exact_id_match",
			req:      MessageMedicine{MedicineID: 1, MedicineName: "paracetamol"},
			offer:    MessageMedicine{MedicineID: 1, MedicineName: "paracetamol"},
			expected: true,
		},
		{
			name:     "name_similarity_match",
			req:      MessageMedicine{MedicineID: 1, MedicineName: "paracetamol 500mg"},
			offer:    MessageMedicine{MedicineID: 2, MedicineName: "paracetamol"},
			expected: false, // Only 50% similarity, below 80% threshold
		},
		{
			name:     "high_similarity_match",
			req:      MessageMedicine{MedicineID: 1, MedicineName: "vitamin d drops"},
			offer:    MessageMedicine{MedicineID: 2, MedicineName: "vitamin d"},
			expected: false, // 66% similarity, below 80% threshold
		},
		{
			name:     "no_match",
			req:      MessageMedicine{MedicineID: 1, MedicineName: "aspirin"},
			offer:    MessageMedicine{MedicineID: 2, MedicineName: "paracetamol"},
			expected: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := engine.medicinesMatch(test.req, test.offer)
			assert.Equal(t, test.expected, result)
		})
	}
}

func TestCalculateMatch(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	now := time.Now()
	request := &database.Message{
		ID:               1,
		MessageTimestamp: now.Add(-1 * time.Hour),
	}
	offer := &database.Message{
		ID:               2,
		MessageTimestamp: now,
	}

	reqMed := MessageMedicine{
		MedicineID:      1,
		MedicineName:    "paracetamol",
		ConfidenceScore: 0.9,
	}
	offerMed := MessageMedicine{
		MedicineID:      1,
		MedicineName:    "paracetamol",
		ConfidenceScore: 0.8,
	}

	match := engine.calculateMatch(request, offer, reqMed, offerMed)

	assert.Equal(t, request.ID, match.RequestMessageID)
	assert.Equal(t, offer.ID, match.OfferMessageID)
	assert.Equal(t, reqMed.MedicineID, match.MedicineID)
	assert.Greater(t, match.ConfidenceScore, 0.0)
	assert.LessOrEqual(t, match.ConfidenceScore, 1.0)

	// Should get bonus for exact medicine match
	assert.Greater(t, match.ConfidenceScore, 0.8)
}

func TestStoreMatch(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	match := Match{
		RequestMessageID: 1,
		OfferMessageID:   2,
		MedicineID:       1,
		ConfidenceScore:  0.85,
		MatchingCriteria: MatchingCriteria{
			NameSimilarity: 1.0,
			TimeRelevance:  0.8,
		},
	}

	// Mock the check query to return no existing match
	mockDB.On("QueryRow", mock.AnythingOfType("string"), 1, 2, 1).Return(&database.Row{})

	// Mock the insert query
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(database.Result{}, nil)

	err := engine.storeMatch(match)
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestCleanupExpiredMatches(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	// Mock the cleanup query
	mockResult := &MockResult{rowsAffected: 5}
	mockDB.On("Exec", mock.AnythingOfType("string")).Return(mockResult, nil)

	err := engine.CleanupExpiredMatches()
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

// MockResult implements database.Result interface for testing
type MockResult struct {
	rowsAffected int64
}

func (m *MockResult) RowsAffected() (int64, error) {
	return m.rowsAffected, nil
}

func (m *MockResult) LastInsertId() (int64, error) {
	return 0, nil
}

// Benchmark tests for performance
func BenchmarkCalculateNameSimilarity(b *testing.B) {
	logger := zaptest.NewLogger(b)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	name1 := "paracetamol 500mg tablets"
	name2 := "paracetamol extra strength"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.calculateNameSimilarity(name1, name2)
	}
}

func BenchmarkCalculateMatch(b *testing.B) {
	logger := zaptest.NewLogger(b)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	now := time.Now()
	request := &database.Message{
		ID:               1,
		MessageTimestamp: now.Add(-1 * time.Hour),
	}
	offer := &database.Message{
		ID:               2,
		MessageTimestamp: now,
	}

	reqMed := MessageMedicine{
		MedicineID:      1,
		MedicineName:    "paracetamol 500mg",
		ConfidenceScore: 0.9,
	}
	offerMed := MessageMedicine{
		MedicineID:      1,
		MedicineName:    "paracetamol",
		ConfidenceScore: 0.8,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.calculateMatch(request, offer, reqMed, offerMed)
	}
}

// Integration test with real database (requires test database)
func TestMatchingEngineIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// This would require a test database setup
	// For now, we'll skip this test
	t.Skip("Integration test requires test database setup")
}

// Test error handling
func TestFindMatchesErrorHandling(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	// Mock database error
	mockDB.On("Query", mock.AnythingOfType("string")).Return((*database.Rows)(nil), assert.AnError)

	ctx := context.Background()
	err := engine.FindMatches(ctx)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get requests")

	mockDB.AssertExpectations(t)
}

// Additional comprehensive tests

func TestGetUnprocessedRequestsWithRealDB(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := zaptest.NewLogger(t)
	engine := NewMatchingEngine(logger, &database.DB{DB: db})

	// Mock successful query
	rows := sqlmock.NewRows([]string{
		"id", "whatsapp_message_id", "account_id", "group_jid", "group_name",
		"sender_jid", "sender_name", "content", "message_type", "processing_status",
		"ai_analysis", "error_message", "message_timestamp", "received_at",
		"processed_at", "created_at", "updated_at",
	}).AddRow(
		1, "msg1", "acc1", "group1", "Test Group",
		"sender1", "Test Sender", "أحتاج باراسيتامول", "request", "processed",
		`{"medicines": [{"name": "paracetamol", "confidence": 0.9}]}`, nil,
		time.Now(), time.Now(), time.Now(), time.Now(), time.Now(),
	)

	mock.ExpectQuery("SELECT (.+) FROM messages").WillReturnRows(rows)

	messages, err := engine.getUnprocessedRequests()
	assert.NoError(t, err)
	assert.Len(t, messages, 1)
	assert.Equal(t, "أحتاج باراسيتامول", messages[0].Content)
	assert.Equal(t, "request", messages[0].MessageType)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestGetAvailableOffersWithRealDB(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := zaptest.NewLogger(t)
	engine := NewMatchingEngine(logger, &database.DB{DB: db})

	// Mock successful query
	rows := sqlmock.NewRows([]string{
		"id", "whatsapp_message_id", "account_id", "group_jid", "group_name",
		"sender_jid", "sender_name", "content", "message_type", "processing_status",
		"ai_analysis", "error_message", "message_timestamp", "received_at",
		"processed_at", "created_at", "updated_at",
	}).AddRow(
		2, "msg2", "acc2", "group2", "Test Group 2",
		"sender2", "Test Sender 2", "متوفر باراسيتامول", "offer", "processed",
		`{"medicines": [{"name": "paracetamol", "confidence": 0.8}]}`, nil,
		time.Now(), time.Now(), time.Now(), time.Now(), time.Now(),
	)

	mock.ExpectQuery("SELECT (.+) FROM messages").WillReturnRows(rows)

	messages, err := engine.getAvailableOffers()
	assert.NoError(t, err)
	assert.Len(t, messages, 1)
	assert.Equal(t, "متوفر باراسيتامول", messages[0].Content)
	assert.Equal(t, "offer", messages[0].MessageType)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestGetMessageMedicinesWithRealDB(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := zaptest.NewLogger(t)
	engine := NewMatchingEngine(logger, &database.DB{DB: db})

	// Mock successful query
	rows := sqlmock.NewRows([]string{
		"id", "message_id", "medicine_id", "name", "confidence_score",
		"quantity", "unit", "price", "currency",
	}).AddRow(
		1, 1, 1, "paracetamol", 0.9,
		"10", "tablets", "50", "EGP",
	).AddRow(
		2, 1, 2, "aspirin", 0.7,
		"20", "tablets", "30", "EGP",
	)

	mock.ExpectQuery("SELECT (.+) FROM message_medicines").WillReturnRows(rows)

	medicines, err := engine.getMessageMedicines(1)
	assert.NoError(t, err)
	assert.Len(t, medicines, 2)
	assert.Equal(t, "paracetamol", medicines[0].MedicineName)
	assert.Equal(t, 0.9, medicines[0].ConfidenceScore)
	assert.Equal(t, "aspirin", medicines[1].MedicineName)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestStoreMatchWithRealDB(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := zaptest.NewLogger(t)
	engine := NewMatchingEngine(logger, &database.DB{DB: db})

	match := Match{
		RequestMessageID: 1,
		OfferMessageID:   2,
		MedicineID:       1,
		ConfidenceScore:  0.85,
		MatchingCriteria: MatchingCriteria{
			NameSimilarity: 1.0,
			TimeRelevance:  0.8,
		},
	}

	// Mock check for existing match (no existing match)
	mock.ExpectQuery("SELECT id FROM matches").WillReturnError(sql.ErrNoRows)

	// Mock successful insert
	mock.ExpectExec("INSERT INTO matches").
		WithArgs(1, 2, 1, 0.85, "pending", mock.AnythingOfType("string"),
			mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"),
			mock.AnythingOfType("time.Time")).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err = engine.storeMatch(match)
	assert.NoError(t, err)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestStoreMatchDuplicate(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := zaptest.NewLogger(t)
	engine := NewMatchingEngine(logger, &database.DB{DB: db})

	match := Match{
		RequestMessageID: 1,
		OfferMessageID:   2,
		MedicineID:       1,
		ConfidenceScore:  0.85,
	}

	// Mock existing match found
	rows := sqlmock.NewRows([]string{"id"}).AddRow(1)
	mock.ExpectQuery("SELECT id FROM matches").WillReturnRows(rows)

	err = engine.storeMatch(match)
	assert.NoError(t, err) // Should not error on duplicate

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCleanupExpiredMatchesWithRealDB(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := zaptest.NewLogger(t)
	engine := NewMatchingEngine(logger, &database.DB{DB: db})

	// Mock successful cleanup
	mock.ExpectExec("UPDATE matches").
		WillReturnResult(sqlmock.NewResult(0, 5))

	err = engine.CleanupExpiredMatches()
	assert.NoError(t, err)

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Table-driven tests for name similarity
func TestCalculateNameSimilarityTableDriven(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	testCases := []struct {
		name     string
		name1    string
		name2    string
		expected float64
	}{
		{"identical_names", "paracetamol", "paracetamol", 1.0},
		{"partial_match", "paracetamol 500mg", "paracetamol", 0.5},
		{"no_match", "aspirin", "paracetamol", 0.0},
		{"case_insensitive", "PARACETAMOL", "paracetamol", 1.0},
		{"arabic_names", "باراسيتامول", "باراسيتامول", 1.0},
		{"mixed_arabic", "باراسيتامول 500", "باراسيتامول", 0.5},
		{"empty_first", "", "paracetamol", 0.0},
		{"empty_second", "paracetamol", "", 0.0},
		{"both_empty", "", "", 0.0},
		{"single_word_match", "vitamin", "vitamin", 1.0},
		{"multi_word_partial", "vitamin d3 drops", "vitamin d", 0.5},
		{"complex_match", "paracetamol extra strength 500mg", "paracetamol 500mg", 0.5},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := engine.calculateNameSimilarity(tc.name1, tc.name2)
			assert.Equal(t, tc.expected, result, "Expected %f for %s vs %s, got %f",
				tc.expected, tc.name1, tc.name2, result)
		})
	}
}

// Test time relevance calculation
func TestTimeRelevanceCalculation(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	engine := NewMatchingEngine(logger, mockDB)

	now := time.Now()

	testCases := []struct {
		name          string
		requestTime   time.Time
		offerTime     time.Time
		expectedRange [2]float64 // min, max expected values
	}{
		{
			name:          "same_time",
			requestTime:   now,
			offerTime:     now,
			expectedRange: [2]float64{1.0, 1.0},
		},
		{
			name:          "within_24_hours",
			requestTime:   now,
			offerTime:     now.Add(12 * time.Hour),
			expectedRange: [2]float64{1.0, 1.0},
		},
		{
			name:          "3_days_apart",
			requestTime:   now,
			offerTime:     now.Add(72 * time.Hour),
			expectedRange: [2]float64{0.7, 0.9},
		},
		{
			name:          "week_apart",
			requestTime:   now,
			offerTime:     now.Add(168 * time.Hour),
			expectedRange: [2]float64{0.5, 0.5},
		},
		{
			name:          "old_offer",
			requestTime:   now,
			offerTime:     now.Add(-72 * time.Hour),
			expectedRange: [2]float64{0.7, 0.9},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			request := &database.Message{
				ID:               1,
				MessageTimestamp: tc.requestTime,
			}
			offer := &database.Message{
				ID:               2,
				MessageTimestamp: tc.offerTime,
			}

			reqMed := MessageMedicine{
				MedicineID:      1,
				MedicineName:    "paracetamol",
				ConfidenceScore: 0.9,
			}
			offerMed := MessageMedicine{
				MedicineID:      1,
				MedicineName:    "paracetamol",
				ConfidenceScore: 0.8,
			}

			match := engine.calculateMatch(request, offer, reqMed, offerMed)

			assert.GreaterOrEqual(t, match.MatchingCriteria.TimeRelevance, tc.expectedRange[0])
			assert.LessOrEqual(t, match.MatchingCriteria.TimeRelevance, tc.expectedRange[1])
		})
	}
}
