package matching

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"

	"medibridge-whatsapp/internal/database"
)

func TestNewMatchingEngine(t *testing.T) {
	logger := zaptest.NewLogger(t)
	
	// Create a real database connection for testing
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()
	
	dbWrapper := &database.DB{DB: db}
	
	engine := NewMatchingEngine(logger, dbWrapper)
	
	assert.NotNil(t, engine)
	assert.Equal(t, logger, engine.logger)
	assert.Equal(t, dbWrapper, engine.db)
	
	// Ensure all expectations were met
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCalculateNameSimilarity(t *testing.T) {
	logger := zaptest.NewLogger(t)
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	tests := []struct {
		name     string
		name1    string
		name2    string
		expected float64
	}{
		{"exact_match", "paracetamol", "paracetamol", 1.0},
		{"partial_match", "paracetamol 500mg", "paracetamol", 0.6},
		{"no_match", "aspirin", "paracetamol", 0.0},
		{"empty_strings", "", "", 0.0},
		{"one_empty", "paracetamol", "", 0.0},
		{"case_insensitive", "Paracetamol", "paracetamol", 1.0},
		{"with_spaces", "para cetamol", "paracetamol", 0.8},
		{"arabic_text", "باراسيتامول", "باراسيتامول", 1.0},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			similarity := engine.calculateNameSimilarity(test.name1, test.name2)
			assert.InDelta(t, test.expected, similarity, 0.1, 
				"Expected similarity %.2f, got %.2f for '%s' vs '%s'", 
				test.expected, similarity, test.name1, test.name2)
		})
	}
	
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCalculateTimeRelevance(t *testing.T) {
	logger := zaptest.NewLogger(t)
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	tests := []struct {
		name           string
		timeDiffSecs   int64
		expectedMin    float64
		expectedMax    float64
	}{
		{"same_time", 0, 1.0, 1.0},
		{"one_hour", 3600, 0.9, 1.0},
		{"one_day", 86400, 0.8, 1.0},
		{"one_week", 604800, 0.4, 0.6},
		{"one_month", 2592000, 0.0, 0.2},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			relevance := engine.calculateTimeRelevance(test.timeDiffSecs)
			assert.GreaterOrEqual(t, relevance, test.expectedMin)
			assert.LessOrEqual(t, relevance, test.expectedMax)
		})
	}
	
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestCalculateConfidenceBonus(t *testing.T) {
	logger := zaptest.NewLogger(t)
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	tests := []struct {
		name        string
		reqConf     float64
		offerConf   float64
		expectedMin float64
		expectedMax float64
	}{
		{"high_confidence", 0.9, 0.9, 0.1, 0.3},
		{"medium_confidence", 0.7, 0.7, 0.05, 0.2},
		{"low_confidence", 0.5, 0.5, 0.0, 0.1},
		{"mixed_confidence", 0.9, 0.5, 0.05, 0.2},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			bonus := engine.calculateConfidenceBonus(test.reqConf, test.offerConf)
			assert.GreaterOrEqual(t, bonus, test.expectedMin)
			assert.LessOrEqual(t, bonus, test.expectedMax)
		})
	}
	
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMedicinesMatch(t *testing.T) {
	logger := zaptest.NewLogger(t)
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	tests := []struct {
		name     string
		req      MessageMedicine
		offer    MessageMedicine
		expected bool
	}{
		{
			name: "exact_id_match",
			req:  MessageMedicine{MedicineID: 1, MedicineName: "paracetamol"},
			offer: MessageMedicine{MedicineID: 1, MedicineName: "paracetamol"},
			expected: true,
		},
		{
			name: "high_similarity_match",
			req:  MessageMedicine{MedicineID: 1, MedicineName: "paracetamol"},
			offer: MessageMedicine{MedicineID: 2, MedicineName: "paracetamol 500mg"},
			expected: true, // High similarity should match
		},
		{
			name: "low_similarity_no_match",
			req:  MessageMedicine{MedicineID: 1, MedicineName: "aspirin"},
			offer: MessageMedicine{MedicineID: 2, MedicineName: "paracetamol"},
			expected: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := engine.medicinesMatch(test.req, test.offer)
			assert.Equal(t, test.expected, result)
		})
	}
	
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestGetUnprocessedRequests(t *testing.T) {
	logger := zaptest.NewLogger(t)
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	// Mock the database query
	rows := sqlmock.NewRows([]string{"id", "whatsapp_message_id", "account_id", "group_jid", "group_name", 
		"sender_jid", "sender_name", "content", "message_type", "processing_status", "ai_analysis", 
		"error_message", "message_timestamp", "received_at", "processed_at", "created_at", "updated_at"}).
		AddRow(1, "msg1", "acc1", "group1", "Test Group", "sender1", "Test Sender", 
			"أحتاج باراسيتامول", "request", "processed", `{"medicines":[{"name":"paracetamol"}]}`, 
			nil, time.Now(), time.Now(), nil, time.Now(), time.Now())
	
	mock.ExpectQuery("SELECT (.+) FROM messages").WillReturnRows(rows)
	
	ctx := context.Background()
	requests, err := engine.getUnprocessedRequests(ctx, 10)
	
	assert.NoError(t, err)
	assert.Len(t, requests, 1)
	assert.Equal(t, "أحتاج باراسيتامول", requests[0].Content)
	
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestGetAvailableOffers(t *testing.T) {
	logger := zaptest.NewLogger(t)
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	// Mock the database query
	rows := sqlmock.NewRows([]string{"id", "whatsapp_message_id", "account_id", "group_jid", "group_name", 
		"sender_jid", "sender_name", "content", "message_type", "processing_status", "ai_analysis", 
		"error_message", "message_timestamp", "received_at", "processed_at", "created_at", "updated_at"}).
		AddRow(2, "msg2", "acc2", "group2", "Test Group 2", "sender2", "Test Sender 2", 
			"متوفر باراسيتامول", "offer", "processed", `{"medicines":[{"name":"paracetamol"}]}`, 
			nil, time.Now(), time.Now(), nil, time.Now(), time.Now())
	
	mock.ExpectQuery("SELECT (.+) FROM messages").WillReturnRows(rows)
	
	ctx := context.Background()
	offers, err := engine.getAvailableOffers(ctx, time.Now().Add(-24*time.Hour), 10)
	
	assert.NoError(t, err)
	assert.Len(t, offers, 1)
	assert.Equal(t, "متوفر باراسيتامول", offers[0].Content)
	
	assert.NoError(t, mock.ExpectationsWereMet())
}

// Benchmark tests
func BenchmarkCalculateNameSimilarity(b *testing.B) {
	logger := zaptest.NewLogger(b)
	db, _, _ := sqlmock.New()
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	name1 := "paracetamol 500mg tablets"
	name2 := "paracetamol extra strength"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.calculateNameSimilarity(name1, name2)
	}
}

func BenchmarkCalculateTimeRelevance(b *testing.B) {
	logger := zaptest.NewLogger(b)
	db, _, _ := sqlmock.New()
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.calculateTimeRelevance(3600) // 1 hour
	}
}

func BenchmarkMedicinesMatch(b *testing.B) {
	logger := zaptest.NewLogger(b)
	db, _, _ := sqlmock.New()
	defer db.Close()
	
	engine := NewMatchingEngine(logger, &database.DB{DB: db})
	
	req := MessageMedicine{MedicineID: 1, MedicineName: "paracetamol"}
	offer := MessageMedicine{MedicineID: 2, MedicineName: "paracetamol 500mg"}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.medicinesMatch(req, offer)
	}
}
