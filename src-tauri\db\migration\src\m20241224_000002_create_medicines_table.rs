use sea_orm_migration::{prelude::*, schema::*};

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(Medicines::Table)
                    .if_not_exists()
                    .col(pk_auto(Medicines::Id))
                    .col(string_uniq(Medicines::Name))
                    .col(text_null(Medicines::AlternativeNames))
                    .col(string_null(Medicines::Category))
                    .col(text_null(Medicines::Description))
                    .col(string_null(Medicines::ActiveIngredient))
                    .col(string_null(Medicines::Manufacturer))
                    .col(string_null(Medicines::DosageForm))
                    .col(string_null(Medicines::Strength))
                    .col(integer(Medicines::MentionCount).default(0))
                    .col(integer(Medicines::RequestCount).default(0))
                    .col(integer(Medicines::OfferCount).default(0))
                    .col(timestamp_with_time_zone(Medicines::CreatedAt).default(Expr::current_timestamp()))
                    .col(timestamp_with_time_zone(Medicines::UpdatedAt).default(Expr::current_timestamp()))
                    .col(timestamp_null(Medicines::LastMentionedAt))
                    .to_owned(),
            )
            .await?;

        // Create index on name for faster searches
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_medicines_name")
                    .table(Medicines::Table)
                    .col(Medicines::Name)
                    .to_owned(),
            )
            .await?;

        // Create index on category for filtering
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_medicines_category")
                    .table(Medicines::Table)
                    .col(Medicines::Category)
                    .to_owned(),
            )
            .await?;

        // Create index on mention_count for popular medicines
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_medicines_mention_count")
                    .table(Medicines::Table)
                    .col(Medicines::MentionCount)
                    .to_owned(),
            )
            .await?;

        // Create index on last_mentioned_at for recent activity
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_medicines_last_mentioned_at")
                    .table(Medicines::Table)
                    .col(Medicines::LastMentionedAt)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Medicines::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum Medicines {
    Table,
    Id,
    Name,
    AlternativeNames,
    Category,
    Description,
    ActiveIngredient,
    Manufacturer,
    DosageForm,
    Strength,
    MentionCount,
    RequestCount,
    OfferCount,
    CreatedAt,
    UpdatedAt,
    LastMentionedAt,
}
