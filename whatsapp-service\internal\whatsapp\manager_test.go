package whatsapp

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
	"go.uber.org/zap/zaptest"

	"medibridge-whatsapp/internal/database"
)

// MockDB for testing
type MockDB struct {
	mock.Mock
}

func (m *MockDB) Query(query string, args ...interface{}) (*database.Rows, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Rows), mockArgs.Error(1)
}

func (m *MockDB) QueryRow(query string, args ...interface{}) *database.Row {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Row)
}

func (m *MockDB) Exec(query string, args ...interface{}) (database.Result, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(database.Result), mockArgs.Error(1)
}

// MockWhatsAppClient for testing
type MockWhatsAppClient struct {
	mock.Mock
	connected bool
}

func (m *MockWhatsAppClient) Connect() error {
	args := m.Called()
	if args.Error(0) == nil {
		m.connected = true
	}
	return args.Error(0)
}

func (m *MockWhatsAppClient) Disconnect() {
	m.Called()
	m.connected = false
}

func (m *MockWhatsAppClient) IsConnected() bool {
	args := m.Called()
	return args.Bool(0)
}

func (m *MockWhatsAppClient) GetQR() (string, error) {
	args := m.Called()
	return args.String(0), args.Error(1)
}

func (m *MockWhatsAppClient) GetJID() types.JID {
	args := m.Called()
	return args.Get(0).(types.JID)
}

func TestNewManager(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}

	manager := NewManager(logger, mockDB)

	assert.NotNil(t, manager)
	assert.Equal(t, logger, manager.logger)
	assert.Equal(t, mockDB, manager.db)
	assert.NotNil(t, manager.clients)
	assert.NotNil(t, manager.mu)
}

func TestAddClient(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	accountID := "test-account"
	phoneNumber := "+***********0"

	// Mock database operations
	mockDB.On("QueryRow", mock.AnythingOfType("string"), accountID).Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	err := manager.AddClient(accountID, phoneNumber)
	assert.NoError(t, err)

	// Verify client was added
	clients := manager.GetClients()
	assert.Len(t, clients, 1)
	assert.Equal(t, accountID, clients[0].AccountID)
	assert.Equal(t, phoneNumber, clients[0].PhoneNumber)

	mockDB.AssertExpectations(t)
}

func TestConnectClient(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	accountID := "test-account"
	phoneNumber := "+***********0"

	// Add client first
	mockDB.On("QueryRow", mock.AnythingOfType("string"), accountID).Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil).Times(2)

	err := manager.AddClient(accountID, phoneNumber)
	assert.NoError(t, err)

	// Mock successful connection
	err = manager.ConnectClient(accountID)
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestConnectClientNotFound(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	err := manager.ConnectClient("non-existent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "client not found")
}

func TestDisconnectClient(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	accountID := "test-account"
	phoneNumber := "+***********0"

	// Add and connect client first
	mockDB.On("QueryRow", mock.AnythingOfType("string"), accountID).Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil).Times(3)

	err := manager.AddClient(accountID, phoneNumber)
	assert.NoError(t, err)

	err = manager.ConnectClient(accountID)
	assert.NoError(t, err)

	// Test disconnect
	err = manager.DisconnectClient(accountID)
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestGetQRCode(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	accountID := "test-account"
	phoneNumber := "+***********0"

	// Add client first
	mockDB.On("QueryRow", mock.AnythingOfType("string"), accountID).Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	err := manager.AddClient(accountID, phoneNumber)
	assert.NoError(t, err)

	// Test getting QR code (will fail since we don't have real WhatsApp client)
	qr, err := manager.GetQRCode(accountID)
	assert.Error(t, err) // Expected to fail without real client
	assert.Empty(t, qr)

	mockDB.AssertExpectations(t)
}

func TestRemoveClient(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	accountID := "test-account"
	phoneNumber := "+***********0"

	// Add client first
	mockDB.On("QueryRow", mock.AnythingOfType("string"), accountID).Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil).Times(2)

	err := manager.AddClient(accountID, phoneNumber)
	assert.NoError(t, err)

	// Verify client exists
	clients := manager.GetClients()
	assert.Len(t, clients, 1)

	// Remove client
	err = manager.RemoveClient(accountID)
	assert.NoError(t, err)

	// Verify client was removed
	clients = manager.GetClients()
	assert.Len(t, clients, 0)

	mockDB.AssertExpectations(t)
}

func TestGetClients(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	// Initially no clients
	clients := manager.GetClients()
	assert.Len(t, clients, 0)

	// Add multiple clients
	mockDB.On("QueryRow", mock.AnythingOfType("string"), "account1").Return(&database.Row{})
	mockDB.On("QueryRow", mock.AnythingOfType("string"), "account2").Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil).Times(2)

	err := manager.AddClient("account1", "+***********0")
	assert.NoError(t, err)

	err = manager.AddClient("account2", "+***********1")
	assert.NoError(t, err)

	// Verify both clients exist
	clients = manager.GetClients()
	assert.Len(t, clients, 2)

	mockDB.AssertExpectations(t)
}

func TestShutdown(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	// Add some clients
	mockDB.On("QueryRow", mock.AnythingOfType("string"), "account1").Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	err := manager.AddClient("account1", "+***********0")
	assert.NoError(t, err)

	// Test shutdown
	manager.Shutdown()

	// Verify all clients are disconnected
	clients := manager.GetClients()
	for _, client := range clients {
		assert.Equal(t, "disconnected", client.Status)
	}

	mockDB.AssertExpectations(t)
}

func TestHandleMessage(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	// Mock message storage
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	// Create a test message
	message := &whatsmeow.Message{
		Info: types.MessageInfo{
			ID:        "test-message-id",
			Timestamp: time.Now(),
			Chat:      types.JID{User: "group", Server: "g.us"},
			Sender:    types.JID{User: "sender", Server: "s.whatsapp.net"},
		},
	}

	err := manager.handleMessage("test-account", message)
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestStoreMessage(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	// Mock successful message storage
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	messageInfo := types.MessageInfo{
		ID:        "test-message-id",
		Timestamp: time.Now(),
		Chat:      types.JID{User: "group", Server: "g.us"},
		Sender:    types.JID{User: "sender", Server: "s.whatsapp.net"},
	}

	err := manager.storeMessage("test-account", messageInfo, "Test message content")
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

func TestUpdateClientStatus(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	// Mock database update
	mockDB.On("Exec", mock.AnythingOfType("string"), "connected", mock.AnythingOfType("time.Time"), "test-account").Return(&MockResult{}, nil)

	err := manager.updateClientStatus("test-account", "connected")
	assert.NoError(t, err)

	mockDB.AssertExpectations(t)
}

// MockResult implements database.Result interface
type MockResult struct {
	mock.Mock
}

func (m *MockResult) RowsAffected() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockResult) LastInsertId() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

// Test concurrent operations
func TestConcurrentClientOperations(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	// Mock database operations
	mockDB.On("QueryRow", mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	// Add multiple clients concurrently
	done := make(chan bool, 10)
	for i := 0; i < 10; i++ {
		go func(id int) {
			accountID := fmt.Sprintf("account-%d", id)
			phoneNumber := fmt.Sprintf("+***********%d", id)
			err := manager.AddClient(accountID, phoneNumber)
			assert.NoError(t, err)
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	// Verify all clients were added
	clients := manager.GetClients()
	assert.Len(t, clients, 10)

	mockDB.AssertExpectations(t)
}

// Benchmark tests
func BenchmarkAddClient(b *testing.B) {
	logger := zaptest.NewLogger(b)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	// Mock database operations
	mockDB.On("QueryRow", mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		accountID := fmt.Sprintf("account-%d", i)
		phoneNumber := fmt.Sprintf("+***********%d", i%10)
		manager.AddClient(accountID, phoneNumber)
	}
}

func BenchmarkGetClients(b *testing.B) {
	logger := zaptest.NewLogger(b)
	mockDB := &MockDB{}
	manager := NewManager(logger, mockDB)

	// Add some clients first
	mockDB.On("QueryRow", mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(&database.Row{})
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&MockResult{}, nil)

	for i := 0; i < 100; i++ {
		accountID := fmt.Sprintf("account-%d", i)
		phoneNumber := fmt.Sprintf("+***********%d", i%10)
		manager.AddClient(accountID, phoneNumber)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager.GetClients()
	}
}
