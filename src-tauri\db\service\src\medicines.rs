use std::sync::Arc;

use db_entity::{medicines, medicines::Entity as Medicines};
use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, EntityTrait, PaginatorTrait, QueryFilter,
    QueryOrder, QuerySelect, Set,
};
use serde::{Deserialize, Serialize};

use crate::{PaginationParams, PaginationResult, ServiceError};

/// Medicine service for managing medicine data
#[derive(Clone)]
pub struct MedicineService {
    db: Arc<DatabaseConnection>,
}

impl MedicineService {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    /// Create a new medicine or get existing one by name
    pub async fn create_or_get_medicine(
        &self,
        name: String,
        category: Option<String>,
        description: Option<String>,
    ) -> Result<medicines::Model, ServiceError> {
        // First try to find existing medicine
        if let Some(existing) = self.get_medicine_by_name(&name).await? {
            return Ok(existing);
        }

        // Create new medicine
        let medicine = medicines::ActiveModel {
            name: Set(name),
            category: Set(category),
            description: Set(description),
            mention_count: Set(1),
            request_count: Set(0),
            offer_count: Set(0),
            created_at: Set(chrono::Utc::now().naive_utc()),
            updated_at: Set(chrono::Utc::now().naive_utc()),
            last_mentioned_at: Set(Some(chrono::Utc::now().naive_utc())),
            ..Default::default()
        };

        let result = medicine.insert(&*self.db).await?;
        Ok(result)
    }

    /// Get medicine by ID
    pub async fn get_medicine(&self, id: i32) -> Result<Option<medicines::Model>, ServiceError> {
        let medicine = Medicines::find_by_id(id).one(&*self.db).await?;
        Ok(medicine)
    }

    /// Get medicine by name
    pub async fn get_medicine_by_name(
        &self,
        name: &str,
    ) -> Result<Option<medicines::Model>, ServiceError> {
        let medicine = Medicines::find()
            .filter(medicines::Column::Name.eq(name))
            .one(&*self.db)
            .await?;
        Ok(medicine)
    }

    /// Search medicines by name (fuzzy search)
    pub async fn search_medicines(
        &self,
        query: &str,
        pagination: PaginationParams,
    ) -> Result<PaginationResult<Vec<medicines::Model>>, ServiceError> {
        let paginator = Medicines::find()
            .filter(medicines::Column::Name.contains(query))
            .order_by_desc(medicines::Column::MentionCount)
            .paginate(&*self.db, pagination.page_size);

        let total = paginator.num_items().await?;
        let medicines = paginator.fetch_page(pagination.page - 1).await?;

        Ok(PaginationResult {
            data: medicines,
            total,
            page: pagination.page,
            page_size: pagination.page_size,
        })
    }

    /// Get all medicines with pagination
    pub async fn get_medicines(
        &self,
        pagination: PaginationParams,
    ) -> Result<PaginationResult<Vec<medicines::Model>>, ServiceError> {
        let paginator = Medicines::find()
            .order_by_desc(medicines::Column::MentionCount)
            .paginate(&*self.db, pagination.page_size);

        let total = paginator.num_items().await?;
        let medicines = paginator.fetch_page(pagination.page - 1).await?;

        Ok(PaginationResult {
            data: medicines,
            total,
            page: pagination.page,
            page_size: pagination.page_size,
        })
    }

    /// Get medicines by category
    pub async fn get_medicines_by_category(
        &self,
        category: &str,
        pagination: PaginationParams,
    ) -> Result<PaginationResult<Vec<medicines::Model>>, ServiceError> {
        let paginator = Medicines::find()
            .filter(medicines::Column::Category.eq(category))
            .order_by_desc(medicines::Column::MentionCount)
            .paginate(&*self.db, pagination.page_size);

        let total = paginator.num_items().await?;
        let medicines = paginator.fetch_page(pagination.page - 1).await?;

        Ok(PaginationResult {
            data: medicines,
            total,
            page: pagination.page,
            page_size: pagination.page_size,
        })
    }

    /// Get most mentioned medicines
    pub async fn get_popular_medicines(
        &self,
        limit: u64,
    ) -> Result<Vec<medicines::Model>, ServiceError> {
        let medicines = Medicines::find()
            .order_by_desc(medicines::Column::MentionCount)
            .limit(limit)
            .all(&*self.db)
            .await?;

        Ok(medicines)
    }

    /// Get most requested medicines
    pub async fn get_most_requested_medicines(
        &self,
        limit: u64,
    ) -> Result<Vec<medicines::Model>, ServiceError> {
        let medicines = Medicines::find()
            .order_by_desc(medicines::Column::RequestCount)
            .limit(limit)
            .all(&*self.db)
            .await?;

        Ok(medicines)
    }

    /// Get most offered medicines
    pub async fn get_most_offered_medicines(
        &self,
        limit: u64,
    ) -> Result<Vec<medicines::Model>, ServiceError> {
        let medicines = Medicines::find()
            .order_by_desc(medicines::Column::OfferCount)
            .limit(limit)
            .all(&*self.db)
            .await?;

        Ok(medicines)
    }

    /// Update medicine mention count
    pub async fn increment_mention_count(
        &self,
        id: i32,
        is_request: bool,
    ) -> Result<medicines::Model, ServiceError> {
        let medicine = Medicines::find_by_id(id)
            .one(&*self.db)
            .await?
            .ok_or(ServiceError::NotFound("Medicine not found".to_string()))?;

        let mut medicine: medicines::ActiveModel = medicine.into();
        medicine.mention_count = Set(medicine.mention_count.unwrap() + 1);
        medicine.last_mentioned_at = Set(Some(chrono::Utc::now().naive_utc()));
        medicine.updated_at = Set(chrono::Utc::now().naive_utc());

        if is_request {
            medicine.request_count = Set(medicine.request_count.unwrap() + 1);
        } else {
            medicine.offer_count = Set(medicine.offer_count.unwrap() + 1);
        }

        let result = medicine.update(&*self.db).await?;
        Ok(result)
    }

    /// Update medicine details
    pub async fn update_medicine(
        &self,
        id: i32,
        update_data: UpdateMedicineDto,
    ) -> Result<medicines::Model, ServiceError> {
        let medicine = Medicines::find_by_id(id)
            .one(&*self.db)
            .await?
            .ok_or(ServiceError::NotFound("Medicine not found".to_string()))?;

        let mut medicine: medicines::ActiveModel = medicine.into();

        if let Some(category) = update_data.category {
            medicine.category = Set(Some(category));
        }
        if let Some(description) = update_data.description {
            medicine.description = Set(Some(description));
        }
        if let Some(active_ingredient) = update_data.active_ingredient {
            medicine.active_ingredient = Set(Some(active_ingredient));
        }
        if let Some(manufacturer) = update_data.manufacturer {
            medicine.manufacturer = Set(Some(manufacturer));
        }
        if let Some(dosage_form) = update_data.dosage_form {
            medicine.dosage_form = Set(Some(dosage_form));
        }
        if let Some(strength) = update_data.strength {
            medicine.strength = Set(Some(strength));
        }
        if let Some(alternative_names) = update_data.alternative_names {
            medicine.alternative_names = Set(Some(alternative_names));
        }

        medicine.updated_at = Set(chrono::Utc::now().naive_utc());

        let result = medicine.update(&*self.db).await?;
        Ok(result)
    }
}

/// DTO for updating medicine details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateMedicineDto {
    pub category: Option<String>,
    pub description: Option<String>,
    pub active_ingredient: Option<String>,
    pub manufacturer: Option<String>,
    pub dosage_form: Option<String>,
    pub strength: Option<String>,
    pub alternative_names: Option<String>,
}

/// DTO for creating a new medicine
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateMedicineDto {
    pub name: String,
    pub category: Option<String>,
    pub description: Option<String>,
    pub active_ingredient: Option<String>,
    pub manufacturer: Option<String>,
    pub dosage_form: Option<String>,
    pub strength: Option<String>,
    pub alternative_names: Option<String>,
}
