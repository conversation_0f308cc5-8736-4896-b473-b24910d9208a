# MediBridge WhatsApp Service

خدمة Go منفصلة لإدارة اتصالات WhatsApp ومعالجة الرسائل لتطبيق MediBridge.

## المميزات

- إدارة حسابات WhatsApp متعددة
- مراقبة الرسائل في الجروبات
- معالجة الرسائل تلقائياً
- API RESTful للتواصل مع التطبيق الرئيسي
- دعم إعادة الاتصال التلقائي
- تسجيل مفصل للأحداث

## المتطلبات

- Go 1.21 أو أحدث
- PostgreSQL
- Ollama (للذكاء الصناعي)

## التثبيت

1. استنساخ المشروع:
```bash
git clone <repository-url>
cd whatsapp-service
```

2. تثبيت التبعيات:
```bash
go mod download
```

3. إعداد متغيرات البيئة:
```bash
cp .env.example .env
# قم بتعديل .env حسب إعداداتك
```

4. تشغيل الخدمة:
```bash
go run main.go
```

## API Endpoints

### Health Check
- `GET /health` - فحص حالة الخدمة

### إدارة الحسابات
- `GET /api/v1/accounts` - جلب جميع الحسابات
- `POST /api/v1/accounts` - إنشاء حساب جديد
- `GET /api/v1/accounts/:id` - جلب حساب محدد
- `PUT /api/v1/accounts/:id/status` - تحديث حالة الحساب
- `DELETE /api/v1/accounts/:id` - حذف حساب

### إدارة العملاء
- `GET /api/v1/clients` - جلب جميع العملاء المتصلين
- `POST /api/v1/clients/:phone/connect` - اتصال عميل
- `POST /api/v1/clients/:phone/disconnect` - قطع اتصال عميل
- `GET /api/v1/clients/:phone/qr` - جلب QR code للربط

### إدارة الرسائل
- `GET /api/v1/messages` - جلب الرسائل مع التصفح
- `GET /api/v1/messages/:id` - جلب رسالة محددة
- `POST /api/v1/messages/:id/reprocess` - إعادة معالجة رسالة

### الإحصائيات
- `GET /api/v1/stats/overview` - إحصائيات عامة
- `GET /api/v1/stats/messages` - إحصائيات الرسائل
- `GET /api/v1/stats/medicines` - إحصائيات الأدوية

## البنية

```
whatsapp-service/
├── main.go                 # نقطة الدخول الرئيسية
├── internal/
│   ├── api/               # API server
│   │   └── server.go
│   ├── config/            # إدارة التكوين
│   │   └── config.go
│   ├── database/          # طبقة قاعدة البيانات
│   │   └── database.go
│   └── whatsapp/          # إدارة WhatsApp
│       └── manager.go
├── go.mod                 # تبعيات Go
├── .env.example          # مثال على متغيرات البيئة
└── README.md             # هذا الملف
```

## التطوير

### إضافة ميزة جديدة

1. أضف الوظيفة في الطبقة المناسبة
2. أضف endpoint في API server
3. اختبر الوظيفة
4. حدث الوثائق

### تشغيل الاختبارات

```bash
go test ./...
```

### بناء للإنتاج

```bash
go build -o medibridge-whatsapp main.go
```

## الأمان

- تأكد من تشفير بيانات الجلسات
- استخدم HTTPS في الإنتاج
- قم بتحديث التبعيات بانتظام
- راقب السجلات للأنشطة المشبوهة

## المساهمة

1. Fork المشروع
2. أنشئ branch للميزة الجديدة
3. اكتب الكود والاختبارات
4. أرسل Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
