/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as StatisticsImport } from './routes/statistics'
import { Route as SettingsImport } from './routes/settings'
import { Route as MessagesImport } from './routes/messages'
import { Route as MedicinesImport } from './routes/medicines'
import { Route as MatchesImport } from './routes/matches'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as AccountsImport } from './routes/accounts'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const StatisticsRoute = StatisticsImport.update({
  id: '/statistics',
  path: '/statistics',
  getParentRoute: () => rootRoute,
} as any)

const SettingsRoute = SettingsImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRoute,
} as any)

const MessagesRoute = MessagesImport.update({
  id: '/messages',
  path: '/messages',
  getParentRoute: () => rootRoute,
} as any)

const MedicinesRoute = MedicinesImport.update({
  id: '/medicines',
  path: '/medicines',
  getParentRoute: () => rootRoute,
} as any)

const MatchesRoute = MatchesImport.update({
  id: '/matches',
  path: '/matches',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AccountsRoute = AccountsImport.update({
  id: '/accounts',
  path: '/accounts',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/accounts': {
      id: '/accounts'
      path: '/accounts'
      fullPath: '/accounts'
      preLoaderRoute: typeof AccountsImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/matches': {
      id: '/matches'
      path: '/matches'
      fullPath: '/matches'
      preLoaderRoute: typeof MatchesImport
      parentRoute: typeof rootRoute
    }
    '/medicines': {
      id: '/medicines'
      path: '/medicines'
      fullPath: '/medicines'
      preLoaderRoute: typeof MedicinesImport
      parentRoute: typeof rootRoute
    }
    '/messages': {
      id: '/messages'
      path: '/messages'
      fullPath: '/messages'
      preLoaderRoute: typeof MessagesImport
      parentRoute: typeof rootRoute
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsImport
      parentRoute: typeof rootRoute
    }
    '/statistics': {
      id: '/statistics'
      path: '/statistics'
      fullPath: '/statistics'
      preLoaderRoute: typeof StatisticsImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/accounts': typeof AccountsRoute
  '/dashboard': typeof DashboardRoute
  '/matches': typeof MatchesRoute
  '/medicines': typeof MedicinesRoute
  '/messages': typeof MessagesRoute
  '/settings': typeof SettingsRoute
  '/statistics': typeof StatisticsRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/accounts': typeof AccountsRoute
  '/dashboard': typeof DashboardRoute
  '/matches': typeof MatchesRoute
  '/medicines': typeof MedicinesRoute
  '/messages': typeof MessagesRoute
  '/settings': typeof SettingsRoute
  '/statistics': typeof StatisticsRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/accounts': typeof AccountsRoute
  '/dashboard': typeof DashboardRoute
  '/matches': typeof MatchesRoute
  '/medicines': typeof MedicinesRoute
  '/messages': typeof MessagesRoute
  '/settings': typeof SettingsRoute
  '/statistics': typeof StatisticsRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/accounts'
    | '/dashboard'
    | '/matches'
    | '/medicines'
    | '/messages'
    | '/settings'
    | '/statistics'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/accounts'
    | '/dashboard'
    | '/matches'
    | '/medicines'
    | '/messages'
    | '/settings'
    | '/statistics'
  id:
    | '__root__'
    | '/'
    | '/accounts'
    | '/dashboard'
    | '/matches'
    | '/medicines'
    | '/messages'
    | '/settings'
    | '/statistics'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AccountsRoute: typeof AccountsRoute
  DashboardRoute: typeof DashboardRoute
  MatchesRoute: typeof MatchesRoute
  MedicinesRoute: typeof MedicinesRoute
  MessagesRoute: typeof MessagesRoute
  SettingsRoute: typeof SettingsRoute
  StatisticsRoute: typeof StatisticsRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AccountsRoute: AccountsRoute,
  DashboardRoute: DashboardRoute,
  MatchesRoute: MatchesRoute,
  MedicinesRoute: MedicinesRoute,
  MessagesRoute: MessagesRoute,
  SettingsRoute: SettingsRoute,
  StatisticsRoute: StatisticsRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/accounts",
        "/dashboard",
        "/matches",
        "/medicines",
        "/messages",
        "/settings",
        "/statistics"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/accounts": {
      "filePath": "accounts.tsx"
    },
    "/dashboard": {
      "filePath": "dashboard.tsx"
    },
    "/matches": {
      "filePath": "matches.tsx"
    },
    "/medicines": {
      "filePath": "medicines.tsx"
    },
    "/messages": {
      "filePath": "messages.tsx"
    },
    "/settings": {
      "filePath": "settings.tsx"
    },
    "/statistics": {
      "filePath": "statistics.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
