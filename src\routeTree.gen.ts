/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as MessagesImport } from './routes/messages'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as AccountsImport } from './routes/accounts'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const MessagesRoute = MessagesImport.update({
  id: '/messages',
  path: '/messages',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AccountsRoute = AccountsImport.update({
  id: '/accounts',
  path: '/accounts',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/accounts': {
      id: '/accounts'
      path: '/accounts'
      fullPath: '/accounts'
      preLoaderRoute: typeof AccountsImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/messages': {
      id: '/messages'
      path: '/messages'
      fullPath: '/messages'
      preLoaderRoute: typeof MessagesImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/accounts': typeof AccountsRoute
  '/dashboard': typeof DashboardRoute
  '/messages': typeof MessagesRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/accounts': typeof AccountsRoute
  '/dashboard': typeof DashboardRoute
  '/messages': typeof MessagesRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/accounts': typeof AccountsRoute
  '/dashboard': typeof DashboardRoute
  '/messages': typeof MessagesRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/accounts' | '/dashboard' | '/messages'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/accounts' | '/dashboard' | '/messages'
  id: '__root__' | '/' | '/accounts' | '/dashboard' | '/messages'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AccountsRoute: typeof AccountsRoute
  DashboardRoute: typeof DashboardRoute
  MessagesRoute: typeof MessagesRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AccountsRoute: AccountsRoute,
  DashboardRoute: DashboardRoute,
  MessagesRoute: MessagesRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/accounts",
        "/dashboard",
        "/messages"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/accounts": {
      "filePath": "accounts.tsx"
    },
    "/dashboard": {
      "filePath": "dashboard.tsx"
    },
    "/messages": {
      "filePath": "messages.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
