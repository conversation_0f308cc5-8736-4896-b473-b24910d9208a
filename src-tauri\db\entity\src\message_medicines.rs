use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq, DeriveEntityModel, Serialize, Deserialize)]
#[sea_orm(table_name = "message_medicines")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,

    /// Message ID
    pub message_id: i32,

    /// Medicine ID
    pub medicine_id: i32,

    /// Confidence score from AI analysis (0.0 to 1.0)
    pub confidence_score: f32,

    /// Quantity mentioned (if any)
    pub quantity: Option<String>,

    /// Unit mentioned (box, strip, piece, etc.)
    pub unit: Option<String>,

    /// Price mentioned (if any)
    pub price: Option<String>,

    /// Currency (EGP, USD, etc.)
    pub currency: Option<String>,

    /// Additional notes from AI analysis
    pub notes: Option<String>,

    /// Position in the message where medicine was found
    pub position_start: Option<i32>,
    pub position_end: Option<i32>,

    /// Record creation timestamp
    pub created_at: DateTime,

    /// Last update timestamp
    pub updated_at: DateTime,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, En<PERSON><PERSON><PERSON>, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::messages::Entity",
        from = "Column::MessageId",
        to = "super::messages::Column::Id"
    )]
    Message,

    #[sea_orm(
        belongs_to = "super::medicines::Entity",
        from = "Column::MedicineId",
        to = "super::medicines::Column::Id"
    )]
    Medicine,
}

impl Related<super::messages::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Message.def()
    }
}

impl Related<super::medicines::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Medicine.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// Unit enum for common medicine units
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MedicineUnit {
    Box,     // علبة
    Strip,   // شريط
    Piece,   // قرص/حبة
    Bottle,  // زجاجة
    Tube,    // أنبوب
    Vial,    // فيال
    Ampoule, // أمبولة
    Sachet,  // كيس
    Other(String),
}

impl std::fmt::Display for MedicineUnit {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MedicineUnit::Box => write!(f, "box"),
            MedicineUnit::Strip => write!(f, "strip"),
            MedicineUnit::Piece => write!(f, "piece"),
            MedicineUnit::Bottle => write!(f, "bottle"),
            MedicineUnit::Tube => write!(f, "tube"),
            MedicineUnit::Vial => write!(f, "vial"),
            MedicineUnit::Ampoule => write!(f, "ampoule"),
            MedicineUnit::Sachet => write!(f, "sachet"),
            MedicineUnit::Other(s) => write!(f, "{}", s),
        }
    }
}

impl From<String> for MedicineUnit {
    fn from(s: String) -> Self {
        match s.as_str() {
            "box" | "علبة" | "علب" => MedicineUnit::Box,
            "strip" | "شريط" | "شرايط" => MedicineUnit::Strip,
            "piece" | "قرص" | "حبة" | "أقراص" | "حبوب" => MedicineUnit::Piece,
            "bottle" | "زجاجة" | "زجاجات" => MedicineUnit::Bottle,
            "tube" | "أنبوب" | "أنابيب" => MedicineUnit::Tube,
            "vial" | "فيال" | "فيالات" => MedicineUnit::Vial,
            "ampoule" | "أمبولة" | "أمبولات" => MedicineUnit::Ampoule,
            "sachet" | "كيس" | "أكياس" => MedicineUnit::Sachet,
            _ => MedicineUnit::Other(s),
        }
    }
}

// Currency enum
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Currency {
    EGP, // Egyptian Pound
    USD, // US Dollar
    EUR, // Euro
    Other(String),
}

impl std::fmt::Display for Currency {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Currency::EGP => write!(f, "EGP"),
            Currency::USD => write!(f, "USD"),
            Currency::EUR => write!(f, "EUR"),
            Currency::Other(s) => write!(f, "{}", s),
        }
    }
}

impl From<String> for Currency {
    fn from(s: String) -> Self {
        match s.to_uppercase().as_str() {
            "EGP" | "جنيه" | "جنية" | "ج.م" => Currency::EGP,
            "USD" | "دولار" | "$" => Currency::USD,
            "EUR" | "يورو" | "€" => Currency::EUR,
            _ => Currency::Other(s),
        }
    }
}
