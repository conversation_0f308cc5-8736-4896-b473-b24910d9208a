{"rustc": 16591470773350601817, "features": "[\"alloc\", \"fs\", \"libc-extra-traits\", \"net\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 15904350142235955753, "path": 12852764465068927780, "deps": [[3430646239657634944, "build_script_build", false, 8706831761404542382], [7896293946984509699, "bitflags", false, 11798274180455121489], [10281541584571964250, "windows_sys", false, 10712915432557800419], [14633813869673313769, "libc_errno", false, 10494767302728047828]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustix-5530b3d03ecce3b6\\dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}