import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { invoke } from '@tauri-apps/api/core'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { 
  Activity, 
  MessageSquare, 
  Pill, 
  Users, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react'

interface StatsOverview {
  total_accounts: number
  active_accounts: number
  total_messages: number
  processed_messages: number
  pending_messages: number
  total_medicines: number
  total_matches: number
}

interface MessageStats {
  daily_messages: Array<{ date: string; count: number }>
  message_types: {
    request: number
    offer: number
    unknown: number
  }
  processing_stats: {
    pending: number
    processed: number
    failed: number
  }
}

interface Client {
  id: number
  phone_number: string
  display_name: string
  connected: boolean
  last_seen: string
}

export const Route = createFileRoute('/dashboard')({
  component: DashboardPage,
})

function DashboardPage() {
  const [stats, setStats] = useState<StatsOverview | null>(null)
  const [messageStats, setMessageStats] = useState<MessageStats | null>(null)
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [serviceHealth, setServiceHealth] = useState<boolean | null>(null)

  useEffect(() => {
    loadDashboardData()
    const interval = setInterval(loadDashboardData, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const [statsData, messageStatsData, clientsData, healthStatus] = await Promise.all([
        invoke<StatsOverview>('get_stats_overview'),
        invoke<MessageStats>('get_message_stats'),
        invoke<Client[]>('get_whatsapp_clients'),
        invoke<boolean>('check_whatsapp_health')
      ])
      
      setStats(statsData)
      setMessageStats(messageStatsData)
      setClients(clientsData)
      setServiceHealth(healthStatus)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      toast.error('فشل في تحميل بيانات لوحة التحكم')
      setServiceHealth(false)
    } finally {
      setLoading(false)
    }
  }

  const getServiceStatusBadge = () => {
    if (serviceHealth === null) {
      return <Badge variant="outline">جاري التحقق...</Badge>
    }
    return serviceHealth ? (
      <Badge variant="default" className="bg-green-500">
        <CheckCircle className="w-3 h-3 mr-1" />
        متصل
      </Badge>
    ) : (
      <Badge variant="destructive">
        <AlertCircle className="w-3 h-3 mr-1" />
        غير متصل
      </Badge>
    )
  }

  const connectedClients = clients.filter(client => client.connected).length
  const totalClients = clients.length

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">لوحة التحكم</h1>
          <p className="text-muted-foreground">نظرة عامة على نشاط MediBridge</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {getServiceStatusBadge()}
          <Button onClick={loadDashboardData} disabled={loading}>
            <Activity className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </div>
      </div>

      {/* Service Status Alert */}
      {serviceHealth === false && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-destructive">
              <AlertCircle className="w-5 h-5" />
              <div>
                <h3 className="font-semibold">خدمة WhatsApp غير متاحة</h3>
                <p className="text-sm">تأكد من تشغيل خدمة WhatsApp Backend على المنفذ 8080</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الحسابات</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_accounts || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.active_accounts || 0} نشط من أصل {stats?.total_accounts || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الرسائل</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_messages || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.processed_messages || 0} تم معالجتها
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأدوية المكتشفة</CardTitle>
            <Pill className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_medicines || 0}</div>
            <p className="text-xs text-muted-foreground">
              أدوية مختلفة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المطابقات</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_matches || 0}</div>
            <p className="text-xs text-muted-foreground">
              مطابقات محتملة
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Message Processing Stats */}
        <Card>
          <CardHeader>
            <CardTitle>حالة معالجة الرسائل</CardTitle>
            <CardDescription>توزيع الرسائل حسب حالة المعالجة</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {messageStats && (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>تم المعالجة</span>
                  </div>
                  <Badge variant="default">{messageStats.processing_stats.processed}</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-yellow-500" />
                    <span>في الانتظار</span>
                  </div>
                  <Badge variant="outline">{messageStats.processing_stats.pending}</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <span>فشل</span>
                  </div>
                  <Badge variant="destructive">{messageStats.processing_stats.failed}</Badge>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Message Types */}
        <Card>
          <CardHeader>
            <CardTitle>أنواع الرسائل</CardTitle>
            <CardDescription>توزيع الرسائل حسب النوع</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {messageStats && (
              <>
                <div className="flex items-center justify-between">
                  <span>طلبات</span>
                  <Badge variant="default">{messageStats.message_types.request}</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span>عروض</span>
                  <Badge variant="secondary">{messageStats.message_types.offer}</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span>غير محدد</span>
                  <Badge variant="outline">{messageStats.message_types.unknown}</Badge>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Connected Clients */}
        <Card>
          <CardHeader>
            <CardTitle>حالة الاتصالات</CardTitle>
            <CardDescription>حسابات WhatsApp المتصلة</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>الحسابات المتصلة</span>
                <Badge variant="default">{connectedClients} / {totalClients}</Badge>
              </div>
              
              {clients.slice(0, 3).map((client) => (
                <div key={client.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    {client.connected ? (
                      <Wifi className="w-3 h-3 text-green-500" />
                    ) : (
                      <WifiOff className="w-3 h-3 text-gray-400" />
                    )}
                    <span>{client.display_name}</span>
                  </div>
                  <span className="text-muted-foreground">{client.phone_number}</span>
                </div>
              ))}
              
              {clients.length > 3 && (
                <p className="text-xs text-muted-foreground">
                  و {clients.length - 3} حسابات أخرى...
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>النشاط الأخير</CardTitle>
            <CardDescription>آخر الأحداث في النظام</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>تم معالجة {messageStats?.processing_stats.processed || 0} رسالة</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>اكتشاف {stats?.total_medicines || 0} دواء مختلف</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>إنشاء {stats?.total_matches || 0} مطابقة محتملة</span>
              </div>
              
              {serviceHealth && (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>خدمة WhatsApp متصلة</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
