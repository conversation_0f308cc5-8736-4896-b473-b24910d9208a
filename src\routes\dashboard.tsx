import { createFileRoute } from '@tanstack/react-router';
import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { AnalyticsDashboard } from '@/components/analytics/PerformanceChart';
import { AlertCenter, useAlerts } from '@/components/notifications/AlertCenter';
import {
  Activity,
  MessageSquare,
  Pill,
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
} from 'lucide-react';

interface StatsOverview {
  total_accounts: number;
  active_accounts: number;
  total_messages: number;
  processed_messages: number;
  pending_messages: number;
  total_medicines: number;
  total_matches: number;
}

interface MessageStats {
  daily_messages: Array<{ date: string; count: number }>;
  message_types: {
    request: number;
    offer: number;
    unknown: number;
  };
  processing_stats: {
    pending: number;
    processed: number;
    failed: number;
  };
}

interface Client {
  id: number;
  phone_number: string;
  display_name: string;
  connected: boolean;
  last_seen: string;
}

export const Route = createFileRoute('/dashboard')({
  component: DashboardPage,
});

function DashboardPage() {
  const [stats, setStats] = useState<StatsOverview | null>(null);
  const [messageStats, setMessageStats] = useState<MessageStats | null>(null);
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const { alerts, generateSampleAlerts, markAsRead, dismissAlert, clearAll } =
    useAlerts();
  const [serviceHealth, setServiceHealth] = useState<boolean | null>(null);

  useEffect(() => {
    loadDashboardData();
    generateSampleAlerts(); // Load sample alerts for demo
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [statsData, messageStatsData, clientsData, healthStatus] =
        await Promise.all([
          invoke<StatsOverview>('get_stats_overview'),
          invoke<MessageStats>('get_message_stats'),
          invoke<Client[]>('get_whatsapp_clients'),
          invoke<boolean>('check_whatsapp_health'),
        ]);

      setStats(statsData);
      setMessageStats(messageStatsData);
      setClients(clientsData);
      setServiceHealth(healthStatus);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast.error('فشل في تحميل بيانات لوحة التحكم');
      setServiceHealth(false);
    } finally {
      setLoading(false);
    }
  };

  const getServiceStatusBadge = () => {
    if (serviceHealth === null) {
      return <Badge variant="outline">جاري التحقق...</Badge>;
    }
    return serviceHealth ? (
      <Badge variant="default" className="bg-green-500">
        <CheckCircle className="mr-1 h-3 w-3" />
        متصل
      </Badge>
    ) : (
      <Badge variant="destructive">
        <AlertCircle className="mr-1 h-3 w-3" />
        غير متصل
      </Badge>
    );
  };

  const connectedClients = clients.filter(client => client.connected).length;
  const totalClients = clients.length;

  if (loading && !stats) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg">جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">لوحة التحكم</h1>
          <p className="text-muted-foreground">نظرة عامة على نشاط MediBridge</p>
        </div>

        <div className="flex items-center space-x-4">
          {getServiceStatusBadge()}
          <Button onClick={loadDashboardData} disabled={loading}>
            <Activity
              className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`}
            />
            تحديث
          </Button>
        </div>
      </div>

      {/* Service Status Alert */}
      {serviceHealth === false && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive flex items-center space-x-2">
              <AlertCircle className="h-5 w-5" />
              <div>
                <h3 className="font-semibold">خدمة WhatsApp غير متاحة</h3>
                <p className="text-sm">
                  تأكد من تشغيل خدمة WhatsApp Backend على المنفذ 8080
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي الحسابات
            </CardTitle>
            <Users className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.total_accounts || 0}
            </div>
            <p className="text-muted-foreground text-xs">
              {stats?.active_accounts || 0} نشط من أصل{' '}
              {stats?.total_accounts || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي الرسائل
            </CardTitle>
            <MessageSquare className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.total_messages || 0}
            </div>
            <p className="text-muted-foreground text-xs">
              {stats?.processed_messages || 0} تم معالجتها
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              الأدوية المكتشفة
            </CardTitle>
            <Pill className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.total_medicines || 0}
            </div>
            <p className="text-muted-foreground text-xs">أدوية مختلفة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المطابقات</CardTitle>
            <TrendingUp className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.total_matches || 0}
            </div>
            <p className="text-muted-foreground text-xs">مطابقات محتملة</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Message Processing Stats */}
        <Card>
          <CardHeader>
            <CardTitle>حالة معالجة الرسائل</CardTitle>
            <CardDescription>توزيع الرسائل حسب حالة المعالجة</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {messageStats && (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>تم المعالجة</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="default">
                      {messageStats.processing_stats.processed}
                    </Badge>
                    <span className="text-muted-foreground text-xs">
                      (
                      {stats
                        ? Math.round(
                            (messageStats.processing_stats.processed /
                              stats.total_messages) *
                              100,
                          )
                        : 0}
                      %)
                    </span>
                  </div>
                </div>

                <div className="h-2 w-full rounded-full bg-gray-200">
                  <div
                    className="h-2 rounded-full bg-green-500"
                    style={{
                      width: `${stats ? (messageStats.processing_stats.processed / stats.total_messages) * 100 : 0}%`,
                    }}
                  ></div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span>في الانتظار</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">
                      {messageStats.processing_stats.pending}
                    </Badge>
                    <span className="text-muted-foreground text-xs">
                      (
                      {stats
                        ? Math.round(
                            (messageStats.processing_stats.pending /
                              stats.total_messages) *
                              100,
                          )
                        : 0}
                      %)
                    </span>
                  </div>
                </div>

                <div className="h-2 w-full rounded-full bg-gray-200">
                  <div
                    className="h-2 rounded-full bg-yellow-500"
                    style={{
                      width: `${stats ? (messageStats.processing_stats.pending / stats.total_messages) * 100 : 0}%`,
                    }}
                  ></div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <span>فشل</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive">
                      {messageStats.processing_stats.failed}
                    </Badge>
                    <span className="text-muted-foreground text-xs">
                      (
                      {stats
                        ? Math.round(
                            (messageStats.processing_stats.failed /
                              stats.total_messages) *
                              100,
                          )
                        : 0}
                      %)
                    </span>
                  </div>
                </div>

                <div className="h-2 w-full rounded-full bg-gray-200">
                  <div
                    className="h-2 rounded-full bg-red-500"
                    style={{
                      width: `${stats ? (messageStats.processing_stats.failed / stats.total_messages) * 100 : 0}%`,
                    }}
                  ></div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Message Types */}
        <Card>
          <CardHeader>
            <CardTitle>أنواع الرسائل</CardTitle>
            <CardDescription>توزيع الرسائل حسب النوع</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {messageStats && (
              <>
                <div className="flex items-center justify-between">
                  <span>طلبات</span>
                  <Badge variant="default">
                    {messageStats.message_types.request}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span>عروض</span>
                  <Badge variant="secondary">
                    {messageStats.message_types.offer}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span>غير محدد</span>
                  <Badge variant="outline">
                    {messageStats.message_types.unknown}
                  </Badge>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Connected Clients */}
        <Card>
          <CardHeader>
            <CardTitle>حالة الاتصالات</CardTitle>
            <CardDescription>حسابات WhatsApp المتصلة</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>الحسابات المتصلة</span>
                <Badge variant="default">
                  {connectedClients} / {totalClients}
                </Badge>
              </div>

              {clients.slice(0, 3).map(client => (
                <div
                  key={client.id}
                  className="flex items-center justify-between text-sm"
                >
                  <div className="flex items-center space-x-2">
                    {client.connected ? (
                      <Wifi className="h-3 w-3 text-green-500" />
                    ) : (
                      <WifiOff className="h-3 w-3 text-gray-400" />
                    )}
                    <span>{client.display_name}</span>
                  </div>
                  <span className="text-muted-foreground">
                    {client.phone_number}
                  </span>
                </div>
              ))}

              {clients.length > 3 && (
                <p className="text-muted-foreground text-xs">
                  و {clients.length - 3} حسابات أخرى...
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>النشاط الأخير</CardTitle>
            <CardDescription>آخر الأحداث في النظام</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between rounded-lg bg-green-50 p-3">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">
                    تم معالجة {messageStats?.processing_stats.processed || 0}{' '}
                    رسالة
                  </span>
                </div>
                <span className="text-muted-foreground text-xs">منذ دقائق</span>
              </div>

              <div className="flex items-center justify-between rounded-lg bg-blue-50 p-3">
                <div className="flex items-center space-x-2">
                  <Pill className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">
                    اكتشاف {stats?.total_medicines || 0} دواء مختلف
                  </span>
                </div>
                <span className="text-muted-foreground text-xs">اليوم</span>
              </div>

              <div className="flex items-center justify-between rounded-lg bg-purple-50 p-3">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-purple-600" />
                  <span className="text-sm">
                    إنشاء {stats?.total_matches || 0} مطابقة محتملة
                  </span>
                </div>
                <span className="text-muted-foreground text-xs">
                  هذا الأسبوع
                </span>
              </div>

              {serviceHealth && (
                <div className="flex items-center justify-between rounded-lg bg-green-50 p-3">
                  <div className="flex items-center space-x-2">
                    <Wifi className="h-4 w-4 text-green-600" />
                    <span className="text-sm">خدمة WhatsApp متصلة</span>
                  </div>
                  <span className="text-muted-foreground text-xs">الآن</span>
                </div>
              )}

              {!serviceHealth && (
                <div className="flex items-center justify-between rounded-lg bg-red-50 p-3">
                  <div className="flex items-center space-x-2">
                    <WifiOff className="h-4 w-4 text-red-600" />
                    <span className="text-sm">خدمة WhatsApp غير متصلة</span>
                  </div>
                  <span className="text-muted-foreground text-xs">
                    منذ قليل
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Dashboard */}
      <div className="space-y-6">
        <div>
          <h2 className="mb-2 text-2xl font-bold">التحليلات المتقدمة</h2>
          <p className="text-muted-foreground">
            رسوم بيانية تفاعلية لأداء النظام
          </p>
        </div>
        <AnalyticsDashboard />
      </div>

      {/* Alert Center */}
      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <AlertCenter
            alerts={alerts}
            onMarkAsRead={markAsRead}
            onDismiss={dismissAlert}
            onClearAll={clearAll}
          />
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>إجراءات سريعة</CardTitle>
            <CardDescription>أدوات مفيدة للإدارة السريعة</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full" variant="outline">
              <Activity className="mr-2 h-4 w-4" />
              تشغيل المطابقة اليدوية
            </Button>

            <Button className="w-full" variant="outline">
              <MessageSquare className="mr-2 h-4 w-4" />
              إعادة معالجة الرسائل
            </Button>

            <Button className="w-full" variant="outline">
              <Wifi className="mr-2 h-4 w-4" />
              إعادة اتصال WhatsApp
            </Button>

            <Button className="w-full" variant="outline">
              <CheckCircle className="mr-2 h-4 w-4" />
              تنظيف البيانات القديمة
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
