package config

import (
	"os"
	"strconv"
)

// Config holds all configuration for the WhatsApp service
type Config struct {
	Port        int
	DatabaseURL string
	LogLevel    string
	
	// WhatsApp specific settings
	SessionDir     string
	WebhookURL     string
	MaxRetries     int
	RetryDelay     int // seconds
	
	// AI service settings
	OllamaURL      string
	AIModel        string
	AITimeout      int // seconds
}

// Load loads configuration from environment variables with defaults
func Load() *Config {
	return &Config{
		Port:           getEnvAsInt("PORT", 8080),
		DatabaseURL:    getEnv("DATABASE_URL", "postgres://user:password@localhost/medibridge?sslmode=disable"),
		LogLevel:       getEnv("LOG_LEVEL", "info"),
		
		SessionDir:     getEnv("SESSION_DIR", "./sessions"),
		WebhookURL:     getEnv("WEBHOOK_URL", ""),
		MaxRetries:     getEnvAsInt("MAX_RETRIES", 3),
		RetryDelay:     getEnvAsInt("RETRY_DELAY", 5),
		
		OllamaURL:      getEnv("OLLAMA_URL", "http://localhost:11434"),
		AIModel:        getEnv("AI_MODEL", "llama2"),
		AITimeout:      getEnvAsInt("AI_TIMEOUT", 30),
	}
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt gets an environment variable as integer with a default value
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
