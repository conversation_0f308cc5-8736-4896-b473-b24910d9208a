{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 13173659658734678530, "deps": [[376837177317575824, "softbuffer", false, 14823593269787447068], [442785307232013896, "tauri_runtime", false, 646080527324604523], [3150220818285335163, "url", false, 6530261799351731353], [3722963349756955755, "once_cell", false, 16821080632637768006], [4143744114649553716, "raw_window_handle", false, 3850038477068984947], [5986029879202738730, "log", false, 3210362874149672375], [7752760652095876438, "build_script_build", false, 14646362836042100109], [8539587424388551196, "webview2_com", false, 6232518207737226145], [9010263965687315507, "http", false, 3354175764241350141], [11050281405049894993, "tauri_utils", false, 5597325593669197443], [13116089016666501665, "windows", false, 9661768058793115304], [13223659721939363523, "tao", false, 6503315544995249156], [14794439852947137341, "wry", false, 15915444810342487615]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-7b9ebeabe9b1bcb1\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}