{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 7780054017505697778, "deps": [[40386456601120721, "percent_encoding", false, 17688296499909026107], [442785307232013896, "tauri_runtime", false, 646080527324604523], [1200537532907108615, "url<PERSON><PERSON>n", false, 14176211530670977511], [3150220818285335163, "url", false, 6530261799351731353], [4143744114649553716, "raw_window_handle", false, 3850038477068984947], [4341921533227644514, "muda", false, 9014114453930462515], [4919829919303820331, "serialize_to_javascript", false, 8583847596493071862], [5986029879202738730, "log", false, 3210362874149672375], [7752760652095876438, "tauri_runtime_wry", false, 11761696483985120532], [8539587424388551196, "webview2_com", false, 6232518207737226145], [9010263965687315507, "http", false, 3354175764241350141], [9228235415475680086, "tauri_macros", false, 11330894984214067224], [9538054652646069845, "tokio", false, 5524459453298109882], [9689903380558560274, "serde", false, 10996426815917291500], [9920160576179037441, "getrandom", false, 15430601449999902805], [10229185211513642314, "mime", false, 1599519404717596838], [10629569228670356391, "futures_util", false, 572708887139757164], [10755362358622467486, "build_script_build", false, 9269494212664942228], [10806645703491011684, "thiserror", false, 44651847147331727], [11050281405049894993, "tauri_utils", false, 5597325593669197443], [11989259058781683633, "dunce", false, 13340844955090052542], [12565293087094287914, "window_vibrancy", false, 10561498113019273848], [12986574360607194341, "serde_repr", false, 11609501881873135158], [13077543566650298139, "heck", false, 1670613786372742531], [13116089016666501665, "windows", false, 9661768058793115304], [13625485746686963219, "anyhow", false, 8308486080552771400], [15367738274754116744, "serde_json", false, 1559938316259437281], [16928111194414003569, "dirs", false, 17480947938057484607], [17155886227862585100, "glob", false, 16192797392537921819]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-c231987c4e948544\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}