{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 11226960194569101119, "deps": [[5103565458935487, "futures_io", false, 14003796425397622648], [1615478164327904835, "pin_utils", false, 11973718540244705271], [1906322745568073236, "pin_project_lite", false, 7084418078783317499], [3129130049864710036, "memchr", false, 10024402093055877215], [6955678925937229351, "slab", false, 10116646655836202069], [7013762810557009322, "futures_sink", false, 12403478409331439443], [7620660491849607393, "futures_core", false, 17451578802806441489], [10565019901765856648, "futures_macro", false, 10397878171030325577], [16240732885093539806, "futures_task", false, 13537180190054193767]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-02d1b7ef9ced58a3\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}