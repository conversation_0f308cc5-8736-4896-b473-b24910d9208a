[package]
name = "db_entity"
version = "0.1.0"
edition = "2024"

[dependencies]
async-trait = { workspace = true }
chrono = { workspace = true, features = ["serde", "clock"] }
derive_more = { workspace = true, features = ["from"] }
sea-orm = { workspace = true, features = [
    "macros",
    "mock",
    "runtime-tokio-rustls",
    "sqlx-postgres",
    "with-chrono",
    "with-uuid",
] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
thiserror = { workspace = true }
uuid = { workspace = true, features = ["serde", "v7"] }

[dev-dependencies]
pretty_assertions = { workspace = true }
tokio = { workspace = true, features = ["full"] }
