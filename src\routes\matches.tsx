import { createFileRoute } from '@tanstack/react-router';
import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import {
  Activity,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  MessageSquare,
  User,
  Calendar,
} from 'lucide-react';

interface Match {
  id: number;
  request_message_id: number;
  offer_message_id: number;
  medicine_id: number;
  confidence_score: number;
  status: string;
  matching_criteria?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  notified_at?: string;
  completed_at?: string;
  expires_at?: string;
  // Related data
  medicine_name: string;
  requester_name: string;
  requester_phone: string;
  offerer_name: string;
  offerer_phone: string;
  request_content: string;
  offer_content: string;
}

export const Route = createFileRoute('/matches')({
  component: MatchesPage,
});

function MatchesPage() {
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);

  useEffect(() => {
    loadMatches();
  }, [statusFilter]);

  const loadMatches = async () => {
    try {
      setLoading(true);
      const response = await invoke<{
        matches: Match[];
        total: number;
        limit: number;
        offset: number;
      }>('get_matches', {
        limit: 50,
        offset: 0,
        status: statusFilter === 'all' ? undefined : statusFilter,
      });
      setMatches(response.matches);
    } catch (error) {
      console.error('Failed to load matches:', error);
      toast.error('فشل في تحميل المطابقات');
      // Fallback to mock data
      const mockMatches: Match[] = [
        {
          id: 1,
          request_message_id: 101,
          offer_message_id: 102,
          medicine_id: 1,
          confidence_score: 0.95,
          status: 'pending',
          matching_criteria:
            '{"price_match": true, "quantity_match": true, "time_relevance": 0.9}',
          notes: 'مطابقة عالية الجودة',
          created_at: '2024-01-20T10:00:00Z',
          updated_at: '2024-01-20T10:00:00Z',
          expires_at: '2024-01-27T10:00:00Z',
          medicine_name: 'باراسيتامول',
          requester_name: 'أحمد محمد',
          requester_phone: '201234567890',
          offerer_name: 'صيدلية الشفاء',
          offerer_phone: '201987654321',
          request_content: 'محتاج باراسيتامول 500 مجم عاجل',
          offer_content: 'متوفر باراسيتامول 500 مجم بسعر ممتاز',
        },
        {
          id: 2,
          request_message_id: 103,
          offer_message_id: 104,
          medicine_id: 2,
          confidence_score: 0.87,
          status: 'notified',
          matching_criteria:
            '{"price_match": false, "quantity_match": true, "time_relevance": 0.8}',
          created_at: '2024-01-19T15:30:00Z',
          updated_at: '2024-01-20T09:00:00Z',
          notified_at: '2024-01-20T09:00:00Z',
          expires_at: '2024-01-26T15:30:00Z',
          medicine_name: 'أموكسيسيلين',
          requester_name: 'فاطمة علي',
          requester_phone: '201555666777',
          offerer_name: 'صيدلية النور',
          offerer_phone: '201444555666',
          request_content: 'بدور على أموكسيسيلين 500 مجم',
          offer_content: 'عندي أموكسيسيلين متوفر',
        },
        {
          id: 3,
          request_message_id: 105,
          offer_message_id: 106,
          medicine_id: 3,
          confidence_score: 0.92,
          status: 'completed',
          matching_criteria:
            '{"price_match": true, "quantity_match": true, "time_relevance": 0.95}',
          notes: 'تم إتمام الصفقة بنجاح',
          created_at: '2024-01-18T12:00:00Z',
          updated_at: '2024-01-19T14:00:00Z',
          notified_at: '2024-01-18T13:00:00Z',
          completed_at: '2024-01-19T14:00:00Z',
          medicine_name: 'فيتامين د',
          requester_name: 'محمد حسن',
          requester_phone: '201777888999',
          offerer_name: 'صيدلية الأمل',
          offerer_phone: '201333444555',
          request_content: 'محتاج فيتامين د للأطفال',
          offer_content: 'متوفر فيتامين د نقط للأطفال',
        },
      ];
      setMatches(mockMatches);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      pending: {
        label: 'في الانتظار',
        variant: 'outline' as const,
        icon: Clock,
      },
      notified: {
        label: 'تم الإشعار',
        variant: 'default' as const,
        icon: MessageSquare,
      },
      completed: {
        label: 'مكتمل',
        variant: 'default' as const,
        icon: CheckCircle,
      },
      expired: {
        label: 'منتهي الصلاحية',
        variant: 'secondary' as const,
        icon: AlertCircle,
      },
      cancelled: {
        label: 'ملغي',
        variant: 'destructive' as const,
        icon: AlertCircle,
      },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || {
      label: status,
      variant: 'outline' as const,
      icon: AlertCircle,
    };

    const Icon = statusInfo.icon;
    return (
      <Badge variant={statusInfo.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {statusInfo.label}
      </Badge>
    );
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600';
    if (score >= 0.7) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredMatches = matches.filter(match => {
    return statusFilter === 'all' || match.status === statusFilter;
  });

  const showMatchDetails = (match: Match) => {
    setSelectedMatch(match);
    setIsDetailDialogOpen(true);
  };

  const updateMatchStatus = async (matchId: number, newStatus: string) => {
    try {
      await invoke('update_match_status', {
        matchId,
        status: newStatus,
        notes: null,
      });
      toast.success('تم تحديث حالة المطابقة بنجاح');
      loadMatches(); // Reload matches
    } catch (error) {
      console.error('Failed to update match status:', error);
      toast.error('فشل في تحديث حالة المطابقة');
    }
  };

  const runManualMatching = async () => {
    try {
      setLoading(true);
      await invoke('run_manual_matching');
      toast.success('تم تشغيل عملية المطابقة بنجاح');
      // Wait a bit then reload matches
      setTimeout(() => {
        loadMatches();
      }, 2000);
    } catch (error) {
      console.error('Failed to run manual matching:', error);
      toast.error('فشل في تشغيل عملية المطابقة');
    } finally {
      setLoading(false);
    }
  };

  const getMatchingCriteria = (criteria?: string) => {
    if (!criteria) return null;
    try {
      return JSON.parse(criteria);
    } catch {
      return null;
    }
  };

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg">جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">المطابقات</h1>
          <p className="text-muted-foreground">
            مطابقة طلبات الأدوية مع العروض المتاحة
          </p>
        </div>

        <Button onClick={runManualMatching} disabled={loading}>
          <Activity
            className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`}
          />
          تشغيل المطابقة
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي المطابقات
            </CardTitle>
            <Activity className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{matches.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">في الانتظار</CardTitle>
            <Clock className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {matches.filter(m => m.status === 'pending').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مكتملة</CardTitle>
            <CheckCircle className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {matches.filter(m => m.status === 'completed').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">معدل النجاح</CardTitle>
            <CheckCircle className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {matches.length > 0
                ? Math.round(
                    (matches.filter(m => m.status === 'completed').length /
                      matches.length) *
                      100,
                  )
                : 0}
              %
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>الفلترة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="حالة المطابقة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="pending">في الانتظار</SelectItem>
                <SelectItem value="notified">تم الإشعار</SelectItem>
                <SelectItem value="completed">مكتمل</SelectItem>
                <SelectItem value="expired">منتهي الصلاحية</SelectItem>
                <SelectItem value="cancelled">ملغي</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Matches Table */}
      <Card>
        <CardHeader>
          <CardTitle>المطابقات ({filteredMatches.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الدواء</TableHead>
                <TableHead>الطالب</TableHead>
                <TableHead>المقدم</TableHead>
                <TableHead>درجة الثقة</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>تاريخ الإنشاء</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMatches.map(match => (
                <TableRow key={match.id}>
                  <TableCell className="font-medium">
                    {match.medicine_name}
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{match.requester_name}</div>
                      <div className="text-muted-foreground text-xs">
                        {match.requester_phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{match.offerer_name}</div>
                      <div className="text-muted-foreground text-xs">
                        {match.offerer_phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span
                      className={`font-bold ${getConfidenceColor(match.confidence_score)}`}
                    >
                      {Math.round(match.confidence_score * 100)}%
                    </span>
                  </TableCell>
                  <TableCell>{getStatusBadge(match.status)}</TableCell>
                  <TableCell>
                    {new Date(match.created_at).toLocaleDateString('ar-EG')}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => showMatchDetails(match)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {match.status === 'pending' && (
                        <Button
                          size="sm"
                          onClick={() =>
                            updateMatchStatus(match.id, 'notified')
                          }
                        >
                          إشعار
                        </Button>
                      )}
                      {match.status === 'notified' && (
                        <Button
                          size="sm"
                          onClick={() =>
                            updateMatchStatus(match.id, 'completed')
                          }
                        >
                          إكمال
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredMatches.length === 0 && (
            <div className="py-8 text-center">
              <Activity className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">لا توجد مطابقات</h3>
              <p className="text-muted-foreground">
                لم يتم العثور على مطابقات تطابق المعايير المحددة
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Match Details Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>تفاصيل المطابقة</DialogTitle>
            <DialogDescription>
              معلومات مفصلة عن المطابقة ومعايير التطابق
            </DialogDescription>
          </DialogHeader>

          {selectedMatch && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium">الدواء</label>
                  <p className="text-lg font-semibold">
                    {selectedMatch.medicine_name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">درجة الثقة</label>
                  <p
                    className={`text-lg font-bold ${getConfidenceColor(selectedMatch.confidence_score)}`}
                  >
                    {Math.round(selectedMatch.confidence_score * 100)}%
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">الحالة</label>
                  <div className="mt-1">
                    {getStatusBadge(selectedMatch.status)}
                  </div>
                </div>
              </div>

              {/* Request and Offer */}
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      الطلب
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <label className="text-muted-foreground text-xs font-medium">
                        الطالب
                      </label>
                      <p className="font-medium">
                        {selectedMatch.requester_name}
                      </p>
                      <p className="text-muted-foreground text-sm">
                        {selectedMatch.requester_phone}
                      </p>
                    </div>
                    <div>
                      <label className="text-muted-foreground text-xs font-medium">
                        محتوى الطلب
                      </label>
                      <p className="bg-muted rounded p-2 text-sm">
                        {selectedMatch.request_content}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      العرض
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <label className="text-muted-foreground text-xs font-medium">
                        المقدم
                      </label>
                      <p className="font-medium">
                        {selectedMatch.offerer_name}
                      </p>
                      <p className="text-muted-foreground text-sm">
                        {selectedMatch.offerer_phone}
                      </p>
                    </div>
                    <div>
                      <label className="text-muted-foreground text-xs font-medium">
                        محتوى العرض
                      </label>
                      <p className="bg-muted rounded p-2 text-sm">
                        {selectedMatch.offer_content}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Matching Criteria */}
              {getMatchingCriteria(selectedMatch.matching_criteria) && (
                <Card>
                  <CardHeader>
                    <CardTitle>معايير التطابق</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                      {Object.entries(
                        getMatchingCriteria(selectedMatch.matching_criteria) ||
                          {},
                      ).map(([key, value]) => (
                        <div key={key} className="text-center">
                          <div className="text-muted-foreground text-sm">
                            {key}
                          </div>
                          <div className="font-semibold">
                            {typeof value === 'boolean'
                              ? value
                                ? '✓'
                                : '✗'
                              : value}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Notes */}
              {selectedMatch.notes && (
                <div>
                  <label className="text-sm font-medium">ملاحظات</label>
                  <p className="bg-muted mt-1 rounded-md p-3 text-sm">
                    {selectedMatch.notes}
                  </p>
                </div>
              )}

              {/* Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    الجدول الزمني
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>تاريخ الإنشاء:</span>
                      <span>
                        {new Date(selectedMatch.created_at).toLocaleString(
                          'ar-EG',
                        )}
                      </span>
                    </div>
                    {selectedMatch.notified_at && (
                      <div className="flex justify-between">
                        <span>تاريخ الإشعار:</span>
                        <span>
                          {new Date(selectedMatch.notified_at).toLocaleString(
                            'ar-EG',
                          )}
                        </span>
                      </div>
                    )}
                    {selectedMatch.completed_at && (
                      <div className="flex justify-between">
                        <span>تاريخ الإكمال:</span>
                        <span>
                          {new Date(selectedMatch.completed_at).toLocaleString(
                            'ar-EG',
                          )}
                        </span>
                      </div>
                    )}
                    {selectedMatch.expires_at && (
                      <div className="flex justify-between">
                        <span>تاريخ انتهاء الصلاحية:</span>
                        <span>
                          {new Date(selectedMatch.expires_at).toLocaleString(
                            'ar-EG',
                          )}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
