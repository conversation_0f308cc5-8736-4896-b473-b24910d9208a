use tauri::State;
use crate::api::{WhatsAppApi, Account, CreateAccountRequest};
use crate::error::AppError;
use crate::state::AppState;

/// Get all WhatsApp accounts
#[tauri::command]
pub async fn get_accounts(state: State<'_, AppState>) -> Result<Vec<Account>, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.get_accounts().await.map_err(|e| e.to_string())
}

/// Create a new WhatsApp account
#[tauri::command]
pub async fn create_account(
    phone_number: String,
    display_name: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    let request = CreateAccountRequest {
        phone_number,
        display_name,
    };
    api.create_account(request).await.map_err(|e| e.to_string())
}

/// Update account status
#[tauri::command]
pub async fn update_account_status(
    account_id: i32,
    status: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.update_account_status(account_id, status)
        .await
        .map_err(|e| e.to_string())
}

/// Delete an account
#[tauri::command]
pub async fn delete_account(
    account_id: i32,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let api = WhatsAppApi::new("http://localhost:8080".to_string());
    api.delete_account(account_id).await.map_err(|e| e.to_string())
}
