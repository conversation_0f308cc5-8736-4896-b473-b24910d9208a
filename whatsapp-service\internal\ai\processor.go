package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"

	"medibridge-whatsapp/internal/database"
)

// MessageProcessor handles AI processing of WhatsApp messages
type MessageProcessor struct {
	logger      *zap.Logger
	db          *database.DB
	ollamaClient *OllamaClient
	batchSize   int
	interval    time.Duration
}

// NewMessageProcessor creates a new message processor
func NewMessageProcessor(
	logger *zap.Logger,
	db *database.DB,
	ollamaClient *OllamaClient,
	batchSize int,
	interval time.Duration,
) *MessageProcessor {
	return &MessageProcessor{
		logger:       logger,
		db:           db,
		ollamaClient: ollamaClient,
		batchSize:    batchSize,
		interval:     interval,
	}
}

// Start begins processing messages in batches
func (p *MessageProcessor) Start(ctx context.Context) {
	ticker := time.NewTicker(p.interval)
	defer ticker.Stop()

	p.logger.Info("Starting AI message processor",
		zap.Int("batch_size", p.batchSize),
		zap.Duration("interval", p.interval))

	for {
		select {
		case <-ctx.Done():
			p.logger.Info("AI message processor stopped")
			return
		case <-ticker.C:
			if err := p.processBatch(ctx); err != nil {
				p.logger.Error("Failed to process message batch", zap.Error(err))
			}
		}
	}
}

// processBatch processes a batch of pending messages
func (p *MessageProcessor) processBatch(ctx context.Context) error {
	// Get pending messages
	messages, err := p.db.GetPendingMessages(p.batchSize)
	if err != nil {
		return fmt.Errorf("failed to get pending messages: %w", err)
	}

	if len(messages) == 0 {
		return nil // No messages to process
	}

	p.logger.Info("Processing message batch", zap.Int("count", len(messages)))

	// Process each message
	for _, msg := range messages {
		if err := p.processMessage(ctx, msg); err != nil {
			p.logger.Error("Failed to process message",
				zap.Int("message_id", msg.ID),
				zap.Error(err))
		}
	}

	return nil
}

// processMessage processes a single message with AI
func (p *MessageProcessor) processMessage(ctx context.Context, msg *database.Message) error {
	startTime := time.Now()
	
	p.logger.Debug("Processing message",
		zap.Int("message_id", msg.ID),
		zap.String("content", msg.Content[:min(50, len(msg.Content))]))

	// Analyze message with AI
	analysis, err := p.ollamaClient.AnalyzeMessage(ctx, msg.Content)
	if err != nil {
		// Mark as failed
		errorMsg := err.Error()
		return p.db.UpdateMessageProcessing(msg.ID, "failed", nil, &errorMsg)
	}

	// Convert analysis to JSON
	analysisJSON, err := json.Marshal(analysis)
	if err != nil {
		errorMsg := fmt.Sprintf("failed to marshal analysis: %v", err)
		return p.db.UpdateMessageProcessing(msg.ID, "failed", nil, &errorMsg)
	}

	analysisStr := string(analysisJSON)

	// Update message with analysis
	if err := p.db.UpdateMessageProcessing(msg.ID, "processed", &analysisStr, nil); err != nil {
		return fmt.Errorf("failed to update message processing: %w", err)
	}

	// Update message type if determined
	if analysis.MessageType != "unknown" {
		if err := p.updateMessageType(msg.ID, analysis.MessageType); err != nil {
			p.logger.Error("Failed to update message type",
				zap.Int("message_id", msg.ID),
				zap.Error(err))
		}
	}

	// Store medicines if found
	if len(analysis.Medicines) > 0 {
		if err := p.storeMedicines(msg, analysis.Medicines); err != nil {
			p.logger.Error("Failed to store medicines",
				zap.Int("message_id", msg.ID),
				zap.Error(err))
		}
	}

	processingTime := time.Since(startTime)
	p.logger.Info("Message processed successfully",
		zap.Int("message_id", msg.ID),
		zap.String("type", analysis.MessageType),
		zap.Int("medicines_count", len(analysis.Medicines)),
		zap.Float64("confidence", analysis.Confidence),
		zap.Duration("processing_time", processingTime))

	return nil
}

// updateMessageType updates the message type in the database
func (p *MessageProcessor) updateMessageType(messageID int, messageType string) error {
	query := `UPDATE messages SET message_type = $1, updated_at = NOW() WHERE id = $2`
	_, err := p.db.Exec(query, messageType, messageID)
	return err
}

// storeMedicines stores extracted medicines in the database
func (p *MessageProcessor) storeMedicines(msg *database.Message, medicines []Medicine) error {
	for _, medicine := range medicines {
		// First, ensure the medicine exists in the medicines table
		medicineID, err := p.ensureMedicineExists(medicine.Name)
		if err != nil {
			p.logger.Error("Failed to ensure medicine exists",
				zap.String("medicine", medicine.Name),
				zap.Error(err))
			continue
		}

		// Insert into message_medicines table
		if err := p.insertMessageMedicine(msg.ID, medicineID, medicine); err != nil {
			p.logger.Error("Failed to insert message medicine",
				zap.Int("message_id", msg.ID),
				zap.Int("medicine_id", medicineID),
				zap.Error(err))
			continue
		}

		// Update medicine statistics
		isRequest := msg.MessageType == "request"
		if err := p.updateMedicineStats(medicineID, isRequest); err != nil {
			p.logger.Error("Failed to update medicine stats",
				zap.Int("medicine_id", medicineID),
				zap.Error(err))
		}
	}

	return nil
}

// ensureMedicineExists ensures a medicine exists in the database and returns its ID
func (p *MessageProcessor) ensureMedicineExists(name string) (int, error) {
	// Try to find existing medicine
	var id int
	query := `SELECT id FROM medicines WHERE name = $1`
	err := p.db.QueryRow(query, name).Scan(&id)
	
	if err == nil {
		return id, nil // Medicine exists
	}

	// Medicine doesn't exist, create it
	insertQuery := `
		INSERT INTO medicines (name, mention_count, request_count, offer_count, created_at, updated_at, last_mentioned_at)
		VALUES ($1, 1, 0, 0, NOW(), NOW(), NOW())
		RETURNING id
	`
	
	err = p.db.QueryRow(insertQuery, name).Scan(&id)
	if err != nil {
		return 0, fmt.Errorf("failed to create medicine: %w", err)
	}

	return id, nil
}

// insertMessageMedicine inserts a message-medicine relationship
func (p *MessageProcessor) insertMessageMedicine(messageID, medicineID int, medicine Medicine) error {
	query := `
		INSERT INTO message_medicines (
			message_id, medicine_id, confidence_score, quantity, unit, 
			price, currency, position_start, position_end, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
		ON CONFLICT (message_id, medicine_id) DO NOTHING
	`

	var positionStart, positionEnd *int
	if medicine.Position.Start > 0 {
		positionStart = &medicine.Position.Start
	}
	if medicine.Position.End > 0 {
		positionEnd = &medicine.Position.End
	}

	var quantity, unit, price, currency *string
	if medicine.Quantity != "" {
		quantity = &medicine.Quantity
	}
	if medicine.Unit != "" {
		unit = &medicine.Unit
	}
	if medicine.Price != "" {
		price = &medicine.Price
	}
	if medicine.Currency != "" {
		currency = &medicine.Currency
	}

	_, err := p.db.Exec(query,
		messageID,
		medicineID,
		medicine.Confidence,
		quantity,
		unit,
		price,
		currency,
		positionStart,
		positionEnd,
	)

	return err
}

// updateMedicineStats updates medicine statistics
func (p *MessageProcessor) updateMedicineStats(medicineID int, isRequest bool) error {
	var query string
	if isRequest {
		query = `
			UPDATE medicines 
			SET mention_count = mention_count + 1, 
			    request_count = request_count + 1,
			    last_mentioned_at = NOW(),
			    updated_at = NOW()
			WHERE id = $1
		`
	} else {
		query = `
			UPDATE medicines 
			SET mention_count = mention_count + 1, 
			    offer_count = offer_count + 1,
			    last_mentioned_at = NOW(),
			    updated_at = NOW()
			WHERE id = $1
		`
	}

	_, err := p.db.Exec(query, medicineID)
	return err
}

// ProcessSingleMessage processes a single message immediately (for manual reprocessing)
func (p *MessageProcessor) ProcessSingleMessage(ctx context.Context, messageID int) error {
	// Get the message
	query := `
		SELECT id, whatsapp_message_id, account_id, group_jid, group_name,
		       sender_jid, sender_name, content, message_type, processing_status,
		       ai_analysis, error_message, message_timestamp, received_at,
		       processed_at, created_at, updated_at
		FROM messages 
		WHERE id = $1
	`

	msg := &database.Message{}
	err := p.db.QueryRow(query, messageID).Scan(
		&msg.ID,
		&msg.WhatsAppMessageID,
		&msg.AccountID,
		&msg.GroupJID,
		&msg.GroupName,
		&msg.SenderJID,
		&msg.SenderName,
		&msg.Content,
		&msg.MessageType,
		&msg.ProcessingStatus,
		&msg.AIAnalysis,
		&msg.ErrorMessage,
		&msg.MessageTimestamp,
		&msg.ReceivedAt,
		&msg.ProcessedAt,
		&msg.CreatedAt,
		&msg.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to get message: %w", err)
	}

	// Reset processing status
	if err := p.db.UpdateMessageProcessing(messageID, "pending", nil, nil); err != nil {
		return fmt.Errorf("failed to reset message status: %w", err)
	}

	// Process the message
	return p.processMessage(ctx, msg)
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
