package whatsapp

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types/events"
	"go.uber.org/zap"

	"medibridge-whatsapp/internal/database"
)

// Manager manages multiple WhatsApp client connections
type Manager struct {
	logger   *zap.Logger
	db       *database.DB
	clients  map[string]*Client
	store    *sqlstore.Container
	mutex    sync.RWMutex
	shutdown chan struct{}
}

// Client represents a single WhatsApp client connection
type Client struct {
	ID          int
	PhoneNumber string
	DisplayName string
	Client      *whatsmeow.Client
	Connected   bool
	LastSeen    time.Time
}

// NewManager creates a new WhatsApp manager
func NewManager(logger *zap.Logger, db *database.DB) *Manager {
	// Initialize WhatsApp store
	store := sqlstore.NewWithDB(db.DB, "postgres", nil)

	return &Manager{
		logger:   logger,
		db:       db,
		clients:  make(map[string]*Client),
		store:    store,
		shutdown: make(chan struct{}),
	}
}

// Start initializes and starts all active WhatsApp connections
func (m *Manager) Start() error {
	m.logger.Info("Starting WhatsApp manager")

	// Get all active accounts from database
	accounts, err := m.db.GetActiveAccounts()
	if err != nil {
		return fmt.Errorf("failed to get active accounts: %w", err)
	}

	// Start each account
	for _, account := range accounts {
		if err := m.startClient(account); err != nil {
			m.logger.Error("Failed to start client",
				zap.String("phone", account.PhoneNumber),
				zap.Error(err))
		}
	}

	// Start message processing goroutine
	go m.processMessages()

	return nil
}

// startClient starts a WhatsApp client for the given account
func (m *Manager) startClient(account *database.Account) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Check if client already exists
	if _, exists := m.clients[account.PhoneNumber]; exists {
		return fmt.Errorf("client already exists for %s", account.PhoneNumber)
	}

	// Get device store
	ctx := context.Background()
	deviceStore, err := m.store.GetFirstDevice(ctx)
	if err != nil {
		return fmt.Errorf("failed to get device store: %w", err)
	}

	// Create WhatsApp client
	client := whatsmeow.NewClient(deviceStore, nil)

	// Set up event handler
	client.AddEventHandler(m.eventHandler(account.ID))

	// Create our client wrapper
	waClient := &Client{
		ID:          account.ID,
		PhoneNumber: account.PhoneNumber,
		DisplayName: account.DisplayName,
		Client:      client,
		Connected:   false,
		LastSeen:    time.Now(),
	}

	// Store client
	m.clients[account.PhoneNumber] = waClient

	// Connect client
	if client.Store.ID == nil {
		// Need to pair/login
		m.logger.Info("Client needs pairing", zap.String("phone", account.PhoneNumber))
		// For now, we'll mark as inactive and require manual pairing
		m.db.UpdateAccountStatus(account.ID, "inactive")
		return fmt.Errorf("client needs pairing: %s", account.PhoneNumber)
	} else {
		// Connect existing session
		err := client.Connect()
		if err != nil {
			m.logger.Error("Failed to connect client",
				zap.String("phone", account.PhoneNumber),
				zap.Error(err))
			m.db.UpdateAccountStatus(account.ID, "error")
			return err
		}

		waClient.Connected = true
		m.logger.Info("Client connected successfully", zap.String("phone", account.PhoneNumber))
	}

	return nil
}

// eventHandler creates an event handler for a specific account
func (m *Manager) eventHandler(accountID int) func(interface{}) {
	return func(evt interface{}) {
		switch v := evt.(type) {
		case *events.Message:
			m.handleMessage(accountID, v)
		case *events.Connected:
			m.handleConnected(accountID)
		case *events.Disconnected:
			m.handleDisconnected(accountID)
		case *events.LoggedOut:
			m.handleLoggedOut(accountID)
		}
	}
}

// handleMessage processes incoming WhatsApp messages
func (m *Manager) handleMessage(accountID int, evt *events.Message) {
	// Skip if message is from us
	if evt.Info.IsFromMe {
		return
	}

	// Only process group messages for now
	if !evt.Info.IsGroup {
		return
	}

	// Extract message content
	var content string
	if evt.Message.GetConversation() != "" {
		content = evt.Message.GetConversation()
	} else if evt.Message.GetExtendedTextMessage() != nil {
		content = evt.Message.GetExtendedTextMessage().GetText()
	} else {
		// Skip non-text messages for now
		return
	}

	// Create message record
	msg := &database.Message{
		WhatsAppMessageID: evt.Info.ID,
		AccountID:         accountID,
		GroupJID:          evt.Info.Chat.String(),
		GroupName:         "", // We'll need to get this separately
		SenderJID:         evt.Info.Sender.String(),
		SenderName:        evt.Info.PushName,
		Content:           content,
		MessageType:       "unknown",
		ProcessingStatus:  "pending",
		MessageTimestamp:  evt.Info.Timestamp,
		ReceivedAt:        time.Now(),
	}

	// Insert message into database
	if err := m.db.InsertMessage(msg); err != nil {
		m.logger.Error("Failed to insert message",
			zap.Error(err),
			zap.String("message_id", evt.Info.ID))
	} else {
		m.logger.Info("Message received and stored",
			zap.String("message_id", evt.Info.ID),
			zap.String("sender", evt.Info.PushName),
			zap.String("content", content[:min(50, len(content))]))
	}
}

// handleConnected handles client connection events
func (m *Manager) handleConnected(accountID int) {
	m.logger.Info("Client connected", zap.Int("account_id", accountID))
	m.db.UpdateAccountStatus(accountID, "active")

	// Update client status
	m.mutex.Lock()
	for _, client := range m.clients {
		if client.ID == accountID {
			client.Connected = true
			client.LastSeen = time.Now()
			break
		}
	}
	m.mutex.Unlock()
}

// handleDisconnected handles client disconnection events
func (m *Manager) handleDisconnected(accountID int) {
	m.logger.Info("Client disconnected", zap.Int("account_id", accountID))
	m.db.UpdateAccountStatus(accountID, "inactive")

	// Update client status
	m.mutex.Lock()
	for _, client := range m.clients {
		if client.ID == accountID {
			client.Connected = false
			break
		}
	}
	m.mutex.Unlock()
}

// handleLoggedOut handles client logout events
func (m *Manager) handleLoggedOut(accountID int) {
	m.logger.Info("Client logged out", zap.Int("account_id", accountID))
	m.db.UpdateAccountStatus(accountID, "inactive")

	// Remove client
	m.mutex.Lock()
	for phone, client := range m.clients {
		if client.ID == accountID {
			delete(m.clients, phone)
			break
		}
	}
	m.mutex.Unlock()
}

// processMessages processes pending messages with AI
func (m *Manager) processMessages() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			messages, err := m.db.GetPendingMessages(10)
			if err != nil {
				m.logger.Error("Failed to get pending messages", zap.Error(err))
				continue
			}

			for _, msg := range messages {
				// TODO: Process with AI
				// For now, just mark as processed
				m.db.UpdateMessageProcessing(msg.ID, "processed", nil, nil)
			}

		case <-m.shutdown:
			return
		}
	}
}

// GetClients returns information about all connected clients
func (m *Manager) GetClients() map[string]*Client {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	clients := make(map[string]*Client)
	for k, v := range m.clients {
		clients[k] = v
	}
	return clients
}

// Shutdown gracefully shuts down all WhatsApp connections
func (m *Manager) Shutdown() {
	m.logger.Info("Shutting down WhatsApp manager")

	close(m.shutdown)

	m.mutex.Lock()
	defer m.mutex.Unlock()

	for phone, client := range m.clients {
		if client.Connected {
			client.Client.Disconnect()
			m.logger.Info("Disconnected client", zap.String("phone", phone))
		}
	}

	m.clients = make(map[string]*Client)
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
