use std::sync::Arc;

use db_entity::{accounts, accounts::Entity as Accounts};
use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, EntityTrait, PaginatorTrait, QueryFilter,
    QueryOrder, Set,
};
use serde::{Deserialize, Serialize};

use crate::{PaginationParams, PaginationResult, ServiceError};

/// Account service for managing WhatsApp accounts
#[derive(Clone)]
pub struct AccountService {
    db: Arc<DatabaseConnection>,
}

impl AccountService {
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self { db }
    }

    /// Create a new account
    pub async fn create_account(
        &self,
        phone_number: String,
        display_name: String,
    ) -> Result<accounts::Model, ServiceError> {
        let account = accounts::ActiveModel {
            phone_number: Set(phone_number),
            display_name: Set(display_name),
            status: Set("inactive".to_string()),
            created_at: Set(chrono::Utc::now().naive_utc()),
            updated_at: Set(chrono::Utc::now().naive_utc()),
            ..Default::default()
        };

        let result = account.insert(&*self.db).await?;
        Ok(result)
    }

    /// Get account by ID
    pub async fn get_account(&self, id: i32) -> Result<Option<accounts::Model>, ServiceError> {
        let account = Accounts::find_by_id(id).one(&*self.db).await?;
        Ok(account)
    }

    /// Get account by phone number
    pub async fn get_account_by_phone(
        &self,
        phone_number: &str,
    ) -> Result<Option<accounts::Model>, ServiceError> {
        let account = Accounts::find()
            .filter(accounts::Column::PhoneNumber.eq(phone_number))
            .one(&*self.db)
            .await?;
        Ok(account)
    }

    /// Update account status
    pub async fn update_account_status(
        &self,
        id: i32,
        status: String,
    ) -> Result<accounts::Model, ServiceError> {
        let account = Accounts::find_by_id(id)
            .one(&*self.db)
            .await?
            .ok_or(ServiceError::NotFound("Account not found".to_string()))?;

        let mut account: accounts::ActiveModel = account.into();
        account.status = Set(status);
        account.updated_at = Set(chrono::Utc::now().naive_utc());

        let result = account.update(&*self.db).await?;
        Ok(result)
    }

    /// Update account session data
    pub async fn update_session_data(
        &self,
        id: i32,
        session_data: Option<String>,
    ) -> Result<accounts::Model, ServiceError> {
        let account = Accounts::find_by_id(id)
            .one(&*self.db)
            .await?
            .ok_or(ServiceError::NotFound("Account not found".to_string()))?;

        let mut account: accounts::ActiveModel = account.into();
        account.session_data = Set(session_data);
        account.last_connected_at = Set(Some(chrono::Utc::now().naive_utc()));
        account.updated_at = Set(chrono::Utc::now().naive_utc());

        let result = account.update(&*self.db).await?;
        Ok(result)
    }

    /// Get all accounts with pagination
    pub async fn get_accounts(
        &self,
        pagination: PaginationParams,
    ) -> Result<PaginationResult<Vec<accounts::Model>>, ServiceError> {
        let paginator = Accounts::find()
            .order_by_desc(accounts::Column::CreatedAt)
            .paginate(&*self.db, pagination.page_size);

        let total = paginator.num_items().await?;
        let accounts = paginator.fetch_page(pagination.page - 1).await?;

        Ok(PaginationResult {
            data: accounts,
            total,
            page: pagination.page,
            page_size: pagination.page_size,
        })
    }

    /// Get active accounts
    pub async fn get_active_accounts(&self) -> Result<Vec<accounts::Model>, ServiceError> {
        let accounts = Accounts::find()
            .filter(accounts::Column::Status.eq("active"))
            .order_by_desc(accounts::Column::LastConnectedAt)
            .all(&*self.db)
            .await?;

        Ok(accounts)
    }

    /// Delete account
    pub async fn delete_account(&self, id: i32) -> Result<(), ServiceError> {
        let account = Accounts::find_by_id(id)
            .one(&*self.db)
            .await?
            .ok_or(ServiceError::NotFound("Account not found".to_string()))?;

        let account: accounts::ActiveModel = account.into();
        account.delete(&*self.db).await?;

        Ok(())
    }

    /// Update account display name
    pub async fn update_display_name(
        &self,
        id: i32,
        display_name: String,
    ) -> Result<accounts::Model, ServiceError> {
        let account = Accounts::find_by_id(id)
            .one(&*self.db)
            .await?
            .ok_or(ServiceError::NotFound("Account not found".to_string()))?;

        let mut account: accounts::ActiveModel = account.into();
        account.display_name = Set(display_name);
        account.updated_at = Set(chrono::Utc::now().naive_utc());

        let result = account.update(&*self.db).await?;
        Ok(result)
    }
}

/// DTO for creating a new account
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAccountDto {
    pub phone_number: String,
    pub display_name: String,
}

/// DTO for updating account status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAccountStatusDto {
    pub status: String,
}

/// DTO for updating account display name
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAccountDisplayNameDto {
    pub display_name: String,
}
