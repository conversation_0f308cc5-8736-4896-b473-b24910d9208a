package database

import (
	"database/sql"
	"time"

	_ "github.com/lib/pq"
)

// DB wraps the sql.DB connection
type DB struct {
	*sql.DB
}

// Connect establishes a connection to the database
func Connect(databaseURL string) (*DB, error) {
	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		return nil, err
	}

	// Configure connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, err
	}

	return &DB{db}, nil
}

// Account represents a WhatsApp account
type Account struct {
	ID              int       `json:"id"`
	PhoneNumber     string    `json:"phone_number"`
	DisplayName     string    `json:"display_name"`
	SessionData     *string   `json:"session_data,omitempty"`
	Status          string    `json:"status"`
	LastConnectedAt *time.Time `json:"last_connected_at,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// Message represents a WhatsApp message
type Message struct {
	ID                 int       `json:"id"`
	WhatsAppMessageID  string    `json:"whatsapp_message_id"`
	AccountID          int       `json:"account_id"`
	GroupJID           string    `json:"group_jid"`
	GroupName          string    `json:"group_name"`
	SenderJID          string    `json:"sender_jid"`
	SenderName         string    `json:"sender_name"`
	Content            string    `json:"content"`
	MessageType        string    `json:"message_type"`
	ProcessingStatus   string    `json:"processing_status"`
	AIAnalysis         *string   `json:"ai_analysis,omitempty"`
	ErrorMessage       *string   `json:"error_message,omitempty"`
	MessageTimestamp   time.Time `json:"message_timestamp"`
	ReceivedAt         time.Time `json:"received_at"`
	ProcessedAt        *time.Time `json:"processed_at,omitempty"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// GetAccountByPhone retrieves an account by phone number
func (db *DB) GetAccountByPhone(phoneNumber string) (*Account, error) {
	query := `
		SELECT id, phone_number, display_name, session_data, status, 
		       last_connected_at, created_at, updated_at
		FROM accounts 
		WHERE phone_number = $1
	`
	
	account := &Account{}
	err := db.QueryRow(query, phoneNumber).Scan(
		&account.ID,
		&account.PhoneNumber,
		&account.DisplayName,
		&account.SessionData,
		&account.Status,
		&account.LastConnectedAt,
		&account.CreatedAt,
		&account.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	
	return account, nil
}

// UpdateAccountStatus updates the status of an account
func (db *DB) UpdateAccountStatus(accountID int, status string) error {
	query := `
		UPDATE accounts 
		SET status = $1, updated_at = NOW()
		WHERE id = $2
	`
	
	_, err := db.Exec(query, status, accountID)
	return err
}

// UpdateAccountSession updates the session data of an account
func (db *DB) UpdateAccountSession(accountID int, sessionData string) error {
	query := `
		UPDATE accounts 
		SET session_data = $1, last_connected_at = NOW(), updated_at = NOW()
		WHERE id = $2
	`
	
	_, err := db.Exec(query, sessionData, accountID)
	return err
}

// InsertMessage inserts a new message into the database
func (db *DB) InsertMessage(msg *Message) error {
	query := `
		INSERT INTO messages (
			whatsapp_message_id, account_id, group_jid, group_name,
			sender_jid, sender_name, content, message_type,
			processing_status, message_timestamp, received_at,
			created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
		) RETURNING id
	`
	
	now := time.Now()
	err := db.QueryRow(
		query,
		msg.WhatsAppMessageID,
		msg.AccountID,
		msg.GroupJID,
		msg.GroupName,
		msg.SenderJID,
		msg.SenderName,
		msg.Content,
		msg.MessageType,
		msg.ProcessingStatus,
		msg.MessageTimestamp,
		msg.ReceivedAt,
		now,
		now,
	).Scan(&msg.ID)
	
	return err
}

// UpdateMessageProcessing updates the processing status and AI analysis of a message
func (db *DB) UpdateMessageProcessing(messageID int, status string, aiAnalysis *string, errorMsg *string) error {
	query := `
		UPDATE messages 
		SET processing_status = $1, ai_analysis = $2, error_message = $3,
		    processed_at = NOW(), updated_at = NOW()
		WHERE id = $4
	`
	
	_, err := db.Exec(query, status, aiAnalysis, errorMsg, messageID)
	return err
}

// GetActiveAccounts retrieves all active accounts
func (db *DB) GetActiveAccounts() ([]*Account, error) {
	query := `
		SELECT id, phone_number, display_name, session_data, status,
		       last_connected_at, created_at, updated_at
		FROM accounts 
		WHERE status = 'active'
		ORDER BY last_connected_at DESC
	`
	
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var accounts []*Account
	for rows.Next() {
		account := &Account{}
		err := rows.Scan(
			&account.ID,
			&account.PhoneNumber,
			&account.DisplayName,
			&account.SessionData,
			&account.Status,
			&account.LastConnectedAt,
			&account.CreatedAt,
			&account.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		accounts = append(accounts, account)
	}
	
	return accounts, nil
}

// GetPendingMessages retrieves messages that need AI processing
func (db *DB) GetPendingMessages(limit int) ([]*Message, error) {
	query := `
		SELECT id, whatsapp_message_id, account_id, group_jid, group_name,
		       sender_jid, sender_name, content, message_type, processing_status,
		       ai_analysis, error_message, message_timestamp, received_at,
		       processed_at, created_at, updated_at
		FROM messages 
		WHERE processing_status = 'pending'
		ORDER BY received_at ASC
		LIMIT $1
	`
	
	rows, err := db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var messages []*Message
	for rows.Next() {
		msg := &Message{}
		err := rows.Scan(
			&msg.ID,
			&msg.WhatsAppMessageID,
			&msg.AccountID,
			&msg.GroupJID,
			&msg.GroupName,
			&msg.SenderJID,
			&msg.SenderName,
			&msg.Content,
			&msg.MessageType,
			&msg.ProcessingStatus,
			&msg.AIAnalysis,
			&msg.ErrorMessage,
			&msg.MessageTimestamp,
			&msg.ReceivedAt,
			&msg.ProcessedAt,
			&msg.CreatedAt,
			&msg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		messages = append(messages, msg)
	}
	
	return messages, nil
}
