package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
)

// TestRunner runs comprehensive tests for the MediBridge WhatsApp service
func main() {
	fmt.Println("🧪 Running MediBridge WhatsApp Service Tests")
	fmt.Println("=" * 50)

	tests := []TestSuite{
		{
			Name:        "Unit Tests",
			Command:     "go test -short ./...",
			Description: "Running unit tests for all packages",
		},
		{
			Name:        "Integration Tests",
			Command:     "go test -run Integration ./...",
			Description: "Running integration tests",
		},
		{
			Name:        "Coverage Tests",
			Command:     "go test -coverprofile=coverage.out ./...",
			Description: "Running tests with coverage analysis",
		},
		{
			Name:        "Race Detection",
			Command:     "go test -race ./...",
			Description: "Running tests with race condition detection",
		},
		{
			Name:        "Benchmark Tests",
			Command:     "go test -bench=. -benchmem ./...",
			Description: "Running benchmark tests",
		},
		{
			Name:        "Matching Engine Tests",
			Command:     "go test -v ./internal/matching/...",
			Description: "Testing matching engine specifically",
		},
		{
			Name:        "AI Processor Tests",
			Command:     "go test -v ./internal/ai/...",
			Description: "Testing AI processor specifically",
		},
		{
			Name:        "API Handler Tests",
			Command:     "go test -v ./internal/api/...",
			Description: "Testing API handlers specifically",
		},
		{
			Name:        "Database Tests",
			Command:     "go test -v ./internal/database/...",
			Description: "Testing database layer specifically",
		},
		{
			Name:        "Performance Tests",
			Command:     "go test -v ./internal/performance/...",
			Description: "Testing performance optimizer specifically",
		},
		{
			Name:        "WhatsApp Tests",
			Command:     "go test -v ./internal/whatsapp/...",
			Description: "Testing WhatsApp manager specifically",
		},
	}

	var results []TestResult
	totalTests := len(tests)
	passedTests := 0

	for i, test := range tests {
		fmt.Printf("\n[%d/%d] %s\n", i+1, totalTests, test.Name)
		fmt.Printf("📝 %s\n", test.Description)
		fmt.Printf("🔧 Command: %s\n", test.Command)

		result := runTest(test)
		results = append(results, result)

		if result.Success {
			fmt.Printf("✅ %s PASSED\n", test.Name)
			passedTests++
		} else {
			fmt.Printf("❌ %s FAILED\n", test.Name)
			if result.Error != "" {
				fmt.Printf("   Error: %s\n", result.Error)
			}
		}
	}

	// Print summary
	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	fmt.Printf("📊 TEST SUMMARY\n")
	fmt.Printf("Total Tests: %d\n", totalTests)
	fmt.Printf("Passed: %d\n", passedTests)
	fmt.Printf("Failed: %d\n", totalTests-passedTests)
	fmt.Printf("Success Rate: %.1f%%\n", float64(passedTests)/float64(totalTests)*100)

	// Print detailed results
	fmt.Printf("\n📋 DETAILED RESULTS:\n")
	for _, result := range results {
		status := "✅ PASS"
		if !result.Success {
			status = "❌ FAIL"
		}
		fmt.Printf("  %s - %s\n", status, result.TestName)
		if !result.Success && result.Error != "" {
			fmt.Printf("    └─ %s\n", result.Error)
		}
	}

	// Generate coverage report if coverage test passed
	for _, result := range results {
		if result.TestName == "Coverage Tests" && result.Success {
			fmt.Printf("\n📈 Generating coverage report...\n")
			if err := exec.Command("go", "tool", "cover", "-html=coverage.out", "-o", "coverage.html").Run(); err == nil {
				fmt.Printf("✅ Coverage report generated: coverage.html\n")
			} else {
				fmt.Printf("❌ Failed to generate coverage report\n")
			}
			break
		}
	}

	// Exit with appropriate code
	if passedTests == totalTests {
		fmt.Printf("\n🎉 All tests passed! The codebase is ready for production.\n")
		os.Exit(0)
	} else {
		fmt.Printf("\n⚠️  Some tests failed. Please review and fix the issues.\n")
		os.Exit(1)
	}
}

type TestSuite struct {
	Name        string
	Command     string
	Description string
}

type TestResult struct {
	TestName string
	Success  bool
	Error    string
	Output   string
}

func runTest(test TestSuite) TestResult {
	parts := strings.Fields(test.Command)
	cmd := exec.Command(parts[0], parts[1:]...)

	output, err := cmd.CombinedOutput()
	
	result := TestResult{
		TestName: test.Name,
		Success:  err == nil,
		Output:   string(output),
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result
}

// Additional helper functions for test analysis
func analyzeTestOutput(output string) map[string]interface{} {
	analysis := make(map[string]interface{})
	
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.Contains(line, "PASS") {
			analysis["status"] = "PASS"
		} else if strings.Contains(line, "FAIL") {
			analysis["status"] = "FAIL"
		}
		
		if strings.Contains(line, "coverage:") {
			analysis["coverage"] = line
		}
		
		if strings.Contains(line, "BenchmarkResult") {
			analysis["benchmark"] = line
		}
	}
	
	return analysis
}

// Test quality metrics
func calculateTestQuality(results []TestResult) map[string]float64 {
	metrics := make(map[string]float64)
	
	totalTests := len(results)
	passedTests := 0
	
	for _, result := range results {
		if result.Success {
			passedTests++
		}
	}
	
	metrics["pass_rate"] = float64(passedTests) / float64(totalTests) * 100
	metrics["total_tests"] = float64(totalTests)
	metrics["passed_tests"] = float64(passedTests)
	metrics["failed_tests"] = float64(totalTests - passedTests)
	
	return metrics
}

// Performance benchmark analysis
func analyzeBenchmarks(output string) []map[string]interface{} {
	var benchmarks []map[string]interface{}
	
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "Benchmark") && strings.Contains(line, "ns/op") {
			parts := strings.Fields(line)
			if len(parts) >= 4 {
				benchmark := map[string]interface{}{
					"name":       parts[0],
					"iterations": parts[1],
					"ns_per_op":  parts[2],
				}
				
				if len(parts) >= 6 && strings.Contains(parts[4], "B/op") {
					benchmark["bytes_per_op"] = parts[4]
					benchmark["allocs_per_op"] = parts[5]
				}
				
				benchmarks = append(benchmarks, benchmark)
			}
		}
	}
	
	return benchmarks
}

// Coverage analysis
func analyzeCoverage(output string) map[string]string {
	coverage := make(map[string]string)
	
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.Contains(line, "coverage:") {
			parts := strings.Fields(line)
			for i, part := range parts {
				if part == "coverage:" && i+1 < len(parts) {
					coverage["total"] = parts[i+1]
					break
				}
			}
		}
		
		if strings.Contains(line, "%") && strings.Contains(line, ".go") {
			// Individual file coverage
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				coverage[parts[0]] = parts[len(parts)-1]
			}
		}
	}
	
	return coverage
}
