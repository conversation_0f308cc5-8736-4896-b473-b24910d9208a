D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\libdb_service-068a4d9788f78776.rmeta: db\service\src\lib.rs db\service\src\error.rs db\service\src\accounts.rs db\service\src\medicines.rs Cargo.toml

D:\programming\desktop-apps\MediBridge\src-tauri\target\debug\deps\db_service-068a4d9788f78776.d: db\service\src\lib.rs db\service\src\error.rs db\service\src\accounts.rs db\service\src\medicines.rs Cargo.toml

db\service\src\lib.rs:
db\service\src\error.rs:
db\service\src\accounts.rs:
db\service\src\medicines.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
