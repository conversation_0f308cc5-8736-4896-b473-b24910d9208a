{"rustc": 16591470773350601817, "features": "[\"bigdecimal\", \"chrono\", \"default\", \"macros\", \"mock\", \"postgres-array\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sea-query-binder\", \"serde_json\", \"sqlx\", \"sqlx-dep\", \"sqlx-postgres\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"debug-print\", \"default\", \"ipnetwork\", \"json-array\", \"macros\", \"mock\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"proxy\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sea-orm-internal\", \"sea-query-binder\", \"seaography\", \"serde_json\", \"sqlite-use-returning-for-3_35\", \"sqlx\", \"sqlx-all\", \"sqlx-dep\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"tests-cfg\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 5911612443439219948, "profile": 2241668132362809309, "path": 2072398940214879784, "deps": [[1188017320647144970, "async_stream", false, 8384637549452119572], [2410325386367135049, "sea_orm_macros", false, 7595295785304469908], [3150220818285335163, "url", false, 3569982634382302943], [3302295501534065768, "strum", false, 13313147422330952887], [3525657182790186941, "ouroboros", false, 8940244016122669730], [5986029879202738730, "log", false, 14504179726639124161], [7161281228672193341, "sea_query", false, 6993523247413455490], [7817431159498251116, "sea_query_binder", false, 17345366650115277639], [8319709847752024821, "uuid", false, 12906243286195976698], [8606274917505247608, "tracing", false, 702195515399835879], [9689903380558560274, "serde", false, 1190822165864209379], [9897246384292347999, "chrono", false, 11565035621207823088], [10629569228670356391, "futures_util", false, 5770148779909698394], [10806645703491011684, "thiserror", false, 3797447290076662653], [11946729385090170470, "async_trait", false, 2389517705436373950], [12409575957772518135, "time", false, 6202611441483771785], [14647456484942590313, "bigdecimal", false, 16021671231134990048], [15367738274754116744, "serde_json", false, 13370400617386028636], [16682465660942253309, "rust_decimal", false, 7608462804829237094], [17982831385697850842, "sqlx", false, 3199512019196408804]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-5eb41c20a7d650bf\\dep-lib-sea_orm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}