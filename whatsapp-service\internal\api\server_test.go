package api

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap/zaptest"

	"medibridge-whatsapp/internal/database"
	"medibridge-whatsapp/internal/matching"
	"medibridge-whatsapp/internal/whatsapp"
)

// Mock implementations for testing
type MockWhatsAppManager struct {
	mock.Mock
}

func (m *MockWhatsAppManager) GetClients() []whatsapp.Client {
	args := m.Called()
	return args.Get(0).([]whatsapp.Client)
}

func (m *MockWhatsAppManager) ConnectClient(accountID string) error {
	args := m.Called(accountID)
	return args.Error(0)
}

func (m *MockWhatsAppManager) DisconnectClient(accountID string) error {
	args := m.Called(accountID)
	return args.Error(0)
}

func (m *MockWhatsAppManager) GetQRCode(accountID string) (string, error) {
	args := m.Called(accountID)
	return args.String(0), args.Error(1)
}

func (m *MockWhatsAppManager) Shutdown() {
	m.Called()
}

type MockDB struct {
	mock.Mock
}

func (m *MockDB) Query(query string, args ...interface{}) (*database.Rows, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Rows), mockArgs.Error(1)
}

func (m *MockDB) QueryRow(query string, args ...interface{}) *database.Row {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(*database.Row)
}

func (m *MockDB) Exec(query string, args ...interface{}) (database.Result, error) {
	mockArgs := m.Called(query, args)
	return mockArgs.Get(0).(database.Result), mockArgs.Error(1)
}

type MockAIProcessor struct {
	mock.Mock
}

func (m *MockAIProcessor) ProcessMessage(messageID int) error {
	args := m.Called(messageID)
	return args.Error(0)
}

func (m *MockAIProcessor) Start(ctx context.Context) {
	m.Called(ctx)
}

type MockMatchingService struct {
	mock.Mock
}

func (m *MockMatchingService) GetMatches(limit, offset int, status string) ([]matching.MatchDetails, int, error) {
	args := m.Called(limit, offset, status)
	return args.Get(0).([]matching.MatchDetails), args.Int(1), args.Error(2)
}

func (m *MockMatchingService) GetMatchDetails(matchID int) (*matching.MatchDetails, error) {
	args := m.Called(matchID)
	return args.Get(0).(*matching.MatchDetails), args.Error(1)
}

func (m *MockMatchingService) UpdateMatchStatus(matchID int, status string, notes *string) error {
	args := m.Called(matchID, status, notes)
	return args.Error(0)
}

func (m *MockMatchingService) RunManualMatching(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockMatchingService) GetMatchingStats() (*matching.MatchingStats, error) {
	args := m.Called()
	return args.Get(0).(*matching.MatchingStats), args.Error(1)
}

// MockResult implements database.Result interface for testing
type MockResult struct {
	mock.Mock
}

func (m *MockResult) RowsAffected() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockResult) LastInsertId() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

// MockRow implements database.Row interface for testing
type MockRow struct {
	mock.Mock
}

func (m *MockRow) Scan(dest ...interface{}) error {
	args := m.Called(dest...)
	return args.Error(0)
}

// MockRows implements database.Rows interface for testing
type MockRows struct {
	mock.Mock
}

func (m *MockRows) Next() bool {
	args := m.Called()
	return args.Bool(0)
}

func (m *MockRows) Scan(dest ...interface{}) error {
	args := m.Called(dest...)
	return args.Error(0)
}

func (m *MockRows) Close() error {
	args := m.Called()
	return args.Error(0)
}

func setupTestServer() (*Server, *MockWhatsAppManager, *MockDB, *MockAIProcessor, *MockMatchingService) {
	gin.SetMode(gin.TestMode)

	logger := zaptest.NewLogger(nil)
	mockWAManager := &MockWhatsAppManager{}
	mockDB := &MockDB{}
	mockAIProcessor := &MockAIProcessor{}
	mockMatchingService := &MockMatchingService{}

	server := NewServer(logger, mockWAManager, mockDB, mockAIProcessor, mockMatchingService)

	return server, mockWAManager, mockDB, mockAIProcessor, mockMatchingService
}

func TestHealthCheck(t *testing.T) {
	server, mockWAManager, _, _, _ := setupTestServer()

	// Mock WhatsApp health check
	mockWAManager.On("GetClients").Return([]whatsapp.Client{
		{AccountID: "test", Status: "connected"},
	})

	router := server.SetupRoutes()
	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "ok", response["status"])

	mockWAManager.AssertExpectations(t)
}

func TestGetMatches(t *testing.T) {
	server, _, _, _, mockMatchingService := setupTestServer()

	// Mock matching service response
	mockMatches := []matching.MatchDetails{
		{
			ID:               1,
			RequestMessageID: 1,
			OfferMessageID:   2,
			MedicineID:       1,
			ConfidenceScore:  0.85,
			Status:           "pending",
		},
	}
	mockMatchingService.On("GetMatches", 50, 0, "").Return(mockMatches, 1, nil)

	router := server.SetupRoutes()
	req, _ := http.NewRequest("GET", "/api/v1/matches", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(1), response["total"])

	mockMatchingService.AssertExpectations(t)
}

func TestCreateAccount(t *testing.T) {
	server, _, mockDB, _, _ := setupTestServer()

	// Mock database operations
	mockDB.On("Exec", mock.AnythingOfType("string"), mock.AnythingOfType("string"),
		"+************", "disconnected", mock.AnythingOfType("time.Time"),
		mock.AnythingOfType("time.Time")).Return(&MockResult{}, nil)

	router := server.SetupRoutes()

	requestBody := map[string]interface{}{
		"phone_number": "+************",
	}
	jsonBody, _ := json.Marshal(requestBody)

	req, _ := http.NewRequest("POST", "/api/v1/accounts", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "successfully")
	assert.Equal(t, "+************", response["phone"])

	mockDB.AssertExpectations(t)
}

func TestGetAccount(t *testing.T) {
	server, _, mockDB, _, _ := setupTestServer()

	// Mock database query
	mockRow := &MockRow{}
	mockRow.On("Scan", mock.Anything, mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything).Return(nil).Run(func(args mock.Arguments) {
		// Simulate scanning values
		*args[0].(*string) = "acc123"
		*args[1].(*string) = "+************"
		*args[2].(*string) = "connected"
		*args[3].(**time.Time) = nil
		*args[4].(*time.Time) = time.Now()
		*args[5].(*time.Time) = time.Now()
	})

	mockDB.On("QueryRow", mock.AnythingOfType("string"), "1").Return(mockRow)

	router := server.SetupRoutes()
	req, _ := http.NewRequest("GET", "/api/v1/accounts/1", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "acc123", response["account_id"])

	mockDB.AssertExpectations(t)
}

func TestDeleteAccount(t *testing.T) {
	server, _, mockDB, _, _ := setupTestServer()

	// Mock check query
	mockCheckRow := &MockRow{}
	mockCheckRow.On("Scan", mock.Anything).Return(nil).Run(func(args mock.Arguments) {
		*args[0].(*string) = "acc123"
	})
	mockDB.On("QueryRow", mock.AnythingOfType("string"), "1").Return(mockCheckRow)

	// Mock delete query
	mockResult := &MockResult{}
	mockResult.On("RowsAffected").Return(int64(1), nil)
	mockDB.On("Exec", mock.AnythingOfType("string"), "1").Return(mockResult, nil)

	router := server.SetupRoutes()
	req, _ := http.NewRequest("DELETE", "/api/v1/accounts/1", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "successfully")

	mockDB.AssertExpectations(t)
}

func TestGetMessages(t *testing.T) {
	server, _, mockDB, _, _ := setupTestServer()

	// Mock count query
	mockCountRow := &MockRow{}
	mockCountRow.On("Scan", mock.Anything).Return(nil).Run(func(args mock.Arguments) {
		*args[0].(*int) = 5
	})
	mockDB.On("QueryRow", mock.AnythingOfType("string")).Return(mockCountRow)

	// Mock messages query
	mockRows := &MockRows{}
	mockRows.On("Next").Return(true).Once()
	mockRows.On("Next").Return(false)
	mockRows.On("Scan", mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything).Return(nil).Run(func(args mock.Arguments) {
		// Simulate scanning message values
		*args[0].(*int) = 1
		*args[1].(*string) = "msg123"
		*args[2].(*string) = "acc123"
		*args[3].(*string) = "group123"
		*args[4].(*string) = "Test Group"
		*args[5].(*string) = "sender123"
		*args[6].(*string) = "Test Sender"
		*args[7].(*string) = "Test message"
		*args[8].(**string) = nil
		*args[9].(*string) = "pending"
		*args[10].(**string) = nil
		*args[11].(**string) = nil
		*args[12].(*time.Time) = time.Now()
		*args[13].(*time.Time) = time.Now()
		*args[14].(**time.Time) = nil
		*args[15].(*time.Time) = time.Now()
		*args[16].(*time.Time) = time.Now()
	})
	mockRows.On("Close").Return(nil)

	mockDB.On("Query", mock.AnythingOfType("string"), mock.Anything, mock.Anything).Return(mockRows, nil)

	router := server.SetupRoutes()
	req, _ := http.NewRequest("GET", "/api/v1/messages", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(5), response["total"])

	mockDB.AssertExpectations(t)
}

func TestUpdateMatchStatus(t *testing.T) {
	server, _, _, _, mockMatchingService := setupTestServer()

	// Mock matching service
	mockMatchingService.On("UpdateMatchStatus", 1, "completed", (*string)(nil)).Return(nil)

	router := server.SetupRoutes()

	requestBody := map[string]interface{}{
		"status": "completed",
	}
	jsonBody, _ := json.Marshal(requestBody)

	req, _ := http.NewRequest("PUT", "/api/v1/matches/1/status", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "successfully")

	mockMatchingService.AssertExpectations(t)
}

func TestRunManualMatching(t *testing.T) {
	server, _, _, _, mockMatchingService := setupTestServer()

	// Mock matching service
	mockMatchingService.On("RunManualMatching", mock.AnythingOfType("*context.emptyCtx")).Return(nil)

	router := server.SetupRoutes()
	req, _ := http.NewRequest("POST", "/api/v1/matches/run", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "successfully")

	mockMatchingService.AssertExpectations(t)
}

func TestGetMatchingStats(t *testing.T) {
	server, _, _, _, mockMatchingService := setupTestServer()

	// Mock matching service response
	mockStats := &matching.MatchingStats{
		TotalMatches:      100,
		RecentMatches:     10,
		AverageConfidence: 0.75,
		SuccessRate:       0.85,
		MatchesByStatus:   map[string]int{"pending": 20, "completed": 80},
	}
	mockMatchingService.On("GetMatchingStats").Return(mockStats, nil)

	router := server.SetupRoutes()
	req, _ := http.NewRequest("GET", "/api/v1/matches/stats", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response matching.MatchingStats
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 100, response.TotalMatches)
	assert.Equal(t, 0.75, response.AverageConfidence)

	mockMatchingService.AssertExpectations(t)
}

func TestErrorHandling(t *testing.T) {
	server, _, _, _, mockMatchingService := setupTestServer()

	// Mock service error
	mockMatchingService.On("GetMatches", 50, 0, "").Return([]matching.MatchDetails{}, 0, assert.AnError)

	router := server.SetupRoutes()
	req, _ := http.NewRequest("GET", "/api/v1/matches", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["error"], "Failed")

	mockMatchingService.AssertExpectations(t)
}

// Benchmark tests for API performance
func BenchmarkGetMatches(b *testing.B) {
	server, _, _, _, mockMatchingService := setupTestServer()

	mockMatches := make([]matching.MatchDetails, 50)
	for i := range mockMatches {
		mockMatches[i] = matching.MatchDetails{
			ID:              i + 1,
			ConfidenceScore: 0.8,
			Status:          "pending",
		}
	}

	mockMatchingService.On("GetMatches", 50, 0, "").Return(mockMatches, 50, nil)

	router := server.SetupRoutes()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest("GET", "/api/v1/matches", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

func TestCORSHeaders(t *testing.T) {
	server, _, _, _, _ := setupTestServer()

	router := server.SetupRoutes()
	req, _ := http.NewRequest("OPTIONS", "/api/v1/matches", nil)
	req.Header.Set("Origin", "http://localhost:3000")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNoContent, w.Code)
	assert.Contains(t, w.Header().Get("Access-Control-Allow-Origin"), "*")
}

func TestRateLimiting(t *testing.T) {
	// This would test rate limiting if implemented
	t.Skip("Rate limiting not implemented yet")
}

func TestAuthentication(t *testing.T) {
	// This would test authentication if implemented
	t.Skip("Authentication not implemented yet")
}
